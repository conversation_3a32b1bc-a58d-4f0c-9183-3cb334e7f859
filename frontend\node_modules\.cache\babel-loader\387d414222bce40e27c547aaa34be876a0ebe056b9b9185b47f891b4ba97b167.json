{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\smartcoverage\\\\frontend\\\\src\\\\components\\\\dashboards\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Grid, Card, CardContent, Typography, Box, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Alert } from '@mui/material';\nimport { Dashboard as DashboardIcon, People as PeopleIcon, VideoCall as VideoCallIcon, Analytics as AnalyticsIcon, Settings as SettingsIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, PlayArrow as PlayIcon, Stop as StopIcon } from '@mui/icons-material';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { meetingService } from '../../services/meetingService';\nimport { userService } from '../../services/userService';\nimport { zoomService } from '../../services/zoomService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalMeetings: 0,\n    activeMeetings: 0,\n    totalUsers: 0,\n    totalClients: 0,\n    systemHealth: 'good'\n  });\n  const [meetings, setMeetings] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [createMeetingOpen, setCreateMeetingOpen] = useState(false);\n  const [newMeeting, setNewMeeting] = useState({\n    topic: '',\n    client_name: '',\n    client_industry: '',\n    start_time: '',\n    duration: 60,\n    host_email: (user === null || user === void 0 ? void 0 : user.email) || '',\n    meeting_type: 'client_call'\n  });\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [meetingsData, usersData, statsData] = await Promise.all([meetingService.getMeetings(), userService.getUsers(), meetingService.getStats()]);\n      setMeetings(meetingsData.data || []);\n      setUsers(usersData.data || []);\n      setStats(statsData.data || stats);\n    } catch (error) {\n      console.error('Failed to load dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateMeeting = async () => {\n    try {\n      const meetingData = {\n        ...newMeeting,\n        enable_nlp_analysis: true,\n        settings: {\n          auto_recording: 'cloud',\n          waiting_room: true,\n          mute_upon_entry: true\n        }\n      };\n      await zoomService.createMeetingWithNLP(meetingData);\n      setCreateMeetingOpen(false);\n      setNewMeeting({\n        topic: '',\n        client_name: '',\n        client_industry: '',\n        start_time: '',\n        duration: 60,\n        host_email: (user === null || user === void 0 ? void 0 : user.email) || '',\n        meeting_type: 'client_call'\n      });\n      loadDashboardData();\n    } catch (error) {\n      console.error('Failed to create meeting:', error);\n    }\n  };\n  const handleJoinMeeting = async meetingId => {\n    try {\n      const joinInfo = await zoomService.joinMeeting(meetingId);\n      window.open(joinInfo.join_url, '_blank');\n    } catch (error) {\n      console.error('Failed to join meeting:', error);\n    }\n  };\n  const handleStartMeeting = async meetingId => {\n    try {\n      const startInfo = await zoomService.startMeeting(meetingId);\n      window.open(startInfo.start_url, '_blank');\n    } catch (error) {\n      console.error('Failed to start meeting:', error);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'scheduled':\n        return 'primary';\n      case 'in_progress':\n        return 'success';\n      case 'completed':\n        return 'default';\n      case 'cancelled':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const chartData = {\n    meetingTrends: {\n      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n      datasets: [{\n        label: 'Meetings',\n        data: [12, 19, 15, 25, 22, 8, 5],\n        borderColor: 'rgb(75, 192, 192)',\n        backgroundColor: 'rgba(75, 192, 192, 0.2)',\n        tension: 0.1\n      }]\n    },\n    userRoles: {\n      labels: ['Admins', 'Managers', 'Clients'],\n      datasets: [{\n        data: [users.filter(u => u.role === 'admin').length, users.filter(u => u.role === 'manager').length, users.filter(u => u.role === 'client').length],\n        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']\n      }]\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Admin Dashboard - Complete System Control\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(VideoCallIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Total Meetings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: stats.totalMeetings\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(PlayIcon, {\n                color: \"success\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Active Meetings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: stats.activeMeetings\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(PeopleIcon, {\n                color: \"info\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Total Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: stats.totalUsers\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n                color: \"warning\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"System Health\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: stats.systemHealth,\n                  color: stats.systemHealth === 'good' ? 'success' : 'error',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Meeting Trends (This Week)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              data: chartData.meetingTrends\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"User Distribution\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Doughnut, {\n              data: chartData.userRoles\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Meeting Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 26\n            }, this),\n            onClick: () => setCreateMeetingOpen(true),\n            children: \"Create Meeting\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Topic\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Client\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Date/Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Platform\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: meetings.slice(0, 10).map(meeting => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: meeting.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: meeting.client_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: new Date(meeting.meeting_date).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: meeting.status,\n                    color: getStatusColor(meeting.status),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: meeting.meeting_platform || 'zoom',\n                    variant: \"outlined\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [meeting.status === 'scheduled' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"primary\",\n                      onClick: () => handleStartMeeting(meeting.zoom_meeting_id),\n                      title: \"Start Meeting\",\n                      children: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"success\",\n                      onClick: () => handleJoinMeeting(meeting.zoom_meeting_id),\n                      title: \"Join Meeting\",\n                      children: /*#__PURE__*/_jsxDEV(VideoCallIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    color: \"default\",\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    color: \"error\",\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this)]\n              }, meeting.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"User Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: users.slice(0, 10).map(user => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [user.first_name, \" \", user.last_name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: user.role,\n                    color: \"primary\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: user.department\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: user.is_active ? 'Active' : 'Inactive',\n                    color: user.is_active ? 'success' : 'error',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    color: \"default\",\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    color: \"error\",\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this)]\n              }, user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: createMeetingOpen,\n      onClose: () => setCreateMeetingOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Create New Meeting with AI Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Meeting Topic\",\n              value: newMeeting.topic,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                topic: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Client Name\",\n              value: newMeeting.client_name,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                client_name: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Client Industry\",\n              value: newMeeting.client_industry,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                client_industry: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"datetime-local\",\n              label: \"Start Time\",\n              value: newMeeting.start_time,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                start_time: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"number\",\n              label: \"Duration (minutes)\",\n              value: newMeeting.duration,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                duration: parseInt(e.target.value)\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Meeting Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: newMeeting.meeting_type,\n                onChange: e => setNewMeeting({\n                  ...newMeeting,\n                  meeting_type: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"client_call\",\n                  children: \"Client Call\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"internal\",\n                  children: \"Internal Meeting\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"presentation\",\n                  children: \"Presentation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"training\",\n                  children: \"Training\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              children: \"This meeting will be created with AI-powered real-time analysis including sentiment tracking, automatic transcription, and intelligent insights generation.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setCreateMeetingOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateMeeting,\n          variant: \"contained\",\n          children: \"Create Meeting\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"zqaeOHkJ+QWNiBcfH1rdh4GKl4o=\", false, function () {\n  return [useAuth];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Dashboard", "DashboardIcon", "People", "PeopleIcon", "VideoCall", "VideoCallIcon", "Analytics", "AnalyticsIcon", "Settings", "SettingsIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "PlayArrow", "PlayIcon", "Stop", "StopIcon", "Line", "Bar", "Doughnut", "useAuth", "meetingService", "userService", "zoomService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminDashboard", "_s", "user", "stats", "setStats", "totalMeetings", "activeMeetings", "totalUsers", "totalClients", "systemHealth", "meetings", "setMeetings", "users", "setUsers", "loading", "setLoading", "createMeetingOpen", "setCreateMeetingOpen", "newMeeting", "setNewMeeting", "topic", "client_name", "client_industry", "start_time", "duration", "host_email", "email", "meeting_type", "loadDashboardData", "meetingsData", "usersData", "statsData", "Promise", "all", "getMeetings", "getUsers", "getStats", "data", "error", "console", "handleCreateMeeting", "meetingData", "enable_nlp_analysis", "settings", "auto_recording", "waiting_room", "mute_upon_entry", "createMeetingWithNLP", "handleJoinMeeting", "meetingId", "joinInfo", "joinMeeting", "window", "open", "join_url", "handleStartMeeting", "startInfo", "startMeeting", "start_url", "getStatusColor", "status", "chartData", "meetingTrends", "labels", "datasets", "label", "borderColor", "backgroundColor", "tension", "userRoles", "filter", "u", "role", "length", "sx", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "mb", "item", "xs", "sm", "md", "display", "alignItems", "color", "mr", "size", "justifyContent", "startIcon", "onClick", "component", "slice", "map", "meeting", "subject", "Date", "meeting_date", "toLocaleString", "meeting_platform", "zoom_meeting_id", "title", "id", "first_name", "last_name", "department", "is_active", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "mt", "value", "onChange", "e", "target", "type", "InputLabelProps", "shrink", "parseInt", "severity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/smartcoverage/frontend/src/components/dashboards/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  <PERSON><PERSON><PERSON>,\n  Box,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Alert\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  People as PeopleIcon,\n  VideoCall as VideoCallIcon,\n  Analytics as AnalyticsIcon,\n  Settings as SettingsIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  PlayArrow as PlayIcon,\n  Stop as StopIcon\n} from '@mui/icons-material';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { meetingService } from '../../services/meetingService';\nimport { userService } from '../../services/userService';\nimport { zoomService } from '../../services/zoomService';\n\nconst AdminDashboard = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState({\n    totalMeetings: 0,\n    activeMeetings: 0,\n    totalUsers: 0,\n    totalClients: 0,\n    systemHealth: 'good'\n  });\n  const [meetings, setMeetings] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [createMeetingOpen, setCreateMeetingOpen] = useState(false);\n  const [newMeeting, setNewMeeting] = useState({\n    topic: '',\n    client_name: '',\n    client_industry: '',\n    start_time: '',\n    duration: 60,\n    host_email: user?.email || '',\n    meeting_type: 'client_call'\n  });\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [meetingsData, usersData, statsData] = await Promise.all([\n        meetingService.getMeetings(),\n        userService.getUsers(),\n        meetingService.getStats()\n      ]);\n\n      setMeetings(meetingsData.data || []);\n      setUsers(usersData.data || []);\n      setStats(statsData.data || stats);\n    } catch (error) {\n      console.error('Failed to load dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateMeeting = async () => {\n    try {\n      const meetingData = {\n        ...newMeeting,\n        enable_nlp_analysis: true,\n        settings: {\n          auto_recording: 'cloud',\n          waiting_room: true,\n          mute_upon_entry: true\n        }\n      };\n\n      await zoomService.createMeetingWithNLP(meetingData);\n      setCreateMeetingOpen(false);\n      setNewMeeting({\n        topic: '',\n        client_name: '',\n        client_industry: '',\n        start_time: '',\n        duration: 60,\n        host_email: user?.email || '',\n        meeting_type: 'client_call'\n      });\n      loadDashboardData();\n    } catch (error) {\n      console.error('Failed to create meeting:', error);\n    }\n  };\n\n  const handleJoinMeeting = async (meetingId) => {\n    try {\n      const joinInfo = await zoomService.joinMeeting(meetingId);\n      window.open(joinInfo.join_url, '_blank');\n    } catch (error) {\n      console.error('Failed to join meeting:', error);\n    }\n  };\n\n  const handleStartMeeting = async (meetingId) => {\n    try {\n      const startInfo = await zoomService.startMeeting(meetingId);\n      window.open(startInfo.start_url, '_blank');\n    } catch (error) {\n      console.error('Failed to start meeting:', error);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'scheduled': return 'primary';\n      case 'in_progress': return 'success';\n      case 'completed': return 'default';\n      case 'cancelled': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const chartData = {\n    meetingTrends: {\n      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n      datasets: [{\n        label: 'Meetings',\n        data: [12, 19, 15, 25, 22, 8, 5],\n        borderColor: 'rgb(75, 192, 192)',\n        backgroundColor: 'rgba(75, 192, 192, 0.2)',\n        tension: 0.1\n      }]\n    },\n    userRoles: {\n      labels: ['Admins', 'Managers', 'Clients'],\n      datasets: [{\n        data: [\n          users.filter(u => u.role === 'admin').length,\n          users.filter(u => u.role === 'manager').length,\n          users.filter(u => u.role === 'client').length\n        ],\n        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']\n      }]\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Admin Dashboard - Complete System Control\n      </Typography>\n\n      {/* Stats Cards */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <VideoCallIcon color=\"primary\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Total Meetings\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {stats.totalMeetings}\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <PlayIcon color=\"success\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Active Meetings\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {stats.activeMeetings}\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <PeopleIcon color=\"info\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Total Users\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {stats.totalUsers}\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <AnalyticsIcon color=\"warning\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    System Health\n                  </Typography>\n                  <Chip\n                    label={stats.systemHealth}\n                    color={stats.systemHealth === 'good' ? 'success' : 'error'}\n                    size=\"small\"\n                  />\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Charts */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} md={8}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Meeting Trends (This Week)\n              </Typography>\n              <Line data={chartData.meetingTrends} />\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={4}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                User Distribution\n              </Typography>\n              <Doughnut data={chartData.userRoles} />\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Meeting Management */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n            <Typography variant=\"h6\">\n              Meeting Management\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={() => setCreateMeetingOpen(true)}\n            >\n              Create Meeting\n            </Button>\n          </Box>\n\n          <TableContainer component={Paper}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Topic</TableCell>\n                  <TableCell>Client</TableCell>\n                  <TableCell>Date/Time</TableCell>\n                  <TableCell>Status</TableCell>\n                  <TableCell>Platform</TableCell>\n                  <TableCell>Actions</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {meetings.slice(0, 10).map((meeting) => (\n                  <TableRow key={meeting.id}>\n                    <TableCell>{meeting.subject}</TableCell>\n                    <TableCell>{meeting.client_name}</TableCell>\n                    <TableCell>\n                      {new Date(meeting.meeting_date).toLocaleString()}\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={meeting.status}\n                        color={getStatusColor(meeting.status)}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={meeting.meeting_platform || 'zoom'}\n                        variant=\"outlined\"\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {meeting.status === 'scheduled' && (\n                        <>\n                          <IconButton\n                            size=\"small\"\n                            color=\"primary\"\n                            onClick={() => handleStartMeeting(meeting.zoom_meeting_id)}\n                            title=\"Start Meeting\"\n                          >\n                            <PlayIcon />\n                          </IconButton>\n                          <IconButton\n                            size=\"small\"\n                            color=\"success\"\n                            onClick={() => handleJoinMeeting(meeting.zoom_meeting_id)}\n                            title=\"Join Meeting\"\n                          >\n                            <VideoCallIcon />\n                          </IconButton>\n                        </>\n                      )}\n                      <IconButton size=\"small\" color=\"default\">\n                        <EditIcon />\n                      </IconButton>\n                      <IconButton size=\"small\" color=\"error\">\n                        <DeleteIcon />\n                      </IconButton>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </CardContent>\n      </Card>\n\n      {/* User Management */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            User Management\n          </Typography>\n          <TableContainer component={Paper}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Name</TableCell>\n                  <TableCell>Email</TableCell>\n                  <TableCell>Role</TableCell>\n                  <TableCell>Department</TableCell>\n                  <TableCell>Status</TableCell>\n                  <TableCell>Actions</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {users.slice(0, 10).map((user) => (\n                  <TableRow key={user.id}>\n                    <TableCell>{user.first_name} {user.last_name}</TableCell>\n                    <TableCell>{user.email}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={user.role}\n                        color=\"primary\"\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>{user.department}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={user.is_active ? 'Active' : 'Inactive'}\n                        color={user.is_active ? 'success' : 'error'}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <IconButton size=\"small\" color=\"default\">\n                        <EditIcon />\n                      </IconButton>\n                      <IconButton size=\"small\" color=\"error\">\n                        <DeleteIcon />\n                      </IconButton>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </CardContent>\n      </Card>\n\n      {/* Create Meeting Dialog */}\n      <Dialog open={createMeetingOpen} onClose={() => setCreateMeetingOpen(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>Create New Meeting with AI Analysis</DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Meeting Topic\"\n                value={newMeeting.topic}\n                onChange={(e) => setNewMeeting({...newMeeting, topic: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                label=\"Client Name\"\n                value={newMeeting.client_name}\n                onChange={(e) => setNewMeeting({...newMeeting, client_name: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                label=\"Client Industry\"\n                value={newMeeting.client_industry}\n                onChange={(e) => setNewMeeting({...newMeeting, client_industry: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                type=\"datetime-local\"\n                label=\"Start Time\"\n                value={newMeeting.start_time}\n                onChange={(e) => setNewMeeting({...newMeeting, start_time: e.target.value})}\n                InputLabelProps={{ shrink: true }}\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                type=\"number\"\n                label=\"Duration (minutes)\"\n                value={newMeeting.duration}\n                onChange={(e) => setNewMeeting({...newMeeting, duration: parseInt(e.target.value)})}\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <FormControl fullWidth>\n                <InputLabel>Meeting Type</InputLabel>\n                <Select\n                  value={newMeeting.meeting_type}\n                  onChange={(e) => setNewMeeting({...newMeeting, meeting_type: e.target.value})}\n                >\n                  <MenuItem value=\"client_call\">Client Call</MenuItem>\n                  <MenuItem value=\"internal\">Internal Meeting</MenuItem>\n                  <MenuItem value=\"presentation\">Presentation</MenuItem>\n                  <MenuItem value=\"training\">Training</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12}>\n              <Alert severity=\"info\">\n                This meeting will be created with AI-powered real-time analysis including sentiment tracking,\n                automatic transcription, and intelligent insights generation.\n              </Alert>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setCreateMeetingOpen(false)}>Cancel</Button>\n          <Button onClick={handleCreateMeeting} variant=\"contained\">\n            Create Meeting\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,QAAQ,EACrBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AACrD,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAG9D,QAAQ,CAAC;IACjC+D,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsE,KAAK,EAAEC,QAAQ,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC;IAC3C8E,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,KAAK,KAAI,EAAE;IAC7BC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFpF,SAAS,CAAC,MAAM;IACdqF,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACc,YAAY,EAAEC,SAAS,EAAEC,SAAS,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC7DxC,cAAc,CAACyC,WAAW,CAAC,CAAC,EAC5BxC,WAAW,CAACyC,QAAQ,CAAC,CAAC,EACtB1C,cAAc,CAAC2C,QAAQ,CAAC,CAAC,CAC1B,CAAC;MAEFzB,WAAW,CAACkB,YAAY,CAACQ,IAAI,IAAI,EAAE,CAAC;MACpCxB,QAAQ,CAACiB,SAAS,CAACO,IAAI,IAAI,EAAE,CAAC;MAC9BjC,QAAQ,CAAC2B,SAAS,CAACM,IAAI,IAAIlC,KAAK,CAAC;IACnC,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,WAAW,GAAG;QAClB,GAAGvB,UAAU;QACbwB,mBAAmB,EAAE,IAAI;QACzBC,QAAQ,EAAE;UACRC,cAAc,EAAE,OAAO;UACvBC,YAAY,EAAE,IAAI;UAClBC,eAAe,EAAE;QACnB;MACF,CAAC;MAED,MAAMnD,WAAW,CAACoD,oBAAoB,CAACN,WAAW,CAAC;MACnDxB,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,aAAa,CAAC;QACZC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,KAAK,KAAI,EAAE;QAC7BC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFC,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMU,iBAAiB,GAAG,MAAOC,SAAS,IAAK;IAC7C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMvD,WAAW,CAACwD,WAAW,CAACF,SAAS,CAAC;MACzDG,MAAM,CAACC,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE,QAAQ,CAAC;IAC1C,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMiB,kBAAkB,GAAG,MAAON,SAAS,IAAK;IAC9C,IAAI;MACF,MAAMO,SAAS,GAAG,MAAM7D,WAAW,CAAC8D,YAAY,CAACR,SAAS,CAAC;MAC3DG,MAAM,CAACC,IAAI,CAACG,SAAS,CAACE,SAAS,EAAE,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMqB,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,SAAS,GAAG;IAChBC,aAAa,EAAE;MACbC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MACzDC,QAAQ,EAAE,CAAC;QACTC,KAAK,EAAE,UAAU;QACjB5B,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAChC6B,WAAW,EAAE,mBAAmB;QAChCC,eAAe,EAAE,yBAAyB;QAC1CC,OAAO,EAAE;MACX,CAAC;IACH,CAAC;IACDC,SAAS,EAAE;MACTN,MAAM,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;MACzCC,QAAQ,EAAE,CAAC;QACT3B,IAAI,EAAE,CACJzB,KAAK,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,OAAO,CAAC,CAACC,MAAM,EAC5C7D,KAAK,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC,CAACC,MAAM,EAC9C7D,KAAK,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,QAAQ,CAAC,CAACC,MAAM,CAC9C;QACDN,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;MACnD,CAAC;IACH;EACF,CAAC;EAED,oBACEtE,OAAA,CAACjD,GAAG;IAAC8H,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChB/E,OAAA,CAAClD,UAAU;MAACkI,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbrF,OAAA,CAACrD,IAAI;MAAC2I,SAAS;MAACC,OAAO,EAAE,CAAE;MAACV,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACxC/E,OAAA,CAACrD,IAAI;QAAC8I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,eAC9B/E,OAAA,CAACpD,IAAI;UAAAmI,QAAA,eACH/E,OAAA,CAACnD,WAAW;YAAAkI,QAAA,eACV/E,OAAA,CAACjD,GAAG;cAAC8I,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAf,QAAA,gBACrC/E,OAAA,CAACvB,aAAa;gBAACsH,KAAK,EAAC,SAAS;gBAAClB,EAAE,EAAE;kBAAEmB,EAAE,EAAE;gBAAE;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDrF,OAAA,CAACjD,GAAG;gBAAAgI,QAAA,gBACF/E,OAAA,CAAClD,UAAU;kBAACiJ,KAAK,EAAC,eAAe;kBAACd,YAAY;kBAAAF,QAAA,EAAC;gBAE/C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrF,OAAA,CAAClD,UAAU;kBAACkI,OAAO,EAAC,IAAI;kBAAAD,QAAA,EACrBzE,KAAK,CAACE;gBAAa;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPrF,OAAA,CAACrD,IAAI;QAAC8I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,eAC9B/E,OAAA,CAACpD,IAAI;UAAAmI,QAAA,eACH/E,OAAA,CAACnD,WAAW;YAAAkI,QAAA,eACV/E,OAAA,CAACjD,GAAG;cAAC8I,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAf,QAAA,gBACrC/E,OAAA,CAACX,QAAQ;gBAAC0G,KAAK,EAAC,SAAS;gBAAClB,EAAE,EAAE;kBAAEmB,EAAE,EAAE;gBAAE;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CrF,OAAA,CAACjD,GAAG;gBAAAgI,QAAA,gBACF/E,OAAA,CAAClD,UAAU;kBAACiJ,KAAK,EAAC,eAAe;kBAACd,YAAY;kBAAAF,QAAA,EAAC;gBAE/C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrF,OAAA,CAAClD,UAAU;kBAACkI,OAAO,EAAC,IAAI;kBAAAD,QAAA,EACrBzE,KAAK,CAACG;gBAAc;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPrF,OAAA,CAACrD,IAAI;QAAC8I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,eAC9B/E,OAAA,CAACpD,IAAI;UAAAmI,QAAA,eACH/E,OAAA,CAACnD,WAAW;YAAAkI,QAAA,eACV/E,OAAA,CAACjD,GAAG;cAAC8I,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAf,QAAA,gBACrC/E,OAAA,CAACzB,UAAU;gBAACwH,KAAK,EAAC,MAAM;gBAAClB,EAAE,EAAE;kBAAEmB,EAAE,EAAE;gBAAE;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1CrF,OAAA,CAACjD,GAAG;gBAAAgI,QAAA,gBACF/E,OAAA,CAAClD,UAAU;kBAACiJ,KAAK,EAAC,eAAe;kBAACd,YAAY;kBAAAF,QAAA,EAAC;gBAE/C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrF,OAAA,CAAClD,UAAU;kBAACkI,OAAO,EAAC,IAAI;kBAAAD,QAAA,EACrBzE,KAAK,CAACI;gBAAU;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPrF,OAAA,CAACrD,IAAI;QAAC8I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,eAC9B/E,OAAA,CAACpD,IAAI;UAAAmI,QAAA,eACH/E,OAAA,CAACnD,WAAW;YAAAkI,QAAA,eACV/E,OAAA,CAACjD,GAAG;cAAC8I,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAf,QAAA,gBACrC/E,OAAA,CAACrB,aAAa;gBAACoH,KAAK,EAAC,SAAS;gBAAClB,EAAE,EAAE;kBAAEmB,EAAE,EAAE;gBAAE;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDrF,OAAA,CAACjD,GAAG;gBAAAgI,QAAA,gBACF/E,OAAA,CAAClD,UAAU;kBAACiJ,KAAK,EAAC,eAAe;kBAACd,YAAY;kBAAAF,QAAA,EAAC;gBAE/C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrF,OAAA,CAACxC,IAAI;kBACH4G,KAAK,EAAE9D,KAAK,CAACM,YAAa;kBAC1BmF,KAAK,EAAEzF,KAAK,CAACM,YAAY,KAAK,MAAM,GAAG,SAAS,GAAG,OAAQ;kBAC3DqF,IAAI,EAAC;gBAAO;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPrF,OAAA,CAACrD,IAAI;MAAC2I,SAAS;MAACC,OAAO,EAAE,CAAE;MAACV,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACxC/E,OAAA,CAACrD,IAAI;QAAC8I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAb,QAAA,eACvB/E,OAAA,CAACpD,IAAI;UAAAmI,QAAA,eACH/E,OAAA,CAACnD,WAAW;YAAAkI,QAAA,gBACV/E,OAAA,CAAClD,UAAU;cAACkI,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAF,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrF,OAAA,CAACR,IAAI;cAACgD,IAAI,EAAEwB,SAAS,CAACC;YAAc;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPrF,OAAA,CAACrD,IAAI;QAAC8I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAb,QAAA,eACvB/E,OAAA,CAACpD,IAAI;UAAAmI,QAAA,eACH/E,OAAA,CAACnD,WAAW;YAAAkI,QAAA,gBACV/E,OAAA,CAAClD,UAAU;cAACkI,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAF,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrF,OAAA,CAACN,QAAQ;cAAC8C,IAAI,EAAEwB,SAAS,CAACQ;YAAU;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPrF,OAAA,CAACpD,IAAI;MAACiI,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,eAClB/E,OAAA,CAACnD,WAAW;QAAAkI,QAAA,gBACV/E,OAAA,CAACjD,GAAG;UAAC8I,OAAO,EAAC,MAAM;UAACK,cAAc,EAAC,eAAe;UAACJ,UAAU,EAAC,QAAQ;UAACN,EAAE,EAAE,CAAE;UAAAT,QAAA,gBAC3E/E,OAAA,CAAClD,UAAU;YAACkI,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAEzB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbrF,OAAA,CAAChD,MAAM;YACLgI,OAAO,EAAC,WAAW;YACnBmB,SAAS,eAAEnG,OAAA,CAACjB,OAAO;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBe,OAAO,EAAEA,CAAA,KAAMhF,oBAAoB,CAAC,IAAI,CAAE;YAAA2D,QAAA,EAC3C;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENrF,OAAA,CAAC5C,cAAc;UAACiJ,SAAS,EAAE9I,KAAM;UAAAwH,QAAA,eAC/B/E,OAAA,CAAC/C,KAAK;YAAA8H,QAAA,gBACJ/E,OAAA,CAAC3C,SAAS;cAAA0H,QAAA,eACR/E,OAAA,CAAC1C,QAAQ;gBAAAyH,QAAA,gBACP/E,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5BrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7BrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChCrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7BrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/BrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZrF,OAAA,CAAC9C,SAAS;cAAA6H,QAAA,EACPlE,QAAQ,CAACyF,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAEC,OAAO,iBACjCxG,OAAA,CAAC1C,QAAQ;gBAAAyH,QAAA,gBACP/E,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAEyB,OAAO,CAACC;gBAAO;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAEyB,OAAO,CAAChF;gBAAW;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5CrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EACP,IAAI2B,IAAI,CAACF,OAAO,CAACG,YAAY,CAAC,CAACC,cAAc,CAAC;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACZrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,eACR/E,OAAA,CAACxC,IAAI;oBACH4G,KAAK,EAAEoC,OAAO,CAACzC,MAAO;oBACtBgC,KAAK,EAAEjC,cAAc,CAAC0C,OAAO,CAACzC,MAAM,CAAE;oBACtCkC,IAAI,EAAC;kBAAO;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,eACR/E,OAAA,CAACxC,IAAI;oBACH4G,KAAK,EAAEoC,OAAO,CAACK,gBAAgB,IAAI,MAAO;oBAC1C7B,OAAO,EAAC,UAAU;oBAClBiB,IAAI,EAAC;kBAAO;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,GACPyB,OAAO,CAACzC,MAAM,KAAK,WAAW,iBAC7B/D,OAAA,CAAAE,SAAA;oBAAA6E,QAAA,gBACE/E,OAAA,CAACvC,UAAU;sBACTwI,IAAI,EAAC,OAAO;sBACZF,KAAK,EAAC,SAAS;sBACfK,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAC8C,OAAO,CAACM,eAAe,CAAE;sBAC3DC,KAAK,EAAC,eAAe;sBAAAhC,QAAA,eAErB/E,OAAA,CAACX,QAAQ;wBAAA6F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACbrF,OAAA,CAACvC,UAAU;sBACTwI,IAAI,EAAC,OAAO;sBACZF,KAAK,EAAC,SAAS;sBACfK,OAAO,EAAEA,CAAA,KAAMjD,iBAAiB,CAACqD,OAAO,CAACM,eAAe,CAAE;sBAC1DC,KAAK,EAAC,cAAc;sBAAAhC,QAAA,eAEpB/E,OAAA,CAACvB,aAAa;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA,eACb,CACH,eACDrF,OAAA,CAACvC,UAAU;oBAACwI,IAAI,EAAC,OAAO;oBAACF,KAAK,EAAC,SAAS;oBAAAhB,QAAA,eACtC/E,OAAA,CAACf,QAAQ;sBAAAiG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACbrF,OAAA,CAACvC,UAAU;oBAACwI,IAAI,EAAC,OAAO;oBAACF,KAAK,EAAC,OAAO;oBAAAhB,QAAA,eACpC/E,OAAA,CAACb,UAAU;sBAAA+F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GA/CCmB,OAAO,CAACQ,EAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgDf,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPrF,OAAA,CAACpD,IAAI;MAAAmI,QAAA,eACH/E,OAAA,CAACnD,WAAW;QAAAkI,QAAA,gBACV/E,OAAA,CAAClD,UAAU;UAACkI,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrF,OAAA,CAAC5C,cAAc;UAACiJ,SAAS,EAAE9I,KAAM;UAAAwH,QAAA,eAC/B/E,OAAA,CAAC/C,KAAK;YAAA8H,QAAA,gBACJ/E,OAAA,CAAC3C,SAAS;cAAA0H,QAAA,eACR/E,OAAA,CAAC1C,QAAQ;gBAAAyH,QAAA,gBACP/E,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC3BrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5BrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC3BrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjCrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7BrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZrF,OAAA,CAAC9C,SAAS;cAAA6H,QAAA,EACPhE,KAAK,CAACuF,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAElG,IAAI,iBAC3BL,OAAA,CAAC1C,QAAQ;gBAAAyH,QAAA,gBACP/E,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,GAAE1E,IAAI,CAAC4G,UAAU,EAAC,GAAC,EAAC5G,IAAI,CAAC6G,SAAS;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzDrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAE1E,IAAI,CAACwB;gBAAK;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,eACR/E,OAAA,CAACxC,IAAI;oBACH4G,KAAK,EAAE/D,IAAI,CAACsE,IAAK;oBACjBoB,KAAK,EAAC,SAAS;oBACfE,IAAI,EAAC;kBAAO;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,EAAE1E,IAAI,CAAC8G;gBAAU;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,eACR/E,OAAA,CAACxC,IAAI;oBACH4G,KAAK,EAAE/D,IAAI,CAAC+G,SAAS,GAAG,QAAQ,GAAG,UAAW;oBAC9CrB,KAAK,EAAE1F,IAAI,CAAC+G,SAAS,GAAG,SAAS,GAAG,OAAQ;oBAC5CnB,IAAI,EAAC;kBAAO;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZrF,OAAA,CAAC7C,SAAS;kBAAA4H,QAAA,gBACR/E,OAAA,CAACvC,UAAU;oBAACwI,IAAI,EAAC,OAAO;oBAACF,KAAK,EAAC,SAAS;oBAAAhB,QAAA,eACtC/E,OAAA,CAACf,QAAQ;sBAAAiG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACbrF,OAAA,CAACvC,UAAU;oBAACwI,IAAI,EAAC,OAAO;oBAACF,KAAK,EAAC,OAAO;oBAAAhB,QAAA,eACpC/E,OAAA,CAACb,UAAU;sBAAA+F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAzBChF,IAAI,CAAC2G,EAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BZ,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPrF,OAAA,CAACtC,MAAM;MAAC8F,IAAI,EAAErC,iBAAkB;MAACkG,OAAO,EAAEA,CAAA,KAAMjG,oBAAoB,CAAC,KAAK,CAAE;MAACkG,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAxC,QAAA,gBAClG/E,OAAA,CAACrC,WAAW;QAAAoH,QAAA,EAAC;MAAmC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9DrF,OAAA,CAACpC,aAAa;QAAAmH,QAAA,eACZ/E,OAAA,CAACrD,IAAI;UAAC2I,SAAS;UAACC,OAAO,EAAE,CAAE;UAACV,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAAzC,QAAA,gBACxC/E,OAAA,CAACrD,IAAI;YAAC8I,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAX,QAAA,eAChB/E,OAAA,CAAClC,SAAS;cACRyJ,SAAS;cACTnD,KAAK,EAAC,eAAe;cACrBqD,KAAK,EAAEpG,UAAU,CAACE,KAAM;cACxBmG,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEE,KAAK,EAAEoG,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrF,OAAA,CAACrD,IAAI;YAAC8I,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAZ,QAAA,eACvB/E,OAAA,CAAClC,SAAS;cACRyJ,SAAS;cACTnD,KAAK,EAAC,aAAa;cACnBqD,KAAK,EAAEpG,UAAU,CAACG,WAAY;cAC9BkG,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEG,WAAW,EAAEmG,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrF,OAAA,CAACrD,IAAI;YAAC8I,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAZ,QAAA,eACvB/E,OAAA,CAAClC,SAAS;cACRyJ,SAAS;cACTnD,KAAK,EAAC,iBAAiB;cACvBqD,KAAK,EAAEpG,UAAU,CAACI,eAAgB;cAClCiG,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEI,eAAe,EAAEkG,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrF,OAAA,CAACrD,IAAI;YAAC8I,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAZ,QAAA,eACvB/E,OAAA,CAAClC,SAAS;cACRyJ,SAAS;cACTM,IAAI,EAAC,gBAAgB;cACrBzD,KAAK,EAAC,YAAY;cAClBqD,KAAK,EAAEpG,UAAU,CAACK,UAAW;cAC7BgG,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEK,UAAU,EAAEiG,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC5EK,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrF,OAAA,CAACrD,IAAI;YAAC8I,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAZ,QAAA,eACvB/E,OAAA,CAAClC,SAAS;cACRyJ,SAAS;cACTM,IAAI,EAAC,QAAQ;cACbzD,KAAK,EAAC,oBAAoB;cAC1BqD,KAAK,EAAEpG,UAAU,CAACM,QAAS;cAC3B+F,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEM,QAAQ,EAAEqG,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACH,KAAK;cAAC,CAAC;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrF,OAAA,CAACrD,IAAI;YAAC8I,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAZ,QAAA,eACvB/E,OAAA,CAACjC,WAAW;cAACwJ,SAAS;cAAAxC,QAAA,gBACpB/E,OAAA,CAAChC,UAAU;gBAAA+G,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCrF,OAAA,CAAC/B,MAAM;gBACLwJ,KAAK,EAAEpG,UAAU,CAACS,YAAa;gBAC/B4F,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAAC;kBAAC,GAAGD,UAAU;kBAAES,YAAY,EAAE6F,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBAAA1C,QAAA,gBAE9E/E,OAAA,CAAC9B,QAAQ;kBAACuJ,KAAK,EAAC,aAAa;kBAAA1C,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpDrF,OAAA,CAAC9B,QAAQ;kBAACuJ,KAAK,EAAC,UAAU;kBAAA1C,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtDrF,OAAA,CAAC9B,QAAQ;kBAACuJ,KAAK,EAAC,cAAc;kBAAA1C,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtDrF,OAAA,CAAC9B,QAAQ;kBAACuJ,KAAK,EAAC,UAAU;kBAAA1C,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPrF,OAAA,CAACrD,IAAI;YAAC8I,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAX,QAAA,eAChB/E,OAAA,CAAC7B,KAAK;cAAC8J,QAAQ,EAAC,MAAM;cAAAlD,QAAA,EAAC;YAGvB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBrF,OAAA,CAACnC,aAAa;QAAAkH,QAAA,gBACZ/E,OAAA,CAAChD,MAAM;UAACoJ,OAAO,EAAEA,CAAA,KAAMhF,oBAAoB,CAAC,KAAK,CAAE;UAAA2D,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnErF,OAAA,CAAChD,MAAM;UAACoJ,OAAO,EAAEzD,mBAAoB;UAACqC,OAAO,EAAC,WAAW;UAAAD,QAAA,EAAC;QAE1D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACjF,EAAA,CAlcID,cAAc;EAAA,QACDR,OAAO;AAAA;AAAAuI,EAAA,GADpB/H,cAAc;AAocpB,eAAeA,cAAc;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}