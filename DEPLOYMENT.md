# SmartConverge Deployment Guide

## 🚀 Production Deployment

This guide covers deploying SmartConverge in production environments with high availability, security, and performance considerations.

## 📋 Prerequisites

### System Requirements
- **CPU**: 8+ cores (16+ recommended for ML workloads)
- **RAM**: 16GB+ (32GB+ recommended)
- **Storage**: 100GB+ SSD
- **Network**: High-bandwidth internet connection
- **OS**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+

### Software Requirements
- **Node.js**: 18.x LTS
- **Python**: 3.8+
- **PostgreSQL**: 13+
- **MongoDB**: 5.0+
- **Redis**: 6.0+
- **Nginx**: 1.18+
- **PM2**: Latest
- **Docker**: 20.10+ (optional)

## 🏗️ Infrastructure Setup

### 1. Server Configuration

#### Single Server Deployment
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python and pip
sudo apt install python3 python3-pip python3-venv -y

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list
sudo apt update
sudo apt install mongodb-org -y

# Install Redis
sudo apt install redis-server -y

# Install Nginx
sudo apt install nginx -y

# Install PM2
sudo npm install -g pm2
```

#### Multi-Server Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   App Servers   │    │   Database      │
│   (Nginx/HAProxy)│    │   (Node.js/     │    │   Cluster       │
│                 │    │    Python)      │    │   (PG/Mongo/    │
│                 │    │                 │    │    Redis)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. Database Setup

#### PostgreSQL Configuration
```bash
# Create database and user
sudo -u postgres psql << EOF
CREATE DATABASE smartcoverage_prod;
CREATE USER smartcoverage_prod WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE smartcoverage_prod TO smartcoverage_prod;
ALTER USER smartcoverage_prod CREATEDB;
\q
EOF

# Configure PostgreSQL for production
sudo nano /etc/postgresql/13/main/postgresql.conf
```

```conf
# postgresql.conf optimizations
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB
```

#### MongoDB Configuration
```bash
# Configure MongoDB
sudo nano /etc/mongod.conf
```

```yaml
# mongod.conf
storage:
  dbPath: /var/lib/mongodb
  journal:
    enabled: true

systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

net:
  port: 27017
  bindIp: 127.0.0.1

security:
  authorization: enabled

replication:
  replSetName: "smartcoverage-rs"
```

#### Redis Configuration
```bash
# Configure Redis
sudo nano /etc/redis/redis.conf
```

```conf
# redis.conf optimizations
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. Application Deployment

#### Environment Configuration
```bash
# Production environment variables
cat > /opt/smartcoverage/.env << EOF
NODE_ENV=production
PORT=3001

# Database URLs
DB_HOST=localhost
DB_PORT=5432
DB_NAME=smartcoverage_prod
DB_USER=smartcoverage_prod
DB_PASS=secure_password_here

MONGODB_URI=mongodb://localhost:27017/smartcoverage_prod
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=very_secure_jwt_secret_here
CORS_ORIGIN=https://yourdomain.com

# NLP Service
NLP_SERVICE_URL=http://localhost:8000
NLP_API_HOST=0.0.0.0
NLP_API_PORT=8000
NLP_API_WORKERS=4

# Logging
LOG_LEVEL=info
LOG_FILE=/var/log/smartcoverage/app.log
EOF
```

#### PM2 Ecosystem Configuration
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'smartcoverage-backend',
      script: './src/server.js',
      cwd: '/opt/smartcoverage/backend',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      error_file: '/var/log/smartcoverage/backend-error.log',
      out_file: '/var/log/smartcoverage/backend-out.log',
      log_file: '/var/log/smartcoverage/backend.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024'
    },
    {
      name: 'smartcoverage-nlp',
      script: 'start.py',
      cwd: '/opt/smartcoverage/nlp-service',
      interpreter: 'python3',
      instances: 1,
      env: {
        NLP_API_HOST: '0.0.0.0',
        NLP_API_PORT: 8000,
        NLP_API_WORKERS: 4
      },
      error_file: '/var/log/smartcoverage/nlp-error.log',
      out_file: '/var/log/smartcoverage/nlp-out.log',
      log_file: '/var/log/smartcoverage/nlp.log',
      time: true,
      max_memory_restart: '4G'
    }
  ]
};
```

### 4. Nginx Configuration

```nginx
# /etc/nginx/sites-available/smartcoverage
upstream backend {
    server 127.0.0.1:3001;
}

upstream nlp_service {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Frontend (React build)
    location / {
        root /opt/smartcoverage/frontend/build;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Backend API
    location /api/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # NLP Service API
    location /nlp/ {
        proxy_pass http://nlp_service/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 600s;
        proxy_connect_timeout 75s;
    }

    # WebSocket support
    location /ws {
        proxy_pass http://nlp_service;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # File upload size
    client_max_body_size 100M;
}
```

## 🔒 Security Configuration

### 1. Firewall Setup
```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Database Security
```bash
# PostgreSQL security
sudo -u postgres psql << EOF
ALTER USER postgres PASSWORD 'secure_postgres_password';
\q
EOF

# MongoDB security
mongo << EOF
use admin
db.createUser({
  user: "admin",
  pwd: "secure_mongo_password",
  roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase", "readWriteAnyDatabase"]
})
EOF
```

## 📊 Monitoring & Logging

### 1. Log Management
```bash
# Create log directories
sudo mkdir -p /var/log/smartcoverage
sudo chown -R $USER:$USER /var/log/smartcoverage

# Logrotate configuration
sudo nano /etc/logrotate.d/smartcoverage
```

```conf
/var/log/smartcoverage/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 smartcoverage smartcoverage
    postrotate
        pm2 reload all
    endscript
}
```

### 2. Health Monitoring
```bash
# Create monitoring script
cat > /opt/smartcoverage/scripts/health-check.sh << 'EOF'
#!/bin/bash

# Check services
services=("smartcoverage-backend" "smartcoverage-nlp")
for service in "${services[@]}"; do
    if ! pm2 describe $service | grep -q "online"; then
        echo "Service $service is down, restarting..."
        pm2 restart $service
    fi
done

# Check endpoints
if ! curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "Backend health check failed"
    pm2 restart smartcoverage-backend
fi

if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "NLP service health check failed"
    pm2 restart smartcoverage-nlp
fi
EOF

chmod +x /opt/smartcoverage/scripts/health-check.sh

# Add to crontab
crontab -e
# Add: */5 * * * * /opt/smartcoverage/scripts/health-check.sh
```

## 🚀 Deployment Script

```bash
#!/bin/bash
# deploy.sh - Production deployment script

set -e

DEPLOY_DIR="/opt/smartcoverage"
BACKUP_DIR="/opt/smartcoverage-backup-$(date +%Y%m%d-%H%M%S)"

echo "🚀 Starting SmartConverge production deployment..."

# Backup current deployment
if [ -d "$DEPLOY_DIR" ]; then
    echo "📦 Creating backup..."
    sudo cp -r "$DEPLOY_DIR" "$BACKUP_DIR"
fi

# Clone/update repository
if [ ! -d "$DEPLOY_DIR" ]; then
    sudo git clone <repository-url> "$DEPLOY_DIR"
else
    cd "$DEPLOY_DIR"
    sudo git pull origin main
fi

cd "$DEPLOY_DIR"

# Install dependencies
echo "📦 Installing dependencies..."
sudo npm run install:all

# Build frontend
echo "🏗️ Building frontend..."
sudo npm run build:frontend

# Run database migrations
echo "🗄️ Running database migrations..."
sudo npm run migrate

# Restart services
echo "🔄 Restarting services..."
pm2 restart ecosystem.config.js

# Reload Nginx
echo "🔄 Reloading Nginx..."
sudo nginx -t && sudo systemctl reload nginx

echo "✅ Deployment completed successfully!"
echo "🌐 Application available at: https://yourdomain.com"
```

## 🔧 Performance Optimization

### 1. Node.js Optimization
```javascript
// backend/src/server.js optimizations
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster && process.env.NODE_ENV === 'production') {
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }
  
  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died`);
    cluster.fork();
  });
} else {
  // Start application
  require('./app');
}
```

### 2. Database Optimization
```sql
-- PostgreSQL indexes for performance
CREATE INDEX CONCURRENTLY idx_meetings_client_name ON meetings(client_name);
CREATE INDEX CONCURRENTLY idx_meetings_date ON meetings(meeting_date);
CREATE INDEX CONCURRENTLY idx_tags_meeting_id ON tags(meeting_id);
CREATE INDEX CONCURRENTLY idx_tags_confidence ON tags(confidence_score);
```

### 3. Caching Strategy
```javascript
// Redis caching implementation
const redis = require('redis');
const client = redis.createClient(process.env.REDIS_URL);

// Cache frequently accessed data
const cacheMiddleware = (duration = 300) => {
  return async (req, res, next) => {
    const key = `cache:${req.originalUrl}`;
    const cached = await client.get(key);
    
    if (cached) {
      return res.json(JSON.parse(cached));
    }
    
    res.sendResponse = res.json;
    res.json = (body) => {
      client.setex(key, duration, JSON.stringify(body));
      res.sendResponse(body);
    };
    
    next();
  };
};
```

## 🔄 Backup & Recovery

### 1. Database Backup
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/opt/backups/$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

# PostgreSQL backup
pg_dump -h localhost -U smartcoverage_prod smartcoverage_prod > "$BACKUP_DIR/postgres.sql"

# MongoDB backup
mongodump --host localhost --port 27017 --db smartcoverage_prod --out "$BACKUP_DIR/mongodb"

# Compress backups
tar -czf "$BACKUP_DIR.tar.gz" "$BACKUP_DIR"
rm -rf "$BACKUP_DIR"

# Keep only last 30 days
find /opt/backups -name "*.tar.gz" -mtime +30 -delete
```

### 2. Automated Backups
```bash
# Add to crontab
0 2 * * * /opt/smartcoverage/scripts/backup.sh
```

This deployment guide provides a comprehensive approach to deploying SmartConverge in production with security, performance, and reliability considerations.
