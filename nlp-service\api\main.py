from fastapi import <PERSON>AP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import asyncio
import logging
import json
from datetime import datetime
import uvicorn
import websockets
from contextlib import asynccontextmanager

from models.real_time_processor import RealTimeNLPProcessor
from models.sentiment_analyzer import SentimentAnaly<PERSON>
from models.tag_extractor import TagExtractor
from models.summarizer import MeetingSummarizer
from services.semantic_search import SemanticSearchEngine
from services.training_service import TrainingService, TrainingConfig
from services.gemini_service import gemini_service
from config import *

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global instances
nlp_processor = None
search_engine = None
training_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global nlp_processor, search_engine, training_service

    # Startup
    logger.info("🚀 Starting SmartConverge NLP Service...")

    try:
        # Initialize services
        nlp_processor = RealTimeNLPProcessor({
            'redis_host': 'localhost',
            'redis_port': 6379
        })
        await nlp_processor.initialize()

        search_engine = SemanticSearchEngine({
            'index_path': 'data/faiss_index',
            'metadata_path': 'data/metadata.pkl',
            'db_path': 'data/search_db.sqlite'
        })
        await search_engine.initialize()

        training_service = TrainingService({
            'models_dir': 'models',
            'data_dir': 'data'
        })

        logger.info("✅ All services initialized successfully")

    except Exception as e:
        logger.error(f"❌ Failed to initialize services: {e}")
        raise

    yield

    # Shutdown
    logger.info("🛑 Shutting down NLP Service...")
    if nlp_processor:
        nlp_processor.shutdown()

# Create FastAPI app
app = FastAPI(
    title="SmartConverge NLP Service",
    description="Advanced NLP processing service with real-time capabilities",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class TextProcessingRequest(BaseModel):
    text: str = Field(..., description="Text to process")
    options: Optional[Dict[str, Any]] = Field(default={}, description="Processing options")

class SearchRequest(BaseModel):
    query: str = Field(..., description="Search query")
    top_k: int = Field(default=10, description="Number of results to return")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="Search filters")

class DocumentRequest(BaseModel):
    content: str = Field(..., description="Document content")
    metadata: Dict[str, Any] = Field(..., description="Document metadata")
    meeting_id: Optional[int] = Field(default=None, description="Meeting ID")
    chunk_id: Optional[int] = Field(default=None, description="Chunk ID")

class TrainingRequest(BaseModel):
    model_type: str = Field(..., description="Type of model to train")
    training_data: List[Dict[str, Any]] = Field(..., description="Training data")
    config: Dict[str, Any] = Field(default={}, description="Training configuration")

class BatchProcessingRequest(BaseModel):
    texts: List[str] = Field(..., description="List of texts to process")
    options: Optional[Dict[str, Any]] = Field(default={}, description="Processing options")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "nlp_processor": nlp_processor is not None,
            "search_engine": search_engine is not None,
            "training_service": training_service is not None
        }
    }

# Real-time text processing
@app.post("/process/async")
async def process_text_async(request: TextProcessingRequest):
    """Process text asynchronously"""
    try:
        task_id = await nlp_processor.process_text_async(request.text, request.options)
        return {
            "task_id": task_id,
            "status": "processing",
            "message": "Text processing started"
        }
    except Exception as e:
        logger.error(f"Async processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/process/result/{task_id}")
async def get_processing_result(task_id: str):
    """Get processing result by task ID"""
    try:
        result = await nlp_processor.get_result(task_id)
        if result is None:
            raise HTTPException(status_code=404, detail="Result not found or still processing")

        return {
            "task_id": task_id,
            "status": "completed",
            "result": result
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get result: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Synchronous processing for immediate results
@app.post("/process/sync")
async def process_text_sync(request: TextProcessingRequest):
    """Process text synchronously"""
    try:
        # Use the processor's sync method
        result = nlp_processor._process_text_sync(request.text, request.options)
        return {
            "status": "completed",
            "result": result
        }
    except Exception as e:
        logger.error(f"Sync processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Batch processing
@app.post("/process/batch")
async def process_batch(request: BatchProcessingRequest, background_tasks: BackgroundTasks):
    """Process multiple texts in batch"""
    try:
        task_ids = []
        for text in request.texts:
            task_id = await nlp_processor.process_text_async(text, request.options)
            task_ids.append(task_id)

        return {
            "task_ids": task_ids,
            "status": "processing",
            "total_texts": len(request.texts)
        }
    except Exception as e:
        logger.error(f"Batch processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Semantic search endpoints
@app.post("/search")
async def search_documents(request: SearchRequest):
    """Search documents using semantic similarity"""
    try:
        results = await search_engine.search(
            request.query,
            top_k=request.top_k,
            filters=request.filters
        )

        return {
            "query": request.query,
            "results": [
                {
                    "content": r.content,
                    "score": r.score,
                    "metadata": r.metadata,
                    "meeting_id": r.meeting_id,
                    "chunk_id": r.chunk_id
                } for r in results
            ],
            "total_results": len(results)
        }
    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search/semantic")
async def semantic_query(request: SearchRequest):
    """Advanced semantic query with natural language understanding"""
    try:
        result = await search_engine.semantic_query(
            request.query,
            context=request.filters
        )
        return result
    except Exception as e:
        logger.error(f"Semantic query failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Document management
@app.post("/documents")
async def add_document(request: DocumentRequest):
    """Add document to search index"""
    try:
        doc_data = {
            "content": request.content,
            "metadata": request.metadata,
            "meeting_id": request.meeting_id,
            "chunk_id": request.chunk_id
        }

        doc_ids = await search_engine.add_documents([doc_data])

        return {
            "document_id": doc_ids[0],
            "status": "added",
            "message": "Document added to search index"
        }
    except Exception as e:
        logger.error(f"Failed to add document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/documents/batch")
async def add_documents_batch(documents: List[DocumentRequest]):
    """Add multiple documents to search index"""
    try:
        doc_data = []
        for doc in documents:
            doc_data.append({
                "content": doc.content,
                "metadata": doc.metadata,
                "meeting_id": doc.meeting_id,
                "chunk_id": doc.chunk_id
            })

        doc_ids = await search_engine.add_documents(doc_data)

        return {
            "document_ids": doc_ids,
            "status": "added",
            "total_documents": len(doc_ids)
        }
    except Exception as e:
        logger.error(f"Failed to add documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Training endpoints
@app.post("/train/sentiment")
async def train_sentiment_model(request: TrainingRequest):
    """Train sentiment analysis model"""
    try:
        config = TrainingConfig(**request.config)
        config.model_name = config.model_name or "cardiffnlp/twitter-roberta-base-sentiment-latest"

        result = await training_service.train_sentiment_model(
            request.training_data,
            config
        )

        return {
            "status": "completed",
            "model_type": "sentiment",
            "result": result
        }
    except Exception as e:
        logger.error(f"Sentiment training failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/train/classification")
async def train_classification_model(request: TrainingRequest):
    """Train meeting classification model"""
    try:
        config = TrainingConfig(**request.config)
        config.model_name = config.model_name or "distilbert-base-uncased"

        result = await training_service.train_classification_model(
            request.training_data,
            config
        )

        return {
            "status": "completed",
            "model_type": "classification",
            "result": result
        }
    except Exception as e:
        logger.error(f"Classification training failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/train/ner")
async def train_ner_model(request: TrainingRequest):
    """Train Named Entity Recognition model"""
    try:
        config = TrainingConfig(**request.config)
        config.model_name = config.model_name or "distilbert-base-uncased"

        result = await training_service.train_ner_model(
            request.training_data,
            config
        )

        return {
            "status": "completed",
            "model_type": "ner",
            "result": result
        }
    except Exception as e:
        logger.error(f"NER training failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Model evaluation
@app.post("/evaluate/{model_path:path}")
async def evaluate_model(model_path: str, test_data: List[Dict[str, Any]]):
    """Evaluate model performance"""
    try:
        result = await training_service.evaluate_model_performance(
            model_path,
            test_data
        )

        return {
            "status": "completed",
            "evaluation_result": result
        }
    except Exception as e:
        logger.error(f"Model evaluation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Statistics and monitoring
@app.get("/stats")
async def get_statistics():
    """Get service statistics"""
    try:
        search_stats = search_engine.get_stats()

        return {
            "timestamp": datetime.now().isoformat(),
            "search_engine": search_stats,
            "nlp_processor": {
                "active_connections": len(nlp_processor.websocket_connections),
                "queue_size": nlp_processor.processing_queue.qsize(),
                "cache_size": len(nlp_processor.result_cache)
            }
        }
    except Exception as e:
        logger.error(f"Failed to get statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication"""
    await websocket.accept()
    await nlp_processor.add_websocket_connection(websocket)

    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)

            if message.get("type") == "process_text":
                task_id = await nlp_processor.process_text_async(
                    message["text"],
                    message.get("options", {})
                )
                await websocket.send_text(json.dumps({
                    "type": "task_started",
                    "task_id": task_id
                }))

    except WebSocketDisconnect:
        await nlp_processor.remove_websocket_connection(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await nlp_processor.remove_websocket_connection(websocket)

# Specialized endpoints for individual models
@app.post("/models/sentiment/analyze")
async def analyze_sentiment(request: TextProcessingRequest):
    """Analyze sentiment using dedicated model"""
    try:
        sentiment_analyzer = SentimentAnalyzer()
        await sentiment_analyzer.load_model()

        result = sentiment_analyzer.predict(request.text)
        return {
            "text": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "sentiment": result
        }
    except Exception as e:
        logger.error(f"Sentiment analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/models/summarize")
async def summarize_text(request: TextProcessingRequest):
    """Summarize text using dedicated model"""
    try:
        summarizer = MeetingSummarizer()
        await summarizer.load_model()

        result = summarizer.predict(request.text, request.options)
        return {
            "text": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "summary": result
        }
    except Exception as e:
        logger.error(f"Summarization failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/models/extract-tags")
async def extract_tags(request: TextProcessingRequest):
    """Extract tags using dedicated model"""
    try:
        tag_extractor = TagExtractor()
        await tag_extractor.load_model()

        result = tag_extractor.predict(request.text)
        return {
            "text": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "tags": result
        }
    except Exception as e:
        logger.error(f"Tag extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Gemini AI endpoints
@app.post("/gemini/analyze")
async def gemini_analyze_text(request: TextProcessingRequest):
    """Comprehensive text analysis using Gemini AI"""
    try:
        context = request.options.get('meeting_context', {})

        # Run all Gemini analyses in parallel
        results = await asyncio.gather(
            gemini_service.extract_implicit_tags(request.text, context),
            gemini_service.generate_summary(request.text, context),
            gemini_service.analyze_sentiment(request.text, context),
            gemini_service.extract_insights(request.text, context),
            gemini_service.extract_action_items(request.text, context),
            return_exceptions=True
        )

        implicit_tags, summary, sentiment, insights, action_items = results

        return {
            "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "implicit_tags": implicit_tags if not isinstance(implicit_tags, Exception) else [],
            "summary": summary if not isinstance(summary, Exception) else "Summary generation failed",
            "sentiment": sentiment if not isinstance(sentiment, Exception) else gemini_service._get_fallback_sentiment(),
            "insights": insights if not isinstance(insights, Exception) else gemini_service._get_fallback_insights(),
            "action_items": action_items if not isinstance(action_items, Exception) else [],
            "ai_provider": "gemini",
            "processing_time": "async"
        }
    except Exception as e:
        logger.error(f"Gemini analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/gemini/sentiment")
async def gemini_sentiment_analysis(request: TextProcessingRequest):
    """Sentiment analysis using Gemini AI"""
    try:
        context = request.options.get('meeting_context', {})
        result = await gemini_service.analyze_sentiment(request.text, context)

        return {
            "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "sentiment": result,
            "ai_provider": "gemini"
        }
    except Exception as e:
        logger.error(f"Gemini sentiment analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/gemini/summary")
async def gemini_generate_summary(request: TextProcessingRequest):
    """Generate summary using Gemini AI"""
    try:
        context = request.options.get('meeting_context', {})
        result = await gemini_service.generate_summary(request.text, context)

        return {
            "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "summary": result,
            "ai_provider": "gemini"
        }
    except Exception as e:
        logger.error(f"Gemini summary generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/gemini/tags")
async def gemini_extract_tags(request: TextProcessingRequest):
    """Extract implicit tags using Gemini AI"""
    try:
        context = request.options.get('meeting_context', {})
        result = await gemini_service.extract_implicit_tags(request.text, context)

        return {
            "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "implicit_tags": result,
            "ai_provider": "gemini"
        }
    except Exception as e:
        logger.error(f"Gemini tag extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/gemini/insights")
async def gemini_extract_insights(request: TextProcessingRequest):
    """Extract business insights using Gemini AI"""
    try:
        context = request.options.get('meeting_context', {})
        result = await gemini_service.extract_insights(request.text, context)

        return {
            "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "insights": result,
            "ai_provider": "gemini"
        }
    except Exception as e:
        logger.error(f"Gemini insights extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/gemini/action-items")
async def gemini_extract_action_items(request: TextProcessingRequest):
    """Extract action items using Gemini AI"""
    try:
        context = request.options.get('meeting_context', {})
        result = await gemini_service.extract_action_items(request.text, context)

        return {
            "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "action_items": result,
            "ai_provider": "gemini"
        }
    except Exception as e:
        logger.error(f"Gemini action items extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/gemini/test")
async def test_gemini_connection():
    """Test Gemini AI connection"""
    try:
        is_connected = await gemini_service.test_connection()
        return {
            "status": "connected" if is_connected else "disconnected",
            "service": "gemini",
            "initialized": gemini_service.initialized
        }
    except Exception as e:
        logger.error(f"Gemini connection test failed: {e}")
        return {
            "status": "error",
            "service": "gemini",
            "error": str(e)
        }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=API_HOST,
        port=API_PORT,
        reload=True,
        log_level="info"
    )
