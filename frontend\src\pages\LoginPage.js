import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  Alert,
  InputAdornment,
  IconButton,
  Di<PERSON>r,
  Chip,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  Analytics,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';

import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/Common/LoadingSpinner';

const LoginPage = () => {
  const { login, loading } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const onSubmit = async (data) => {
    setError('');
    const result = await login(data);
    if (!result.success) {
      setError(result.error);
    }
  };

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  if (loading) {
    return (
      <Container maxWidth="sm">
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
        >
          <LoadingSpinner message="Signing you in..." />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="sm">
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        py={4}
      >
        {/* Logo and Title */}
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          mb={4}
        >
          <Box
            display="flex"
            alignItems="center"
            gap={1}
            mb={2}
          >
            <Analytics color="primary" sx={{ fontSize: 40 }} />
            <Typography variant="h4" component="h1" fontWeight="bold">
              SmartConverge
            </Typography>
          </Box>
          <Typography variant="h6" color="text.secondary" align="center">
            Intelligent Meeting Analysis System
          </Typography>
        </Box>

        {/* Login Form */}
        <Card sx={{ width: '100%', maxWidth: 400 }}>
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h5" component="h2" align="center" mb={3}>
              Sign In
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit(onSubmit)}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                margin="normal"
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address',
                  },
                })}
                error={!!errors.email}
                helperText={errors.email?.message}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Email color="action" />
                    </InputAdornment>
                  ),
                }}
              />

              <TextField
                fullWidth
                label="Password"
                type={showPassword ? 'text' : 'password'}
                margin="normal"
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters',
                  },
                })}
                error={!!errors.password}
                helperText={errors.password?.message}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleTogglePassword}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                sx={{ mt: 3, mb: 2 }}
                disabled={loading}
              >
                {loading ? 'Signing In...' : 'Sign In'}
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Demo Credentials */}
        <Card sx={{ width: '100%', maxWidth: 400, mt: 3 }}>
          <CardContent sx={{ p: 3 }}>
            <Typography variant="h6" align="center" mb={2}>
              Demo Credentials
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box mb={2}>
              <Chip label="Admin" color="primary" size="small" sx={{ mr: 1 }} />
              <Typography variant="body2" component="div">
                <strong>Email:</strong> <EMAIL><br />
                <strong>Password:</strong> admin123
              </Typography>
            </Box>

            <Box mb={2}>
              <Chip label="Analyst" color="secondary" size="small" sx={{ mr: 1 }} />
              <Typography variant="body2" component="div">
                <strong>Email:</strong> <EMAIL><br />
                <strong>Password:</strong> password123
              </Typography>
            </Box>

            <Box>
              <Chip label="Manager" color="success" size="small" sx={{ mr: 1 }} />
              <Typography variant="body2" component="div">
                <strong>Email:</strong> <EMAIL><br />
                <strong>Password:</strong> password123
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* Footer */}
        <Typography variant="caption" color="text.secondary" align="center" mt={4}>
          © 2024 SmartConverge. All rights reserved.
        </Typography>
      </Box>
    </Container>
  );
};

export default LoginPage;
