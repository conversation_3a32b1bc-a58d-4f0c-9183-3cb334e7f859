version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: smartcoverage-postgres
    environment:
      POSTGRES_DB: smartcoverage
      POSTGRES_USER: smartcoverage_user
      POSTGRES_PASSWORD: smartcoverage_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - smartcoverage-network
    restart: unless-stopped

  # MongoDB Database
  mongodb:
    image: mongo:7-jammy
    container_name: smartcoverage-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: smartcoverage_user
      MONGO_INITDB_ROOT_PASSWORD: smartcoverage_password
      MONGO_INITDB_DATABASE: smartcoverage
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - smartcoverage-network
    restart: unless-stopped

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: smartcoverage-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - smartcoverage-network
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: smartcoverage-backend
    environment:
      NODE_ENV: development
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: smartcoverage
      DB_USER: smartcoverage_user
      DB_PASS: smartcoverage_password
      MONGODB_URI: ************************************************************************************************
      JWT_SECRET: your_super_secret_jwt_key_change_in_production
      CORS_ORIGIN: http://localhost:3000
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      PINECONE_API_KEY: ${PINECONE_API_KEY:-}
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/logs:/app/logs
    depends_on:
      - postgres
      - mongodb
      - redis
    networks:
      - smartcoverage-network
    restart: unless-stopped
    command: npm run dev

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: smartcoverage-frontend
    environment:
      REACT_APP_API_URL: http://localhost:3001/api
      REACT_APP_APP_NAME: SmartConverge
      REACT_APP_VERSION: 1.0.0
      REACT_APP_ENVIRONMENT: development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - smartcoverage-network
    restart: unless-stopped
    command: npm start

volumes:
  postgres_data:
    driver: local
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  smartcoverage-network:
    driver: bridge
