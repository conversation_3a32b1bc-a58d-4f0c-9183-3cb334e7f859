const { Meeting, Tag, Client, User } = require('../models');
const nlpService = require('../services/nlpService');
const logger = require('./logger');

// Sample meeting data from your CSV
const sampleMeetings = [
  {
    meeting_subject: "Blockchain Investment Strategy",
    meeting_notes: "Discussed potential investment in emerging blockchain technologies focusing on DeFi projects. Client expressed interest in allocating 15% of portfolio to crypto assets. Recommended Ethereum-based protocols for their governance models.",
    organizer_name: "<PERSON>",
    organizer_department: "Investment Strategy",
    client_name: "TechVentures Inc.",
    client_industry: "Technology",
    meeting_date: "2024-10-01",
    manual_tags: ["blockchain", "investment", "crypto", "DeFi"],
    attendees: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
    key_decisions: "Proceed with 10% allocation to ETH ecosystem",
    investment_amount_discussed: 500000,
    roi_expectation: "18-25%",
    summary: "Strategic meeting with TechVentures Inc. exploring blockchain investments, particularly DeFi projects. Client interested in 15% portfolio allocation to crypto. Team recommended Ethereum protocols. Decision made to proceed with 10% allocation ($500K) to ETH ecosystem with expected 18-25% ROI. Security audit reports to be shared with client. Overall positive sentiment with 4.2 client satisfaction.",
    client_satisfaction: 4.2,
    sentiment_score: 0.6,
    meeting_duration_minutes: 90,
    billing_amount: 675.00
  },
  {
    meeting_subject: "Quarterly Portfolio Review",
    meeting_notes: "Reviewed Q3 performance across all sectors. Healthcare investments outperformed market by 12%. Discussed rebalancing strategy for incoming quarter with emphasis on reducing exposure to traditional banking.",
    organizer_name: "Robert Williams",
    organizer_department: "Portfolio Management",
    client_name: "Apex Holdings",
    client_industry: "Finance",
    meeting_date: "2024-10-02",
    manual_tags: ["quarterly review", "portfolio management"],
    attendees: ["Robert Williams", "Emma Davis", "Thomas Walker"],
    key_decisions: "Rebalance to increase healthcare exposure by 5%",
    investment_amount_discussed: 0,
    roi_expectation: "8-10%",
    summary: "Quarterly review with Apex Holdings analyzing Q3 portfolio performance. Healthcare investments exceeded market by 12%. Discussion focused on rebalancing strategy for upcoming quarter, reducing traditional banking exposure. Decision to increase healthcare allocation by 5% with projected 8-10% ROI. Portfolio allocation update to be sent. High client satisfaction at 4.5 with positive sentiment.",
    client_satisfaction: 4.5,
    sentiment_score: 0.7,
    meeting_duration_minutes: 75,
    billing_amount: 562.50
  },
  {
    meeting_subject: "Green Energy Fund Pitch",
    meeting_notes: "Presented new green energy fund focused on solar and wind infrastructure projects. Client showed strong interest in ESG investments with competitive returns. Discussed tax incentives for renewable investments.",
    organizer_name: "Jessica Chen",
    organizer_department: "Sustainable Investments",
    client_name: "GreenGrowth Capital",
    client_industry: "Environmental",
    meeting_date: "2024-10-03",
    manual_tags: ["ESG", "renewable energy", "sustainability", "investment opportunity"],
    attendees: ["Jessica Chen", "Alexander Green", "Lisa Johnson", "Maria Rodriguez"],
    key_decisions: "Initial commitment of $2M to green energy fund",
    investment_amount_discussed: 2000000,
    roi_expectation: "12-15%",
    summary: "Client pitch meeting at GreenGrowth Capital's office introducing new green energy fund for solar and wind infrastructure. Client demonstrated strong interest in ESG investments with competitive returns. Tax incentives for renewable investments discussed. Resulted in $2M initial commitment to the fund with 12-15% expected ROI. Follow-up includes sending prospectus and arranging solar farm site visit. Excellent client satisfaction at 4.7 with very positive sentiment.",
    client_satisfaction: 4.7,
    sentiment_score: 0.8,
    meeting_duration_minutes: 120,
    billing_amount: 900.00
  },
  {
    meeting_subject: "AI Startups Evaluation",
    meeting_notes: "Analyzed five AI startups specializing in healthcare applications. Client requires due diligence on intellectual property protections and regulatory approval pathways. Primary interest in diagnostic tools with proven accuracy rates.",
    organizer_name: "Michael Brown",
    organizer_department: "Emerging Tech Investments",
    client_name: "HealthInnovate Group",
    client_industry: "Healthcare",
    meeting_date: "2024-10-04",
    manual_tags: ["AI", "startups", "healthcare", "due diligence"],
    attendees: ["Michael Brown", "Dr. Jennifer Lee", "Samuel Jackson", "Patricia White"],
    key_decisions: "Short-list 2 companies for potential investment",
    investment_amount_discussed: 3500000,
    roi_expectation: "20-30%",
    summary: "Virtual evaluation meeting with HealthInnovate Group analyzing five AI healthcare startups. Focus on diagnostic tools with proven accuracy rates. Client emphasized need for IP protection due diligence and regulatory approval verification. Decision to shortlist two companies for potential $3.5M investment with high-risk profile but potentially high returns (20-30%). Follow-up includes IP review and FDA approval status checks. Moderately satisfied client (3.9) with somewhat positive sentiment.",
    client_satisfaction: 3.9,
    sentiment_score: 0.3,
    meeting_duration_minutes: 105,
    billing_amount: 787.50
  },
  {
    meeting_subject: "Real Estate Market Analysis",
    meeting_notes: "Examined commercial real estate trends in metropolitan areas post-pandemic. Client interested in office space conversion opportunities. Data suggests 15% vacancy rates with declining lease renewals in downtown areas.",
    organizer_name: "Daniel Smith",
    organizer_department: "Real Estate Division",
    client_name: "Urban Development Partners",
    client_industry: "Real Estate",
    meeting_date: "2024-10-05",
    manual_tags: ["real estate", "market analysis", "commercial property"],
    attendees: ["Daniel Smith", "Rachel Green", "Oliver Thompson"],
    key_decisions: "Focus on suburban office conversions to mixed-use",
    investment_amount_discussed: 12000000,
    roi_expectation: "7-9%",
    summary: "Analysis meeting with Urban Development Partners examining post-pandemic commercial real estate trends. Data revealed 15% vacancy rates and declining downtown lease renewals. Discussion centered on office space conversion opportunities, particularly suburban offices to mixed-use properties. Significant investment amount of $12M discussed with relatively conservative 7-9% ROI expectations. Team to prepare valuation models for three target properties. Good client satisfaction (4.0) with somewhat positive sentiment.",
    client_satisfaction: 4.0,
    sentiment_score: 0.4,
    meeting_duration_minutes: 95,
    billing_amount: 712.50
  },
  {
    meeting_subject: "Cryptocurrency Security Protocols",
    meeting_notes: "Detailed discussion about security measures for institutional crypto investments. Client concerned about custody solutions and insurance options. Recommended multi-signature wallets and cold storage options with third-party audits.",
    organizer_name: "Thomas Walker",
    organizer_department: "Digital Assets",
    client_name: "Institutional Investors LLC",
    client_industry: "Financial Services",
    meeting_date: "2024-10-06",
    manual_tags: ["crypto", "security", "blockchain", "institutional investment"],
    attendees: ["Thomas Walker", "Brian Johnson", "Alexandra Lee", "Christopher Davis"],
    key_decisions: "Implement enhanced security protocol before investment",
    investment_amount_discussed: 10000000,
    roi_expectation: "15-25%",
    summary: "Technical review with Institutional Investors LLC focused on security protocols for large-scale crypto investments. Client expressed significant concerns about custody solutions and insurance options. Team recommended multi-signature wallets and cold storage with third-party audits. Very high-risk $10M investment discussed with 15-25% potential returns. Action items include security protocol documentation and custody provider demo. Client satisfaction below average (3.8) with slightly positive sentiment, reflecting security concerns.",
    client_satisfaction: 3.8,
    sentiment_score: 0.2,
    meeting_duration_minutes: 110,
    billing_amount: 825.00
  }
];

// Sample users
const sampleUsers = [
  {
    email: "<EMAIL>",
    password: "admin123",
    first_name: "Admin",
    last_name: "User",
    role: "admin",
    department: "Management",
    is_active: true,
    email_verified: true
  },
  {
    email: "<EMAIL>",
    password: "password123",
    first_name: "Sarah",
    last_name: "Johnson",
    role: "analyst",
    department: "Investment Strategy",
    is_active: true,
    email_verified: true
  },
  {
    email: "<EMAIL>",
    password: "password123",
    first_name: "Robert",
    last_name: "Williams",
    role: "manager",
    department: "Portfolio Management",
    is_active: true,
    email_verified: true
  },
  {
    email: "<EMAIL>",
    password: "password123",
    first_name: "Jessica",
    last_name: "Chen",
    role: "analyst",
    department: "Sustainable Investments",
    is_active: true,
    email_verified: true
  }
];

// Sample clients
const sampleClients = [
  {
    name: "TechVentures Inc.",
    industry: "Technology",
    contact_email: "<EMAIL>",
    contact_person: "John Smith",
    billing_rate: 150.00,
    status: "active",
    tier: "premium",
    onboarding_date: "2024-01-15"
  },
  {
    name: "Apex Holdings",
    industry: "Finance",
    contact_email: "<EMAIL>",
    contact_person: "Emma Davis",
    billing_rate: 150.00,
    status: "active",
    tier: "standard",
    onboarding_date: "2024-02-20"
  },
  {
    name: "GreenGrowth Capital",
    industry: "Environmental",
    contact_email: "<EMAIL>",
    contact_person: "Alexander Green",
    billing_rate: 180.00,
    status: "active",
    tier: "premium",
    onboarding_date: "2024-03-10"
  },
  {
    name: "HealthInnovate Group",
    industry: "Healthcare",
    contact_email: "<EMAIL>",
    contact_person: "Dr. Jennifer Lee",
    billing_rate: 160.00,
    status: "active",
    tier: "standard",
    onboarding_date: "2024-04-05"
  },
  {
    name: "Urban Development Partners",
    industry: "Real Estate",
    contact_email: "<EMAIL>",
    contact_person: "Rachel Green",
    billing_rate: 140.00,
    status: "active",
    tier: "standard",
    onboarding_date: "2024-05-12"
  },
  {
    name: "Institutional Investors LLC",
    industry: "Financial Services",
    contact_email: "<EMAIL>",
    contact_person: "Brian Johnson",
    billing_rate: 200.00,
    status: "active",
    tier: "premium",
    onboarding_date: "2024-06-01"
  }
];

async function seedDatabase() {
  try {
    logger.info('Starting database seeding...');

    // Clear existing data
    await Tag.destroy({ where: {} });
    await Meeting.destroy({ where: {} });
    await Client.destroy({ where: {} });
    await User.destroy({ where: {} });

    logger.info('Cleared existing data');

    // Create users
    const users = await User.bulkCreate(sampleUsers);
    logger.info(`Created ${users.length} users`);

    // Create clients
    const clients = await Client.bulkCreate(sampleClients);
    logger.info(`Created ${clients.length} clients`);

    // Create meetings with NLP processing
    for (const meetingData of sampleMeetings) {
      try {
        // Find the user who created this meeting
        const organizer = users.find(user => 
          `${user.first_name} ${user.last_name}` === meetingData.organizer_name
        );

        const meeting = await Meeting.create({
          ...meetingData,
          created_by: organizer ? organizer.id : users[0].id, // Default to first user if not found
        });

        logger.info(`Created meeting: ${meeting.meeting_subject}`);

        // Process with NLP to generate additional tags
        try {
          const nlpResults = await nlpService.processText(meeting.meeting_notes, {
            subject: meeting.meeting_subject,
            client_name: meeting.client_name,
            organizer_name: meeting.organizer_name,
          });

          // Create manual tags
          for (const tagName of meetingData.manual_tags) {
            await Tag.create({
              meeting_id: meeting.id,
              tag_name: tagName,
              tag_type: 'manual',
              confidence_score: 1.0,
              category: 'manual',
              source: 'manual',
              frequency: 1,
            });
          }

          // Create NLP-generated tags
          for (const tag of nlpResults.all_tags) {
            await Tag.create({
              meeting_id: meeting.id,
              tag_name: tag.name,
              tag_type: tag.type,
              confidence_score: tag.confidence,
              category: tag.category,
              source: tag.source,
              context: tag.context,
              frequency: tag.frequency,
            });
          }

          logger.info(`Created ${meetingData.manual_tags.length + nlpResults.all_tags.length} tags for meeting ${meeting.id}`);
        } catch (nlpError) {
          logger.warn(`NLP processing failed for meeting ${meeting.id}:`, nlpError.message);
          
          // Create manual tags only
          for (const tagName of meetingData.manual_tags) {
            await Tag.create({
              meeting_id: meeting.id,
              tag_name: tagName,
              tag_type: 'manual',
              confidence_score: 1.0,
              category: 'manual',
              source: 'manual',
              frequency: 1,
            });
          }
        }
      } catch (error) {
        logger.error(`Error creating meeting ${meetingData.meeting_subject}:`, error);
      }
    }

    // Update client statistics
    for (const client of clients) {
      try {
        await client.updateMeetingStats();
        logger.info(`Updated stats for client: ${client.name}`);
      } catch (error) {
        logger.warn(`Failed to update stats for client ${client.name}:`, error.message);
      }
    }

    logger.info('✅ Database seeding completed successfully!');
    
    return {
      users: users.length,
      clients: clients.length,
      meetings: sampleMeetings.length,
    };
  } catch (error) {
    logger.error('❌ Database seeding failed:', error);
    throw error;
  }
}

module.exports = {
  seedDatabase,
  sampleMeetings,
  sampleUsers,
  sampleClients,
};
