const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Meeting = sequelize.define('Meeting', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  meeting_subject: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 500],
    },
  },
  meeting_notes: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: true,
    },
  },
  organizer_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 100],
    },
  },
  organizer_department: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 100],
    },
  },
  client_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 200],
    },
  },
  client_industry: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 100],
    },
  },
  meeting_date: {
    type: DataTypes.DATE,
    allowNull: false,
    validate: {
      isDate: true,
    },
  },
  manual_tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
  },
  attendees: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
  },
  key_decisions: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  investment_amount_discussed: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    validate: {
      min: 0,
    },
  },
  roi_expectation: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 50],
    },
  },
  summary: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  sentiment_score: {
    type: DataTypes.FLOAT,
    allowNull: true,
    validate: {
      min: -1,
      max: 1,
    },
  },
  client_satisfaction: {
    type: DataTypes.FLOAT,
    allowNull: true,
    validate: {
      min: 1,
      max: 5,
    },
  },
  meeting_duration_minutes: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
    },
  },
  meeting_type: {
    type: DataTypes.ENUM('in-person', 'virtual', 'hybrid', 'phone'),
    allowNull: true,
    defaultValue: 'virtual',
  },
  meeting_platform: {
    type: DataTypes.ENUM('zoom', 'teams', 'meet', 'in_person', 'other'),
    allowNull: false,
    defaultValue: 'zoom',
  },
  zoom_meeting_id: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  zoom_join_url: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  zoom_start_url: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  zoom_password: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  status: {
    type: DataTypes.ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'rescheduled'),
    allowNull: false,
    defaultValue: 'scheduled',
  },
  is_billable: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  billing_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0,
    },
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
}, {
  tableName: 'meetings',
  timestamps: true,
  indexes: [
    {
      fields: ['meeting_date'],
    },
    {
      fields: ['client_name'],
    },
    {
      fields: ['organizer_name'],
    },
    {
      fields: ['status'],
    },
    {
      fields: ['created_at'],
    },
  ],
});

// Instance methods
Meeting.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());

  // Format dates
  if (values.meeting_date) {
    values.meeting_date = values.meeting_date.toISOString();
  }

  return values;
};

// Class methods
Meeting.findByDateRange = function(startDate, endDate) {
  return this.findAll({
    where: {
      meeting_date: {
        [sequelize.Sequelize.Op.between]: [startDate, endDate],
      },
    },
    order: [['meeting_date', 'DESC']],
  });
};

Meeting.findByClient = function(clientName) {
  return this.findAll({
    where: {
      client_name: {
        [sequelize.Sequelize.Op.iLike]: `%${clientName}%`,
      },
    },
    order: [['meeting_date', 'DESC']],
  });
};

module.exports = Meeting;
