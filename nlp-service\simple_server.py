from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Any, Optional
import asyncio
import logging
import json
from datetime import datetime
import os
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="SmartConverge NLP Service",
    description="Advanced NLP processing service with Gemini AI",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class TextProcessingRequest(BaseModel):
    text: str = Field(..., description="Text to process")
    options: Optional[Dict[str, Any]] = Field(default={}, description="Processing options")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "basic_nlp": True,
            "gemini_available": False  # Will be updated when Gemini is configured
        }
    }

# Basic fallback sentiment analysis
def fallback_sentiment_analysis(text: str):
    """Simple rule-based sentiment analysis"""
    positive_words = ['good', 'great', 'excellent', 'positive', 'successful', 'profitable', 'growth', 'happy', 'satisfied', 'pleased', 'excited', 'optimistic']
    negative_words = ['bad', 'poor', 'negative', 'loss', 'decline', 'risk', 'concern', 'unhappy', 'disappointed', 'worried', 'frustrated', 'problem']
    
    words = text.lower().split()
    positive_count = sum(1 for word in words if any(pos in word for pos in positive_words))
    negative_count = sum(1 for word in words if any(neg in word for neg in negative_words))
    
    total_words = len(words)
    if total_words == 0:
        return {
            "overall": "neutral",
            "confidence": 0.5,
            "sentiment_score": 0,
            "scores": {"positive": 0.33, "negative": 0.33, "neutral": 0.34}
        }
    
    score = (positive_count - negative_count) / max(total_words, 1)
    
    if score > 0.02:
        overall = "positive"
    elif score < -0.02:
        overall = "negative"
    else:
        overall = "neutral"
    
    return {
        "overall": overall,
        "confidence": min(0.8, abs(score) * 10 + 0.3),
        "sentiment_score": score,
        "scores": {
            "positive": max(0, positive_count / max(total_words, 1)),
            "negative": max(0, negative_count / max(total_words, 1)),
            "neutral": max(0, 1 - (positive_count + negative_count) / max(total_words, 1))
        },
        "reasoning": f"Found {positive_count} positive and {negative_count} negative indicators",
        "key_indicators": []
    }

# Basic fallback tag extraction
def fallback_tag_extraction(text: str):
    """Simple keyword-based tag extraction"""
    business_keywords = {
        'investment': ['investment', 'invest', 'funding', 'capital'],
        'strategy': ['strategy', 'strategic', 'plan', 'planning'],
        'growth': ['growth', 'expand', 'expansion', 'scale'],
        'revenue': ['revenue', 'income', 'profit', 'earnings'],
        'market': ['market', 'customer', 'client', 'segment'],
        'risk': ['risk', 'challenge', 'concern', 'issue'],
        'opportunity': ['opportunity', 'potential', 'chance'],
        'technology': ['technology', 'tech', 'digital', 'innovation'],
        'partnership': ['partnership', 'collaboration', 'alliance'],
        'competition': ['competition', 'competitor', 'competitive']
    }
    
    tags = []
    text_lower = text.lower()
    
    for category, keywords in business_keywords.items():
        for keyword in keywords:
            if keyword in text_lower:
                tags.append({
                    "name": category,
                    "type": "explicit",
                    "confidence": 0.7,
                    "source": "keyword_match",
                    "category": "business",
                    "frequency": text_lower.count(keyword)
                })
                break  # Only add each category once
    
    return tags

# Basic fallback summarization
def fallback_summarization(text: str):
    """Simple extractive summarization"""
    sentences = [s.strip() for s in text.split('.') if s.strip()]
    
    if len(sentences) <= 2:
        return text
    
    # Simple scoring based on sentence length and position
    scored_sentences = []
    for i, sentence in enumerate(sentences):
        score = len(sentence.split())  # Longer sentences get higher scores
        if i == 0:  # First sentence bonus
            score *= 1.5
        if i == len(sentences) - 1:  # Last sentence bonus
            score *= 1.2
        scored_sentences.append((score, sentence))
    
    # Sort by score and take top sentences
    scored_sentences.sort(reverse=True)
    top_sentences = scored_sentences[:min(3, len(scored_sentences))]
    
    # Sort back by original order
    summary_sentences = []
    for _, sentence in top_sentences:
        original_index = sentences.index(sentence)
        summary_sentences.append((original_index, sentence))
    
    summary_sentences.sort()
    summary = '. '.join([sentence for _, sentence in summary_sentences]) + '.'
    
    return summary

# Synchronous processing endpoint
@app.post("/process/sync")
async def process_text_sync(request: TextProcessingRequest):
    """Process text synchronously"""
    try:
        start_time = datetime.now()
        
        # Basic processing with fallback methods
        sentiment = fallback_sentiment_analysis(request.text)
        tags = fallback_tag_extraction(request.text)
        summary = fallback_summarization(request.text)
        
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        result = {
            "explicit_tags": tags,
            "implicit_tags": [],
            "all_tags": tags,
            "summary": summary,
            "sentiment": sentiment,
            "entities": [],
            "processing_time_ms": processing_time,
            "word_count": len(request.text.split()),
            "confidence_score": 0.6,
            "ai_provider": "fallback"
        }
        
        return {
            "status": "completed",
            "result": result
        }
    except Exception as e:
        logger.error(f"Sync processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Basic endpoints
@app.post("/models/sentiment/analyze")
async def analyze_sentiment_basic(request: TextProcessingRequest):
    """Basic sentiment analysis"""
    try:
        result = fallback_sentiment_analysis(request.text)
        return {
            "text": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "sentiment": result
        }
    except Exception as e:
        logger.error(f"Sentiment analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/models/extract-tags")
async def extract_tags_basic(request: TextProcessingRequest):
    """Basic tag extraction"""
    try:
        result = fallback_tag_extraction(request.text)
        return {
            "text": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "tags": result
        }
    except Exception as e:
        logger.error(f"Tag extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/models/summarize")
async def summarize_text_basic(request: TextProcessingRequest):
    """Basic text summarization"""
    try:
        result = fallback_summarization(request.text)
        return {
            "text": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "summary": result
        }
    except Exception as e:
        logger.error(f"Summarization failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Test endpoint for connectivity
@app.get("/test")
async def test_connection():
    """Test endpoint"""
    return {
        "status": "connected",
        "service": "nlp-basic",
        "message": "NLP service is running successfully"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "SmartConverge NLP Service",
        "version": "1.0.0",
        "status": "running",
        "gemini_available": False,
        "endpoints": [
            "/health",
            "/test",
            "/process/sync",
            "/models/sentiment/analyze",
            "/models/extract-tags",
            "/models/summarize"
        ]
    }

if __name__ == "__main__":
    print("🚀 Starting SmartConverge NLP Service...")
    print("📍 Service will be available at: http://localhost:8000")
    print("📋 Health check: http://localhost:8000/health")
    print("🧪 Test endpoint: http://localhost:8000/test")
    uvicorn.run(app, host="0.0.0.0", port=8000)
