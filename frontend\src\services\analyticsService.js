import api from './api';

class AnalyticsService {
  // Get manager analytics
  async getManagerAnalytics(managerId, filters = {}) {
    try {
      const response = await api.get(`/analytics/manager/${managerId}`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get manager analytics:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          monthlyMeetings: 0,
          averageSentiment: 'Neutral',
          activeClients: 0,
          averageDuration: '45m',
          weeklyMeetings: [8, 12, 15, 10],
          sentimentDistribution: [25, 35, 25, 10, 5],
          meetingTypeDistribution: [45, 25, 20, 10],
          topClients: []
        }
      };
    }
  }

  // Get admin analytics
  async getAdminAnalytics(filters = {}) {
    try {
      const response = await api.get('/analytics/admin', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get admin analytics:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          totalUsers: 0,
          totalMeetings: 0,
          totalClients: 0,
          activeUsers: 0,
          statusDistribution: [],
          userRoles: [],
          platformUsage: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            data: [12, 15, 8, 20, 18, 5, 3]
          }
        }
      };
    }
  }

  // Get dashboard analytics
  async getDashboardAnalytics(filters = {}) {
    try {
      const response = await api.get('/analytics/dashboard', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get dashboard analytics:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          totalMeetings: 0,
          totalClients: 0,
          averageSentiment: 0,
          totalRevenue: 0,
          meetingTrends: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            data: [8, 12, 15, 10]
          },
          sentimentDistribution: [60, 30, 10],
          clientActivity: {
            labels: ['Client A', 'Client B', 'Client C'],
            data: [5, 8, 3]
          }
        }
      };
    }
  }

  // Get meeting analytics
  async getMeetingAnalytics(meetingId) {
    try {
      const response = await api.get(`/analytics/meeting/${meetingId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting analytics:', error);
      throw error;
    }
  }

  // Get client analytics
  async getClientAnalytics(clientId, filters = {}) {
    try {
      const response = await api.get(`/analytics/client/${clientId}`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get client analytics:', error);
      throw error;
    }
  }

  // Get sentiment trends
  async getSentimentTrends(filters = {}) {
    try {
      const response = await api.get('/analytics/sentiment-trends', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get sentiment trends:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          datasets: [{
            label: 'Sentiment Score',
            data: [0.2, 0.4, 0.3, 0.5],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)'
          }]
        }
      };
    }
  }

  // Get meeting frequency analytics
  async getMeetingFrequency(filters = {}) {
    try {
      const response = await api.get('/analytics/meeting-frequency', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting frequency:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          daily: [2, 3, 1, 4, 2, 1, 0],
          weekly: [12, 15, 8, 20],
          monthly: [45, 52, 38, 61]
        }
      };
    }
  }

  // Get client engagement metrics
  async getClientEngagement(filters = {}) {
    try {
      const response = await api.get('/analytics/client-engagement', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get client engagement:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          engagementScore: 75,
          activeClients: 25,
          newClients: 5,
          churnRate: 2.5,
          satisfactionScore: 4.2
        }
      };
    }
  }

  // Get performance metrics
  async getPerformanceMetrics(filters = {}) {
    try {
      const response = await api.get('/analytics/performance', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get performance metrics:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          averageMeetingDuration: 45,
          meetingSuccessRate: 85,
          clientSatisfaction: 4.2,
          followUpRate: 78,
          conversionRate: 12.5
        }
      };
    }
  }

  // Get tag analytics
  async getTagAnalytics(filters = {}) {
    try {
      const response = await api.get('/analytics/tags', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get tag analytics:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          topTags: [
            { name: 'investment', count: 45, sentiment: 0.3 },
            { name: 'strategy', count: 38, sentiment: 0.2 },
            { name: 'growth', count: 32, sentiment: 0.4 },
            { name: 'risk', count: 28, sentiment: -0.1 },
            { name: 'opportunity', count: 25, sentiment: 0.5 }
          ],
          tagTrends: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            data: [15, 18, 22, 25]
          }
        }
      };
    }
  }

  // Get real-time analytics
  async getRealTimeAnalytics() {
    try {
      const response = await api.get('/analytics/real-time');
      return response.data;
    } catch (error) {
      console.error('Failed to get real-time analytics:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          activeMeetings: 0,
          onlineUsers: 0,
          todaysMeetings: 0,
          systemHealth: 'good'
        }
      };
    }
  }

  // Get comparative analytics
  async getComparativeAnalytics(period1, period2) {
    try {
      const response = await api.get('/analytics/compare', {
        params: { period1, period2 }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get comparative analytics:', error);
      throw error;
    }
  }

  // Get export analytics
  async exportAnalytics(type, filters = {}, format = 'csv') {
    try {
      const response = await api.get(`/analytics/export/${type}`, {
        params: { ...filters, format },
        responseType: 'blob'
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `analytics-${type}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return { success: true };
    } catch (error) {
      console.error('Failed to export analytics:', error);
      throw error;
    }
  }

  // Get custom analytics
  async getCustomAnalytics(query) {
    try {
      const response = await api.post('/analytics/custom', { query });
      return response.data;
    } catch (error) {
      console.error('Failed to get custom analytics:', error);
      throw error;
    }
  }

  // Get analytics summary
  async getAnalyticsSummary(filters = {}) {
    try {
      const response = await api.get('/analytics/summary', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get analytics summary:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          totalMeetings: 0,
          averageDuration: 0,
          sentimentScore: 0,
          engagementRate: 0,
          growthRate: 0,
          insights: [
            'No data available yet',
            'Start by creating some meetings',
            'Analytics will appear as data is collected'
          ]
        }
      };
    }
  }

  // Get user analytics
  async getUserAnalytics(userId, filters = {}) {
    try {
      const response = await api.get(`/analytics/user/${userId}`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get user analytics:', error);
      throw error;
    }
  }

  // Get industry analytics
  async getIndustryAnalytics(industry, filters = {}) {
    try {
      const response = await api.get(`/analytics/industry/${industry}`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get industry analytics:', error);
      throw error;
    }
  }

  // Get predictive analytics
  async getPredictiveAnalytics(type, filters = {}) {
    try {
      const response = await api.get(`/analytics/predictive/${type}`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get predictive analytics:', error);
      throw error;
    }
  }

  // Generate analytics report
  async generateReport(reportType, filters = {}) {
    try {
      const response = await api.post('/analytics/generate-report', {
        type: reportType,
        filters
      });
      return response.data;
    } catch (error) {
      console.error('Failed to generate analytics report:', error);
      throw error;
    }
  }
}

export const analyticsService = new AnalyticsService();
export default analyticsService;
