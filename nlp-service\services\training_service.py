import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from transformers import (
    AutoTokenizer, AutoModel, AutoModelForSequenceClassification,
    AdamW, get_linear_schedule_with_warmup, TrainingArguments, Trainer
)
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from typing import Dict, List, Any, Optional, Tuple
import logging
import json
from pathlib import Path
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass
import asyncio
from concurrent.futures import ThreadPoolExecutor
import pickle

logger = logging.getLogger(__name__)

@dataclass
class TrainingConfig:
    """Training configuration"""
    model_name: str
    num_epochs: int = 3
    batch_size: int = 16
    learning_rate: float = 2e-5
    weight_decay: float = 0.01
    warmup_steps: int = 100
    max_length: int = 512
    validation_split: float = 0.2
    save_steps: int = 500
    eval_steps: int = 100
    output_dir: str = "models/trained"

class MeetingDataset(Dataset):
    """Custom dataset for meeting data"""
    
    def __init__(self, texts: List[str], labels: List[int], tokenizer, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

class TrainingService:
    """Advanced training service for NLP models"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.models_dir = Path(config.get('models_dir', 'models'))
        self.data_dir = Path(config.get('data_dir', 'data'))
        self.training_history = {}
        
        # Create directories
        self.models_dir.mkdir(parents=True, exist_ok=True)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Thread pool for async operations
        self.executor = ThreadPoolExecutor(max_workers=2)
    
    async def train_sentiment_model(self, training_data: List[Dict[str, Any]], config: TrainingConfig) -> Dict[str, Any]:
        """Train sentiment analysis model"""
        logger.info("Starting sentiment model training...")
        
        try:
            # Prepare data
            texts = [item['text'] for item in training_data]
            labels = [self._sentiment_to_label(item['sentiment']) for item in training_data]
            
            # Split data
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, labels, test_size=config.validation_split, random_state=42, stratify=labels
            )
            
            # Load tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(config.model_name)
            model = AutoModelForSequenceClassification.from_pretrained(
                config.model_name,
                num_labels=3  # negative, neutral, positive
            )
            
            # Create datasets
            train_dataset = MeetingDataset(train_texts, train_labels, tokenizer, config.max_length)
            val_dataset = MeetingDataset(val_texts, val_labels, tokenizer, config.max_length)
            
            # Train model
            training_results = await self._train_model(
                model, tokenizer, train_dataset, val_dataset, config, "sentiment"
            )
            
            # Save model
            model_path = self.models_dir / "sentiment_model"
            model.save_pretrained(model_path)
            tokenizer.save_pretrained(model_path)
            
            logger.info("✅ Sentiment model training completed")
            return training_results
            
        except Exception as e:
            logger.error(f"❌ Sentiment model training failed: {e}")
            raise
    
    async def train_classification_model(self, training_data: List[Dict[str, Any]], config: TrainingConfig) -> Dict[str, Any]:
        """Train meeting classification model"""
        logger.info("Starting classification model training...")
        
        try:
            # Prepare data
            texts = [item['text'] for item in training_data]
            labels = [item['category_id'] for item in training_data]
            
            # Get unique labels
            unique_labels = sorted(list(set(labels)))
            num_labels = len(unique_labels)
            label_map = {label: idx for idx, label in enumerate(unique_labels)}
            
            # Map labels to indices
            mapped_labels = [label_map[label] for label in labels]
            
            # Split data
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, mapped_labels, test_size=config.validation_split, random_state=42, stratify=mapped_labels
            )
            
            # Load tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(config.model_name)
            model = AutoModelForSequenceClassification.from_pretrained(
                config.model_name,
                num_labels=num_labels
            )
            
            # Create datasets
            train_dataset = MeetingDataset(train_texts, train_labels, tokenizer, config.max_length)
            val_dataset = MeetingDataset(val_texts, val_labels, tokenizer, config.max_length)
            
            # Train model
            training_results = await self._train_model(
                model, tokenizer, train_dataset, val_dataset, config, "classification"
            )
            
            # Save model and label mapping
            model_path = self.models_dir / "classification_model"
            model.save_pretrained(model_path)
            tokenizer.save_pretrained(model_path)
            
            # Save label mapping
            with open(model_path / "label_map.json", "w") as f:
                json.dump(label_map, f, indent=2)
            
            training_results['label_map'] = label_map
            training_results['num_labels'] = num_labels
            
            logger.info("✅ Classification model training completed")
            return training_results
            
        except Exception as e:
            logger.error(f"❌ Classification model training failed: {e}")
            raise
    
    async def train_ner_model(self, training_data: List[Dict[str, Any]], config: TrainingConfig) -> Dict[str, Any]:
        """Train Named Entity Recognition model"""
        logger.info("Starting NER model training...")
        
        try:
            # Prepare NER data (tokens and labels)
            tokenized_data = self._prepare_ner_data(training_data)
            
            # Split data
            train_data, val_data = train_test_split(
                tokenized_data, test_size=config.validation_split, random_state=42
            )
            
            # Get unique labels
            all_labels = set()
            for item in tokenized_data:
                all_labels.update(item['labels'])
            
            label_list = sorted(list(all_labels))
            label_map = {label: idx for idx, label in enumerate(label_list)}
            
            # Load tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(config.model_name)
            model = AutoModelForTokenClassification.from_pretrained(
                config.model_name,
                num_labels=len(label_list)
            )
            
            # Create datasets
            train_dataset = NERDataset(train_data, tokenizer, label_map, config.max_length)
            val_dataset = NERDataset(val_data, tokenizer, label_map, config.max_length)
            
            # Train model
            training_results = await self._train_ner_model(
                model, tokenizer, train_dataset, val_dataset, config
            )
            
            # Save model
            model_path = self.models_dir / "ner_model"
            model.save_pretrained(model_path)
            tokenizer.save_pretrained(model_path)
            
            # Save label mapping
            with open(model_path / "label_map.json", "w") as f:
                json.dump(label_map, f, indent=2)
            
            training_results['label_map'] = label_map
            
            logger.info("✅ NER model training completed")
            return training_results
            
        except Exception as e:
            logger.error(f"❌ NER model training failed: {e}")
            raise
    
    async def _train_model(self, model, tokenizer, train_dataset, val_dataset, config: TrainingConfig, model_type: str) -> Dict[str, Any]:
        """Generic model training function"""
        
        # Move model to device
        model.to(self.device)
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=config.batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=config.batch_size)
        
        # Setup optimizer and scheduler
        optimizer = AdamW(
            model.parameters(),
            lr=config.learning_rate,
            weight_decay=config.weight_decay
        )
        
        total_steps = len(train_loader) * config.num_epochs
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=config.warmup_steps,
            num_training_steps=total_steps
        )
        
        # Training loop
        training_history = {
            'train_losses': [],
            'val_losses': [],
            'val_accuracies': [],
            'learning_rates': []
        }
        
        best_val_accuracy = 0.0
        
        for epoch in range(config.num_epochs):
            # Training phase
            model.train()
            total_train_loss = 0
            
            for batch_idx, batch in enumerate(train_loader):
                optimizer.zero_grad()
                
                # Move batch to device
                inputs = {k: v.to(self.device) for k, v in batch.items()}
                
                # Forward pass
                outputs = model(**inputs)
                loss = outputs.loss
                
                # Backward pass
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
                scheduler.step()
                
                total_train_loss += loss.item()
                
                # Log progress
                if batch_idx % 50 == 0:
                    logger.info(f"Epoch {epoch+1}/{config.num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")
            
            avg_train_loss = total_train_loss / len(train_loader)
            training_history['train_losses'].append(avg_train_loss)
            training_history['learning_rates'].append(scheduler.get_last_lr()[0])
            
            # Validation phase
            val_loss, val_accuracy, val_report = await self._evaluate_model(model, val_loader)
            training_history['val_losses'].append(val_loss)
            training_history['val_accuracies'].append(val_accuracy)
            
            logger.info(f"Epoch {epoch+1} - Train Loss: {avg_train_loss:.4f}, Val Loss: {val_loss:.4f}, Val Accuracy: {val_accuracy:.4f}")
            
            # Save best model
            if val_accuracy > best_val_accuracy:
                best_val_accuracy = val_accuracy
                best_model_path = self.models_dir / f"best_{model_type}_model"
                model.save_pretrained(best_model_path)
                tokenizer.save_pretrained(best_model_path)
        
        # Generate training plots
        plots_path = await self._generate_training_plots(training_history, model_type)
        
        return {
            'training_history': training_history,
            'best_val_accuracy': best_val_accuracy,
            'final_val_report': val_report,
            'plots_path': str(plots_path),
            'model_path': str(best_model_path)
        }
    
    async def _evaluate_model(self, model, data_loader) -> Tuple[float, float, str]:
        """Evaluate model performance"""
        model.eval()
        total_loss = 0
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for batch in data_loader:
                inputs = {k: v.to(self.device) for k, v in batch.items() if k != 'labels'}
                labels = batch['labels'].to(self.device)
                
                outputs = model(**inputs, labels=labels)
                loss = outputs.loss
                
                total_loss += loss.item()
                
                predictions = torch.argmax(outputs.logits, dim=-1)
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        avg_loss = total_loss / len(data_loader)
        accuracy = accuracy_score(all_labels, all_predictions)
        report = classification_report(all_labels, all_predictions)
        
        return avg_loss, accuracy, report
    
    async def _generate_training_plots(self, history: Dict[str, List], model_type: str) -> Path:
        """Generate training visualization plots"""
        
        def create_plots():
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'{model_type.title()} Model Training Results', fontsize=16)
            
            # Training and validation loss
            axes[0, 0].plot(history['train_losses'], label='Training Loss', color='blue')
            axes[0, 0].plot(history['val_losses'], label='Validation Loss', color='red')
            axes[0, 0].set_title('Training and Validation Loss')
            axes[0, 0].set_xlabel('Epoch')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].legend()
            axes[0, 0].grid(True)
            
            # Validation accuracy
            axes[0, 1].plot(history['val_accuracies'], label='Validation Accuracy', color='green')
            axes[0, 1].set_title('Validation Accuracy')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('Accuracy')
            axes[0, 1].legend()
            axes[0, 1].grid(True)
            
            # Learning rate schedule
            axes[1, 0].plot(history['learning_rates'], label='Learning Rate', color='orange')
            axes[1, 0].set_title('Learning Rate Schedule')
            axes[1, 0].set_xlabel('Step')
            axes[1, 0].set_ylabel('Learning Rate')
            axes[1, 0].legend()
            axes[1, 0].grid(True)
            
            # Loss comparison
            axes[1, 1].bar(['Train Loss', 'Val Loss'], 
                          [history['train_losses'][-1], history['val_losses'][-1]],
                          color=['blue', 'red'], alpha=0.7)
            axes[1, 1].set_title('Final Loss Comparison')
            axes[1, 1].set_ylabel('Loss')
            
            plt.tight_layout()
            
            # Save plot
            plots_dir = self.models_dir / "training_plots"
            plots_dir.mkdir(exist_ok=True)
            plot_path = plots_dir / f"{model_type}_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return plot_path
        
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        plot_path = await loop.run_in_executor(self.executor, create_plots)
        
        return plot_path
    
    def _sentiment_to_label(self, sentiment: str) -> int:
        """Convert sentiment string to label"""
        sentiment_map = {
            'negative': 0,
            'neutral': 1,
            'positive': 2
        }
        return sentiment_map.get(sentiment.lower(), 1)  # Default to neutral
    
    def _prepare_ner_data(self, training_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prepare data for NER training"""
        prepared_data = []
        
        for item in training_data:
            tokens = item['tokens']
            labels = item['labels']
            
            # Ensure tokens and labels have same length
            if len(tokens) == len(labels):
                prepared_data.append({
                    'tokens': tokens,
                    'labels': labels
                })
        
        return prepared_data
    
    async def fine_tune_existing_model(self, model_path: str, training_data: List[Dict[str, Any]], config: TrainingConfig) -> Dict[str, Any]:
        """Fine-tune an existing model"""
        logger.info(f"Fine-tuning model from {model_path}...")
        
        try:
            # Load existing model
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForSequenceClassification.from_pretrained(model_path)
            
            # Prepare data
            texts = [item['text'] for item in training_data]
            labels = [item['label'] for item in training_data]
            
            # Split data
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, labels, test_size=config.validation_split, random_state=42
            )
            
            # Create datasets
            train_dataset = MeetingDataset(train_texts, train_labels, tokenizer, config.max_length)
            val_dataset = MeetingDataset(val_texts, val_labels, tokenizer, config.max_length)
            
            # Fine-tune with lower learning rate
            config.learning_rate = config.learning_rate / 10  # Reduce learning rate for fine-tuning
            
            # Train model
            training_results = await self._train_model(
                model, tokenizer, train_dataset, val_dataset, config, "fine_tuned"
            )
            
            logger.info("✅ Model fine-tuning completed")
            return training_results
            
        except Exception as e:
            logger.error(f"❌ Model fine-tuning failed: {e}")
            raise
    
    async def evaluate_model_performance(self, model_path: str, test_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Evaluate model performance on test data"""
        logger.info(f"Evaluating model from {model_path}...")
        
        try:
            # Load model
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForSequenceClassification.from_pretrained(model_path)
            model.to(self.device)
            model.eval()
            
            # Prepare test data
            texts = [item['text'] for item in test_data]
            true_labels = [item['label'] for item in test_data]
            
            # Create dataset
            test_dataset = MeetingDataset(texts, true_labels, tokenizer)
            test_loader = DataLoader(test_dataset, batch_size=16)
            
            # Evaluate
            test_loss, test_accuracy, test_report = await self._evaluate_model(model, test_loader)
            
            # Generate confusion matrix
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for batch in test_loader:
                    inputs = {k: v.to(self.device) for k, v in batch.items() if k != 'labels'}
                    labels = batch['labels'].to(self.device)
                    
                    outputs = model(**inputs)
                    predictions = torch.argmax(outputs.logits, dim=-1)
                    
                    all_predictions.extend(predictions.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
            
            # Create confusion matrix plot
            cm = confusion_matrix(all_labels, all_predictions)
            
            plt.figure(figsize=(8, 6))
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
            plt.title('Confusion Matrix')
            plt.ylabel('True Label')
            plt.xlabel('Predicted Label')
            
            cm_path = self.models_dir / "confusion_matrix.png"
            plt.savefig(cm_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return {
                'test_loss': test_loss,
                'test_accuracy': test_accuracy,
                'classification_report': test_report,
                'confusion_matrix_path': str(cm_path),
                'predictions': all_predictions,
                'true_labels': all_labels
            }
            
        except Exception as e:
            logger.error(f"❌ Model evaluation failed: {e}")
            raise
    
    def get_training_history(self, model_type: str) -> Optional[Dict[str, Any]]:
        """Get training history for a model type"""
        return self.training_history.get(model_type)
    
    async def export_model_for_inference(self, model_path: str, export_format: str = "onnx") -> str:
        """Export model for optimized inference"""
        logger.info(f"Exporting model to {export_format} format...")
        
        try:
            if export_format.lower() == "onnx":
                # Export to ONNX format
                import torch.onnx
                
                # Load model
                tokenizer = AutoTokenizer.from_pretrained(model_path)
                model = AutoModelForSequenceClassification.from_pretrained(model_path)
                model.eval()
                
                # Create dummy input
                dummy_input = tokenizer("Sample text for export", return_tensors="pt", max_length=512, padding="max_length", truncation=True)
                
                # Export path
                export_path = Path(model_path) / "model.onnx"
                
                # Export to ONNX
                torch.onnx.export(
                    model,
                    (dummy_input['input_ids'], dummy_input['attention_mask']),
                    str(export_path),
                    export_params=True,
                    opset_version=11,
                    do_constant_folding=True,
                    input_names=['input_ids', 'attention_mask'],
                    output_names=['logits'],
                    dynamic_axes={
                        'input_ids': {0: 'batch_size'},
                        'attention_mask': {0: 'batch_size'},
                        'logits': {0: 'batch_size'}
                    }
                )
                
                logger.info(f"✅ Model exported to {export_path}")
                return str(export_path)
            
            else:
                raise ValueError(f"Unsupported export format: {export_format}")
                
        except Exception as e:
            logger.error(f"❌ Model export failed: {e}")
            raise

class NERDataset(Dataset):
    """Dataset for NER training"""
    
    def __init__(self, data: List[Dict], tokenizer, label_map: Dict[str, int], max_length: int = 512):
        self.data = data
        self.tokenizer = tokenizer
        self.label_map = label_map
        self.max_length = max_length
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        tokens = item['tokens']
        labels = item['labels']
        
        # Tokenize and align labels
        encoding = self.tokenizer(
            tokens,
            is_split_into_words=True,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        # Align labels with tokenized input
        word_ids = encoding.word_ids()
        aligned_labels = []
        
        for word_id in word_ids:
            if word_id is None:
                aligned_labels.append(-100)  # Special token
            else:
                aligned_labels.append(self.label_map.get(labels[word_id], 0))
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(aligned_labels, dtype=torch.long)
        }
