{"ast": null, "code": "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "map": {"version": 3, "names": ["viewport", "getViewportRect", "getDocumentRect", "listScrollParents", "getOffsetParent", "getDocumentElement", "getComputedStyle", "isElement", "isHTMLElement", "getBoundingClientRect", "getParentNode", "contains", "getNodeName", "rectToClientRect", "max", "min", "getInnerBoundingClientRect", "element", "strategy", "rect", "top", "clientTop", "left", "clientLeft", "bottom", "clientHeight", "right", "clientWidth", "width", "height", "x", "y", "getClientRectFromMixedType", "clippingParent", "getClippingParents", "clippingParents", "canEscapeClipping", "indexOf", "position", "clipperElement", "filter", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "concat", "firstClippingParent", "clippingRect", "reduce", "accRect"], "sources": ["C:/Users/<USER>/Documents/augment-projects/smartcoverage/node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js"], "sourcesContent": ["import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,aAAa;AACtC,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAC1D,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,GAAG,EAAEC,GAAG,QAAQ,kBAAkB;AAE3C,SAASC,0BAA0BA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACrD,IAAIC,IAAI,GAAGV,qBAAqB,CAACQ,OAAO,EAAE,KAAK,EAAEC,QAAQ,KAAK,OAAO,CAAC;EACtEC,IAAI,CAACC,GAAG,GAAGD,IAAI,CAACC,GAAG,GAAGH,OAAO,CAACI,SAAS;EACvCF,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACG,IAAI,GAAGL,OAAO,CAACM,UAAU;EAC1CJ,IAAI,CAACK,MAAM,GAAGL,IAAI,CAACC,GAAG,GAAGH,OAAO,CAACQ,YAAY;EAC7CN,IAAI,CAACO,KAAK,GAAGP,IAAI,CAACG,IAAI,GAAGL,OAAO,CAACU,WAAW;EAC5CR,IAAI,CAACS,KAAK,GAAGX,OAAO,CAACU,WAAW;EAChCR,IAAI,CAACU,MAAM,GAAGZ,OAAO,CAACQ,YAAY;EAClCN,IAAI,CAACW,CAAC,GAAGX,IAAI,CAACG,IAAI;EAClBH,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACC,GAAG;EACjB,OAAOD,IAAI;AACb;AAEA,SAASa,0BAA0BA,CAACf,OAAO,EAAEgB,cAAc,EAAEf,QAAQ,EAAE;EACrE,OAAOe,cAAc,KAAKjC,QAAQ,GAAGa,gBAAgB,CAACZ,eAAe,CAACgB,OAAO,EAAEC,QAAQ,CAAC,CAAC,GAAGX,SAAS,CAAC0B,cAAc,CAAC,GAAGjB,0BAA0B,CAACiB,cAAc,EAAEf,QAAQ,CAAC,GAAGL,gBAAgB,CAACX,eAAe,CAACG,kBAAkB,CAACY,OAAO,CAAC,CAAC,CAAC;AAC/O,CAAC,CAAC;AACF;AACA;;AAGA,SAASiB,kBAAkBA,CAACjB,OAAO,EAAE;EACnC,IAAIkB,eAAe,GAAGhC,iBAAiB,CAACO,aAAa,CAACO,OAAO,CAAC,CAAC;EAC/D,IAAImB,iBAAiB,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAACC,OAAO,CAAC/B,gBAAgB,CAACW,OAAO,CAAC,CAACqB,QAAQ,CAAC,IAAI,CAAC;EAC9F,IAAIC,cAAc,GAAGH,iBAAiB,IAAI5B,aAAa,CAACS,OAAO,CAAC,GAAGb,eAAe,CAACa,OAAO,CAAC,GAAGA,OAAO;EAErG,IAAI,CAACV,SAAS,CAACgC,cAAc,CAAC,EAAE;IAC9B,OAAO,EAAE;EACX,CAAC,CAAC;;EAGF,OAAOJ,eAAe,CAACK,MAAM,CAAC,UAAUP,cAAc,EAAE;IACtD,OAAO1B,SAAS,CAAC0B,cAAc,CAAC,IAAItB,QAAQ,CAACsB,cAAc,EAAEM,cAAc,CAAC,IAAI3B,WAAW,CAACqB,cAAc,CAAC,KAAK,MAAM;EACxH,CAAC,CAAC;AACJ,CAAC,CAAC;AACF;;AAGA,eAAe,SAASQ,eAAeA,CAACxB,OAAO,EAAEyB,QAAQ,EAAEC,YAAY,EAAEzB,QAAQ,EAAE;EACjF,IAAI0B,mBAAmB,GAAGF,QAAQ,KAAK,iBAAiB,GAAGR,kBAAkB,CAACjB,OAAO,CAAC,GAAG,EAAE,CAAC4B,MAAM,CAACH,QAAQ,CAAC;EAC5G,IAAIP,eAAe,GAAG,EAAE,CAACU,MAAM,CAACD,mBAAmB,EAAE,CAACD,YAAY,CAAC,CAAC;EACpE,IAAIG,mBAAmB,GAAGX,eAAe,CAAC,CAAC,CAAC;EAC5C,IAAIY,YAAY,GAAGZ,eAAe,CAACa,MAAM,CAAC,UAAUC,OAAO,EAAEhB,cAAc,EAAE;IAC3E,IAAId,IAAI,GAAGa,0BAA0B,CAACf,OAAO,EAAEgB,cAAc,EAAEf,QAAQ,CAAC;IACxE+B,OAAO,CAAC7B,GAAG,GAAGN,GAAG,CAACK,IAAI,CAACC,GAAG,EAAE6B,OAAO,CAAC7B,GAAG,CAAC;IACxC6B,OAAO,CAACvB,KAAK,GAAGX,GAAG,CAACI,IAAI,CAACO,KAAK,EAAEuB,OAAO,CAACvB,KAAK,CAAC;IAC9CuB,OAAO,CAACzB,MAAM,GAAGT,GAAG,CAACI,IAAI,CAACK,MAAM,EAAEyB,OAAO,CAACzB,MAAM,CAAC;IACjDyB,OAAO,CAAC3B,IAAI,GAAGR,GAAG,CAACK,IAAI,CAACG,IAAI,EAAE2B,OAAO,CAAC3B,IAAI,CAAC;IAC3C,OAAO2B,OAAO;EAChB,CAAC,EAAEjB,0BAA0B,CAACf,OAAO,EAAE6B,mBAAmB,EAAE5B,QAAQ,CAAC,CAAC;EACtE6B,YAAY,CAACnB,KAAK,GAAGmB,YAAY,CAACrB,KAAK,GAAGqB,YAAY,CAACzB,IAAI;EAC3DyB,YAAY,CAAClB,MAAM,GAAGkB,YAAY,CAACvB,MAAM,GAAGuB,YAAY,CAAC3B,GAAG;EAC5D2B,YAAY,CAACjB,CAAC,GAAGiB,YAAY,CAACzB,IAAI;EAClCyB,YAAY,CAAChB,CAAC,GAAGgB,YAAY,CAAC3B,GAAG;EACjC,OAAO2B,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}