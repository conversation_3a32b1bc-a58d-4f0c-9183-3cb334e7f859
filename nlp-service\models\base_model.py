import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer, AutoConfig
from abc import ABC, abstractmethod
import logging
from pathlib import Path
import json
from typing import Dict, List, Any, Optional, Tuple
import numpy as np

logger = logging.getLogger(__name__)

class BaseNLPModel(ABC):
    """Base class for all NLP models"""
    
    def __init__(self, model_name: str, model_path: Optional[Path] = None):
        self.model_name = model_name
        self.model_path = model_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        self.tokenizer = None
        self.config = None
        
    @abstractmethod
    def load_model(self):
        """Load the model and tokenizer"""
        pass
    
    @abstractmethod
    def predict(self, text: str) -> Dict[str, Any]:
        """Make predictions on input text"""
        pass
    
    @abstractmethod
    def train(self, train_data: List[Dict], validation_data: Optional[List[Dict]] = None):
        """Train the model on provided data"""
        pass
    
    def save_model(self, save_path: Path):
        """Save the model to disk"""
        save_path.mkdir(parents=True, exist_ok=True)
        
        if self.model:
            self.model.save_pretrained(save_path)
        if self.tokenizer:
            self.tokenizer.save_pretrained(save_path)
        if self.config:
            with open(save_path / "config.json", "w") as f:
                json.dump(self.config, f, indent=2)
                
        logger.info(f"Model saved to {save_path}")
    
    def load_from_path(self, load_path: Path):
        """Load model from disk"""
        if not load_path.exists():
            raise FileNotFoundError(f"Model path {load_path} does not exist")
        
        self.tokenizer = AutoTokenizer.from_pretrained(load_path)
        self.model = AutoModel.from_pretrained(load_path)
        
        config_path = load_path / "config.json"
        if config_path.exists():
            with open(config_path, "r") as f:
                self.config = json.load(f)
        
        self.model.to(self.device)
        logger.info(f"Model loaded from {load_path}")

class MeetingClassificationModel(BaseNLPModel):
    """Custom model for meeting content classification"""
    
    def __init__(self, model_name: str, num_labels: int = 10, model_path: Optional[Path] = None):
        super().__init__(model_name, model_path)
        self.num_labels = num_labels
        self.label_map = {}
        
    def load_model(self):
        """Load pre-trained model and add classification head"""
        try:
            if self.model_path and self.model_path.exists():
                self.load_from_path(self.model_path)
            else:
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                base_model = AutoModel.from_pretrained(self.model_name)
                
                # Create classification model
                self.model = MeetingClassifier(base_model, self.num_labels)
                self.model.to(self.device)
                
            logger.info(f"Classification model loaded: {self.model_name}")
            
        except Exception as e:
            logger.error(f"Error loading classification model: {e}")
            raise
    
    def predict(self, text: str) -> Dict[str, Any]:
        """Classify meeting content"""
        if not self.model or not self.tokenizer:
            raise ValueError("Model not loaded")
        
        # Tokenize input
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        ).to(self.device)
        
        # Get predictions
        with torch.no_grad():
            outputs = self.model(**inputs)
            probabilities = torch.softmax(outputs.logits, dim=-1)
            predictions = torch.argmax(probabilities, dim=-1)
        
        # Convert to readable format
        results = {
            "predicted_class": predictions.item(),
            "confidence": probabilities.max().item(),
            "all_probabilities": probabilities.cpu().numpy().tolist()[0]
        }
        
        # Map to labels if available
        if self.label_map:
            results["predicted_label"] = self.label_map.get(predictions.item(), "unknown")
        
        return results
    
    def train(self, train_data: List[Dict], validation_data: Optional[List[Dict]] = None):
        """Train the classification model"""
        from torch.utils.data import DataLoader
        from transformers import AdamW, get_linear_schedule_with_warmup
        
        # Prepare data
        train_dataset = MeetingDataset(train_data, self.tokenizer)
        train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
        
        val_loader = None
        if validation_data:
            val_dataset = MeetingDataset(validation_data, self.tokenizer)
            val_loader = DataLoader(val_dataset, batch_size=16)
        
        # Setup optimizer and scheduler
        optimizer = AdamW(self.model.parameters(), lr=2e-5)
        total_steps = len(train_loader) * 3  # 3 epochs
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=100,
            num_training_steps=total_steps
        )
        
        # Training loop
        self.model.train()
        for epoch in range(3):
            total_loss = 0
            for batch in train_loader:
                optimizer.zero_grad()
                
                inputs = {k: v.to(self.device) for k, v in batch.items() if k != 'labels'}
                labels = batch['labels'].to(self.device)
                
                outputs = self.model(**inputs, labels=labels)
                loss = outputs.loss
                
                loss.backward()
                optimizer.step()
                scheduler.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / len(train_loader)
            logger.info(f"Epoch {epoch + 1}, Average Loss: {avg_loss:.4f}")
            
            # Validation
            if val_loader:
                val_accuracy = self._evaluate(val_loader)
                logger.info(f"Validation Accuracy: {val_accuracy:.4f}")

    def _evaluate(self, data_loader):
        """Evaluate model on validation data"""
        self.model.eval()
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch in data_loader:
                inputs = {k: v.to(self.device) for k, v in batch.items() if k != 'labels'}
                labels = batch['labels'].to(self.device)
                
                outputs = self.model(**inputs)
                predictions = torch.argmax(outputs.logits, dim=-1)
                
                correct += (predictions == labels).sum().item()
                total += labels.size(0)
        
        return correct / total

class MeetingClassifier(nn.Module):
    """Neural network for meeting classification"""
    
    def __init__(self, base_model, num_labels: int, dropout_rate: float = 0.1):
        super().__init__()
        self.base_model = base_model
        self.dropout = nn.Dropout(dropout_rate)
        self.classifier = nn.Linear(base_model.config.hidden_size, num_labels)
        self.num_labels = num_labels
        
    def forward(self, input_ids, attention_mask=None, labels=None):
        outputs = self.base_model(input_ids=input_ids, attention_mask=attention_mask)
        
        # Use [CLS] token representation
        pooled_output = outputs.last_hidden_state[:, 0]
        pooled_output = self.dropout(pooled_output)
        logits = self.classifier(pooled_output)
        
        loss = None
        if labels is not None:
            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(logits.view(-1, self.num_labels), labels.view(-1))
        
        return type('ModelOutput', (), {
            'loss': loss,
            'logits': logits,
            'hidden_states': outputs.hidden_states if hasattr(outputs, 'hidden_states') else None,
            'attentions': outputs.attentions if hasattr(outputs, 'attentions') else None
        })()

class MeetingDataset(torch.utils.data.Dataset):
    """Dataset class for meeting data"""
    
    def __init__(self, data: List[Dict], tokenizer, max_length: int = 512):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text = item['text']
        label = item.get('label', 0)
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }
