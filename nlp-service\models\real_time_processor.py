import asyncio
import torch
import torch.nn as nn
from transformers import <PERSON>Tokenizer, AutoModel, pipeline
from sentence_transformers import SentenceTransformer
import numpy as np
from typing import Dict, List, Any, Optional, AsyncGenerator
import logging
from pathlib import Path
import json
import time
from datetime import datetime
import websockets
import redis
from concurrent.futures import ThreadPoolExecutor
import queue
import threading
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ProcessingResult:
    """Data class for processing results"""
    text: str
    sentiment: Dict[str, Any]
    tags: List[Dict[str, Any]]
    summary: str
    entities: List[Dict[str, Any]]
    confidence_score: float
    processing_time: float
    timestamp: datetime

class RealTimeNLPProcessor:
    """Real-time NLP processing engine with deep learning models"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.models = {}
        self.pipelines = {}
        self.processing_queue = queue.Queue()
        self.result_cache = {}
        self.is_running = False
        
        # Redis for real-time communication
        self.redis_client = redis.Redis(
            host=config.get('redis_host', 'localhost'),
            port=config.get('redis_port', 6379),
            decode_responses=True
        )
        
        # Thread pool for parallel processing
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # WebSocket connections
        self.websocket_connections = set()
        
    async def initialize(self):
        """Initialize all models and services"""
        logger.info("Initializing Real-Time NLP Processor...")
        
        try:
            # Load sentiment analysis model
            await self._load_sentiment_model()
            
            # Load NER model
            await self._load_ner_model()
            
            # Load summarization model
            await self._load_summarization_model()
            
            # Load embedding model
            await self._load_embedding_model()
            
            # Load custom classification model
            await self._load_classification_model()
            
            # Start processing workers
            self._start_processing_workers()
            
            logger.info("✅ Real-Time NLP Processor initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize NLP Processor: {e}")
            raise
    
    async def _load_sentiment_model(self):
        """Load sentiment analysis model"""
        model_name = "cardiffnlp/twitter-roberta-base-sentiment-latest"
        
        self.pipelines['sentiment'] = pipeline(
            "sentiment-analysis",
            model=model_name,
            device=0 if torch.cuda.is_available() else -1,
            return_all_scores=True
        )
        
        logger.info("✅ Sentiment model loaded")
    
    async def _load_ner_model(self):
        """Load Named Entity Recognition model"""
        model_name = "dbmdz/bert-large-cased-finetuned-conll03-english"
        
        self.pipelines['ner'] = pipeline(
            "ner",
            model=model_name,
            tokenizer=model_name,
            device=0 if torch.cuda.is_available() else -1,
            aggregation_strategy="simple"
        )
        
        logger.info("✅ NER model loaded")
    
    async def _load_summarization_model(self):
        """Load summarization model"""
        model_name = "facebook/bart-large-cnn"
        
        self.pipelines['summarization'] = pipeline(
            "summarization",
            model=model_name,
            device=0 if torch.cuda.is_available() else -1
        )
        
        logger.info("✅ Summarization model loaded")
    
    async def _load_embedding_model(self):
        """Load sentence embedding model"""
        self.models['embeddings'] = SentenceTransformer('all-MiniLM-L6-v2')
        logger.info("✅ Embedding model loaded")
    
    async def _load_classification_model(self):
        """Load custom meeting classification model"""
        try:
            # Load custom fine-tuned model if available
            model_path = Path("models/meeting_classifier")
            if model_path.exists():
                self.models['classifier'] = MeetingClassificationModel.load_from_path(model_path)
            else:
                # Use base model
                self.models['classifier'] = MeetingClassificationModel(
                    "distilbert-base-uncased",
                    num_labels=10
                )
                await self.models['classifier'].load_model()
            
            logger.info("✅ Classification model loaded")
            
        except Exception as e:
            logger.warning(f"Classification model not available: {e}")
    
    def _start_processing_workers(self):
        """Start background processing workers"""
        self.is_running = True
        
        # Start processing threads
        for i in range(2):
            thread = threading.Thread(
                target=self._processing_worker,
                name=f"NLP-Worker-{i}",
                daemon=True
            )
            thread.start()
        
        logger.info("✅ Processing workers started")
    
    def _processing_worker(self):
        """Background worker for processing tasks"""
        while self.is_running:
            try:
                # Get task from queue with timeout
                task = self.processing_queue.get(timeout=1.0)
                
                # Process the task
                result = self._process_text_sync(task['text'], task['options'])
                
                # Store result
                task_id = task['id']
                self.result_cache[task_id] = result
                
                # Publish result to Redis
                self.redis_client.publish(
                    f"nlp_result_{task_id}",
                    json.dumps(result, default=str)
                )
                
                # Send to WebSocket clients
                asyncio.create_task(self._broadcast_result(task_id, result))
                
                self.processing_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in processing worker: {e}")
    
    async def process_text_async(self, text: str, options: Dict[str, Any] = None) -> str:
        """Process text asynchronously and return task ID"""
        task_id = f"task_{int(time.time() * 1000)}_{hash(text) % 10000}"
        
        # Add to processing queue
        self.processing_queue.put({
            'id': task_id,
            'text': text,
            'options': options or {},
            'timestamp': datetime.now()
        })
        
        return task_id
    
    def _process_text_sync(self, text: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Synchronous text processing"""
        start_time = time.time()
        
        try:
            # Sentiment analysis
            sentiment_result = self._analyze_sentiment(text)
            
            # Named entity recognition
            entities = self._extract_entities(text)
            
            # Tag extraction
            tags = self._extract_tags(text)
            
            # Summarization (if text is long enough)
            summary = self._generate_summary(text) if len(text) > 200 else text[:100] + "..."
            
            # Classification
            classification = self._classify_text(text)
            
            # Generate embeddings
            embeddings = self._generate_embeddings(text)
            
            processing_time = time.time() - start_time
            
            result = {
                'text': text[:200] + "..." if len(text) > 200 else text,
                'sentiment': sentiment_result,
                'entities': entities,
                'tags': tags,
                'summary': summary,
                'classification': classification,
                'embeddings': embeddings.tolist() if isinstance(embeddings, np.ndarray) else embeddings,
                'processing_time': processing_time,
                'timestamp': datetime.now().isoformat(),
                'confidence_score': self._calculate_overall_confidence(sentiment_result, entities, tags)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing text: {e}")
            return {
                'error': str(e),
                'processing_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            }
    
    def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of text"""
        try:
            results = self.pipelines['sentiment'](text)
            
            # Convert to standardized format
            sentiment_scores = {}
            for result in results[0]:  # First result contains all scores
                label = result['label'].lower()
                if 'positive' in label:
                    sentiment_scores['positive'] = result['score']
                elif 'negative' in label:
                    sentiment_scores['negative'] = result['score']
                else:
                    sentiment_scores['neutral'] = result['score']
            
            # Determine overall sentiment
            max_score = max(sentiment_scores.values())
            overall_sentiment = max(sentiment_scores, key=sentiment_scores.get)
            
            return {
                'overall': overall_sentiment,
                'confidence': max_score,
                'scores': sentiment_scores,
                'sentiment_score': self._calculate_sentiment_score(sentiment_scores)
            }
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            return {'overall': 'neutral', 'confidence': 0.0, 'scores': {}, 'sentiment_score': 0.0}
    
    def _extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract named entities"""
        try:
            entities = self.pipelines['ner'](text)
            
            processed_entities = []
            for entity in entities:
                processed_entities.append({
                    'text': entity['word'],
                    'label': entity['entity_group'],
                    'confidence': entity['score'],
                    'start': entity.get('start', 0),
                    'end': entity.get('end', 0)
                })
            
            return processed_entities
            
        except Exception as e:
            logger.error(f"Entity extraction failed: {e}")
            return []
    
    def _extract_tags(self, text: str) -> List[Dict[str, Any]]:
        """Extract tags using multiple methods"""
        tags = []
        
        # Financial keywords
        financial_keywords = [
            'investment', 'portfolio', 'roi', 'return', 'profit', 'revenue',
            'blockchain', 'cryptocurrency', 'bitcoin', 'ethereum', 'defi'
        ]
        
        text_lower = text.lower()
        for keyword in financial_keywords:
            if keyword in text_lower:
                tags.append({
                    'name': keyword,
                    'type': 'explicit',
                    'category': 'financial',
                    'confidence': 1.0,
                    'source': 'keyword_matching'
                })
        
        return tags[:10]  # Limit to top 10 tags
    
    def _generate_summary(self, text: str) -> str:
        """Generate text summary"""
        try:
            if len(text) < 100:
                return text
            
            # Adjust summary length based on input
            max_length = min(150, len(text.split()) // 3)
            min_length = min(50, max_length // 2)
            
            summary_result = self.pipelines['summarization'](
                text,
                max_length=max_length,
                min_length=min_length,
                do_sample=False
            )
            
            return summary_result[0]['summary_text']
            
        except Exception as e:
            logger.error(f"Summarization failed: {e}")
            return text[:200] + "..." if len(text) > 200 else text
    
    def _classify_text(self, text: str) -> Dict[str, Any]:
        """Classify meeting content"""
        try:
            if 'classifier' in self.models:
                result = self.models['classifier'].predict(text)
                return result
            else:
                return {'predicted_class': 0, 'confidence': 0.5}
                
        except Exception as e:
            logger.error(f"Classification failed: {e}")
            return {'predicted_class': 0, 'confidence': 0.0}
    
    def _generate_embeddings(self, text: str) -> np.ndarray:
        """Generate sentence embeddings"""
        try:
            embeddings = self.models['embeddings'].encode(text)
            return embeddings
            
        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            return np.zeros(384)  # Default embedding size
    
    def _calculate_sentiment_score(self, scores: Dict[str, float]) -> float:
        """Calculate normalized sentiment score (-1 to 1)"""
        positive = scores.get('positive', 0)
        negative = scores.get('negative', 0)
        neutral = scores.get('neutral', 0)
        
        return positive - negative
    
    def _calculate_overall_confidence(self, sentiment: Dict, entities: List, tags: List) -> float:
        """Calculate overall confidence score"""
        sentiment_conf = sentiment.get('confidence', 0)
        entity_conf = np.mean([e['confidence'] for e in entities]) if entities else 0
        tag_conf = np.mean([t['confidence'] for t in tags]) if tags else 0
        
        return np.mean([sentiment_conf, entity_conf, tag_conf])
    
    async def get_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get processing result by task ID"""
        return self.result_cache.get(task_id)
    
    async def _broadcast_result(self, task_id: str, result: Dict[str, Any]):
        """Broadcast result to WebSocket clients"""
        if self.websocket_connections:
            message = json.dumps({
                'type': 'processing_result',
                'task_id': task_id,
                'result': result
            }, default=str)
            
            # Send to all connected clients
            disconnected = set()
            for websocket in self.websocket_connections:
                try:
                    await websocket.send(message)
                except websockets.exceptions.ConnectionClosed:
                    disconnected.add(websocket)
            
            # Remove disconnected clients
            self.websocket_connections -= disconnected
    
    async def add_websocket_connection(self, websocket):
        """Add WebSocket connection"""
        self.websocket_connections.add(websocket)
        logger.info(f"WebSocket client connected. Total: {len(self.websocket_connections)}")
    
    async def remove_websocket_connection(self, websocket):
        """Remove WebSocket connection"""
        self.websocket_connections.discard(websocket)
        logger.info(f"WebSocket client disconnected. Total: {len(self.websocket_connections)}")
    
    def shutdown(self):
        """Shutdown the processor"""
        self.is_running = False
        self.executor.shutdown(wait=True)
        logger.info("Real-Time NLP Processor shutdown complete")

class MeetingClassificationModel:
    """Custom meeting classification model"""
    
    def __init__(self, model_name: str, num_labels: int):
        self.model_name = model_name
        self.num_labels = num_labels
        self.model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    async def load_model(self):
        """Load the classification model"""
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(
            self.model_name,
            num_labels=self.num_labels
        )
        self.model.to(self.device)
        self.model.eval()
    
    def predict(self, text: str) -> Dict[str, Any]:
        """Predict meeting category"""
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        ).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(**inputs)
            probabilities = torch.softmax(outputs.logits, dim=-1)
            predicted_class = torch.argmax(probabilities, dim=-1).item()
        
        return {
            'predicted_class': predicted_class,
            'confidence': probabilities.max().item(),
            'all_probabilities': probabilities.cpu().numpy().tolist()[0]
        }
    
    @classmethod
    def load_from_path(cls, path: Path):
        """Load model from saved path"""
        # Implementation for loading saved model
        pass
