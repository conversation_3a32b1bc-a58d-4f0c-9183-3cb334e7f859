const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Tag = sequelize.define('Tag', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  meeting_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'meetings',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  tag_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 100],
    },
  },
  tag_type: {
    type: DataTypes.ENUM('explicit', 'implicit', 'manual'),
    allowNull: false,
    defaultValue: 'explicit',
  },
  confidence_score: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 1.0,
    validate: {
      min: 0,
      max: 1,
    },
  },
  category: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 50],
    },
  },
  source: {
    type: DataTypes.ENUM('nlp_model', 'rule_based', 'manual', 'hybrid'),
    allowNull: false,
    defaultValue: 'nlp_model',
  },
  context: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Context where the tag was extracted from',
  },
  relevance_score: {
    type: DataTypes.FLOAT,
    allowNull: true,
    validate: {
      min: 0,
      max: 1,
    },
  },
  frequency: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 1,
    },
  },
  is_validated: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  validated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
  validation_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
}, {
  tableName: 'tags',
  timestamps: true,
  indexes: [
    {
      fields: ['meeting_id'],
    },
    {
      fields: ['tag_name'],
    },
    {
      fields: ['tag_type'],
    },
    {
      fields: ['confidence_score'],
    },
    {
      fields: ['category'],
    },
    {
      unique: true,
      fields: ['meeting_id', 'tag_name', 'tag_type'],
    },
  ],
});

// Instance methods
Tag.prototype.validate = function(userId) {
  this.is_validated = true;
  this.validated_by = userId;
  this.validation_date = new Date();
  return this.save();
};

// Class methods
Tag.findByConfidenceRange = function(minConfidence, maxConfidence = 1.0) {
  return this.findAll({
    where: {
      confidence_score: {
        [sequelize.Sequelize.Op.between]: [minConfidence, maxConfidence],
      },
    },
    order: [['confidence_score', 'DESC']],
  });
};

Tag.findByCategory = function(category) {
  return this.findAll({
    where: {
      category: category,
    },
    order: [['confidence_score', 'DESC']],
  });
};

Tag.getTopTags = function(limit = 10) {
  return this.findAll({
    attributes: [
      'tag_name',
      [sequelize.fn('COUNT', sequelize.col('tag_name')), 'count'],
      [sequelize.fn('AVG', sequelize.col('confidence_score')), 'avg_confidence'],
    ],
    group: ['tag_name'],
    order: [[sequelize.literal('count'), 'DESC']],
    limit: limit,
  });
};

Tag.getTagsByMeeting = function(meetingId) {
  return this.findAll({
    where: {
      meeting_id: meetingId,
    },
    order: [['confidence_score', 'DESC']],
  });
};

module.exports = Tag;
