const axios = require('axios');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');
const nlpService = require('./nlpService');
const Meeting = require('../models/Meeting');

class ZoomNLPService {
  constructor() {
    this.apiKey = process.env.ZOOM_API_KEY;
    this.apiSecret = process.env.ZOOM_API_SECRET;
    this.baseURL = 'https://api.zoom.us/v2';
    this.nlpServiceUrl = process.env.NLP_SERVICE_URL || 'http://localhost:8000';
    this.jwtToken = null;
    this.tokenExpiry = null;
    this.activeMeetings = new Map(); // Track active meetings for real-time processing
  }

  // Generate JWT token for Zoom API
  generateJWT() {
    const payload = {
      iss: this.apiKey,
      exp: Math.floor(Date.now() / 1000) + (60 * 60)
    };
    this.jwtToken = jwt.sign(payload, this.apiSecret);
    this.tokenExpiry = Date.now() + (55 * 60 * 1000);
    return this.jwtToken;
  }

  getValidToken() {
    if (!this.jwtToken || Date.now() >= this.tokenExpiry) {
      return this.generateJWT();
    }
    return this.jwtToken;
  }

  async makeZoomRequest(method, endpoint, data = null) {
    try {
      const token = this.getValidToken();
      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };
      if (data) config.data = data;
      const response = await axios(config);
      return response.data;
    } catch (error) {
      logger.error('Zoom API request failed:', error.response?.data || error.message);
      throw new Error(`Zoom API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Create Zoom meeting with NLP integration
  async createMeetingWithNLP(meetingData) {
    try {
      const {
        topic,
        start_time,
        duration = 60,
        timezone = 'UTC',
        agenda = '',
        host_email,
        password,
        client_name,
        client_industry,
        meeting_type = 'client_call',
        enable_nlp_analysis = true,
        settings = {}
      } = meetingData;

      // Enhanced settings for NLP integration
      const nlpSettings = {
        host_video: true,
        participant_video: true,
        join_before_host: false,
        mute_upon_entry: true,
        waiting_room: true,
        auto_recording: enable_nlp_analysis ? 'cloud' : 'none', // Enable recording for transcription
        cloud_recording_election: enable_nlp_analysis,
        recording_authentication: false,
        auto_recording_local: false,
        auto_recording_cloud: enable_nlp_analysis,
        ...settings
      };

      const meetingPayload = {
        topic,
        type: 2,
        start_time,
        duration,
        timezone,
        agenda,
        password,
        settings: nlpSettings
      };

      const zoomMeeting = await this.makeZoomRequest('POST', `/users/${host_email}/meetings`, meetingPayload);
      
      // Create meeting record in database with NLP flags
      const dbMeeting = await Meeting.create({
        subject: topic,
        client_name,
        client_industry,
        organizer_name: host_email,
        meeting_date: new Date(start_time),
        meeting_duration_minutes: duration,
        meeting_type: 'virtual',
        meeting_platform: 'zoom',
        zoom_meeting_id: zoomMeeting.id.toString(),
        zoom_join_url: zoomMeeting.join_url,
        zoom_start_url: zoomMeeting.start_url,
        zoom_password: zoomMeeting.password,
        status: 'scheduled',
        attendees: [host_email],
        manual_tags: [`meeting_type:${meeting_type}`, `platform:zoom`]
      });

      // Initialize NLP processing pipeline for this meeting
      if (enable_nlp_analysis) {
        await this.initializeNLPPipeline(zoomMeeting.id, dbMeeting.id, {
          client_name,
          client_industry,
          meeting_type,
          expected_duration: duration
        });
      }

      logger.info(`Zoom meeting with NLP integration created: ${zoomMeeting.id}`);
      
      return {
        zoom_meeting: {
          meeting_id: zoomMeeting.id,
          join_url: zoomMeeting.join_url,
          start_url: zoomMeeting.start_url,
          password: zoomMeeting.password,
          topic: zoomMeeting.topic,
          start_time: zoomMeeting.start_time,
          duration: zoomMeeting.duration
        },
        database_meeting: dbMeeting,
        nlp_enabled: enable_nlp_analysis
      };

    } catch (error) {
      logger.error('Failed to create Zoom meeting with NLP:', error);
      throw error;
    }
  }

  // Initialize NLP processing pipeline for a meeting
  async initializeNLPPipeline(zoomMeetingId, dbMeetingId, context) {
    try {
      // Register meeting with NLP service for real-time processing
      const nlpResponse = await axios.post(`${this.nlpServiceUrl}/meetings/register`, {
        zoom_meeting_id: zoomMeetingId,
        database_meeting_id: dbMeetingId,
        context: context,
        processing_options: {
          real_time_sentiment: true,
          real_time_tags: true,
          real_time_entities: true,
          live_transcription: true,
          participant_analysis: true,
          topic_modeling: true
        }
      });

      this.activeMeetings.set(zoomMeetingId, {
        dbMeetingId,
        context,
        nlpSessionId: nlpResponse.data.session_id,
        startTime: Date.now(),
        participants: [],
        transcriptionBuffer: [],
        realTimeAnalysis: {
          sentiment_trend: [],
          key_topics: [],
          entities: [],
          engagement_score: 0
        }
      });

      logger.info(`NLP pipeline initialized for meeting: ${zoomMeetingId}`);
      return nlpResponse.data;

    } catch (error) {
      logger.error(`Failed to initialize NLP pipeline for meeting ${zoomMeetingId}:`, error);
      throw error;
    }
  }

  // Process real-time transcription data
  async processRealTimeTranscription(zoomMeetingId, transcriptionData) {
    try {
      const meetingSession = this.activeMeetings.get(zoomMeetingId);
      if (!meetingSession) {
        logger.warning(`No active session found for meeting: ${zoomMeetingId}`);
        return;
      }

      const {
        text,
        speaker,
        timestamp,
        confidence
      } = transcriptionData;

      // Add to transcription buffer
      meetingSession.transcriptionBuffer.push({
        text,
        speaker,
        timestamp,
        confidence
      });

      // Process with NLP service in real-time
      const nlpAnalysis = await nlpService.processTextRealTime(text, {
        meeting_context: meetingSession.context,
        speaker: speaker,
        timestamp: timestamp,
        session_id: meetingSession.nlpSessionId
      });

      // Update real-time analysis
      if (nlpAnalysis.sentiment) {
        meetingSession.realTimeAnalysis.sentiment_trend.push({
          timestamp,
          sentiment: nlpAnalysis.sentiment.overall,
          confidence: nlpAnalysis.sentiment.confidence,
          speaker
        });
      }

      if (nlpAnalysis.tags) {
        nlpAnalysis.tags.forEach(tag => {
          const existingTag = meetingSession.realTimeAnalysis.key_topics.find(t => t.name === tag.name);
          if (existingTag) {
            existingTag.frequency += 1;
            existingTag.confidence = Math.max(existingTag.confidence, tag.confidence);
          } else {
            meetingSession.realTimeAnalysis.key_topics.push({
              name: tag.name,
              frequency: 1,
              confidence: tag.confidence,
              category: tag.category
            });
          }
        });
      }

      if (nlpAnalysis.entities) {
        meetingSession.realTimeAnalysis.entities.push(...nlpAnalysis.entities);
      }

      // Broadcast real-time updates to connected clients
      await this.broadcastRealTimeUpdate(zoomMeetingId, {
        type: 'transcription_analysis',
        data: {
          transcription: { text, speaker, timestamp },
          analysis: nlpAnalysis,
          meeting_stats: this.getMeetingStats(meetingSession)
        }
      });

      // Process chunks every 30 seconds for deeper analysis
      if (meetingSession.transcriptionBuffer.length >= 10) {
        await this.processTranscriptionChunk(zoomMeetingId);
      }

    } catch (error) {
      logger.error(`Failed to process real-time transcription for meeting ${zoomMeetingId}:`, error);
    }
  }

  // Process accumulated transcription chunks
  async processTranscriptionChunk(zoomMeetingId) {
    try {
      const meetingSession = this.activeMeetings.get(zoomMeetingId);
      if (!meetingSession || meetingSession.transcriptionBuffer.length === 0) {
        return;
      }

      // Combine recent transcriptions
      const chunkText = meetingSession.transcriptionBuffer
        .slice(-10) // Last 10 transcriptions
        .map(t => `${t.speaker}: ${t.text}`)
        .join('\n');

      // Deep NLP analysis on chunk
      const chunkAnalysis = await nlpService.processText(chunkText, {
        meeting_context: meetingSession.context,
        chunk_analysis: true,
        session_id: meetingSession.nlpSessionId
      });

      // Update meeting insights
      await this.updateMeetingInsights(meetingSession.dbMeetingId, chunkAnalysis);

      // Clear processed buffer (keep last 5 for context)
      meetingSession.transcriptionBuffer = meetingSession.transcriptionBuffer.slice(-5);

      logger.info(`Processed transcription chunk for meeting: ${zoomMeetingId}`);

    } catch (error) {
      logger.error(`Failed to process transcription chunk for meeting ${zoomMeetingId}:`, error);
    }
  }

  // Handle Zoom webhook events with NLP integration
  async processZoomWebhookWithNLP(event) {
    try {
      const { event: eventType, payload } = event;
      
      logger.info(`Processing Zoom webhook with NLP: ${eventType}`);
      
      switch (eventType) {
        case 'meeting.started':
          await this.handleMeetingStartedWithNLP(payload);
          break;
        case 'meeting.ended':
          await this.handleMeetingEndedWithNLP(payload);
          break;
        case 'meeting.participant_joined':
          await this.handleParticipantJoinedWithNLP(payload);
          break;
        case 'meeting.participant_left':
          await this.handleParticipantLeftWithNLP(payload);
          break;
        case 'recording.completed':
          await this.handleRecordingCompletedWithNLP(payload);
          break;
        case 'meeting.live_transcription':
          await this.handleLiveTranscription(payload);
          break;
        default:
          logger.info(`Unhandled Zoom event type: ${eventType}`);
      }
    } catch (error) {
      logger.error('Failed to process Zoom webhook with NLP:', error);
      throw error;
    }
  }

  // Handle meeting started with NLP activation
  async handleMeetingStartedWithNLP(payload) {
    const { object: meeting } = payload;
    const zoomMeetingId = meeting.id.toString();
    
    logger.info(`Meeting started with NLP: ${zoomMeetingId}`);
    
    // Update database status
    await Meeting.update(
      { status: 'in_progress' },
      { where: { zoom_meeting_id: zoomMeetingId } }
    );

    // Activate real-time NLP processing
    const meetingSession = this.activeMeetings.get(zoomMeetingId);
    if (meetingSession) {
      await axios.post(`${this.nlpServiceUrl}/meetings/${meetingSession.nlpSessionId}/start`);
      
      // Start real-time monitoring
      this.startRealTimeMonitoring(zoomMeetingId);
    }
  }

  // Handle meeting ended with final NLP analysis
  async handleMeetingEndedWithNLP(payload) {
    const { object: meeting } = payload;
    const zoomMeetingId = meeting.id.toString();
    
    logger.info(`Meeting ended with NLP: ${zoomMeetingId}`);
    
    const meetingSession = this.activeMeetings.get(zoomMeetingId);
    if (meetingSession) {
      // Final NLP analysis
      await this.performFinalAnalysis(zoomMeetingId);
      
      // Update database with final results
      await Meeting.update(
        { 
          status: 'completed',
          meeting_duration_minutes: Math.round((Date.now() - meetingSession.startTime) / 60000)
        },
        { where: { zoom_meeting_id: zoomMeetingId } }
      );

      // Clean up session
      this.activeMeetings.delete(zoomMeetingId);
    }
  }

  // Handle live transcription events
  async handleLiveTranscription(payload) {
    const { object: transcription } = payload;
    const zoomMeetingId = transcription.meeting_id.toString();
    
    await this.processRealTimeTranscription(zoomMeetingId, {
      text: transcription.text,
      speaker: transcription.speaker,
      timestamp: new Date(transcription.timestamp),
      confidence: transcription.confidence || 0.8
    });
  }

  // Perform final comprehensive analysis
  async performFinalAnalysis(zoomMeetingId) {
    try {
      const meetingSession = this.activeMeetings.get(zoomMeetingId);
      if (!meetingSession) return;

      // Get complete transcription
      const fullTranscription = meetingSession.transcriptionBuffer
        .map(t => `${t.speaker}: ${t.text}`)
        .join('\n');

      if (!fullTranscription.trim()) {
        logger.warning(`No transcription data for final analysis: ${zoomMeetingId}`);
        return;
      }

      // Comprehensive NLP analysis
      const finalAnalysis = await nlpService.processText(fullTranscription, {
        meeting_context: meetingSession.context,
        final_analysis: true,
        session_id: meetingSession.nlpSessionId,
        meeting_duration: Date.now() - meetingSession.startTime,
        participant_count: meetingSession.participants.length
      });

      // Update database with final analysis
      await Meeting.update({
        summary: finalAnalysis.summary,
        sentiment_score: finalAnalysis.sentiment?.sentiment_score || 0,
        manual_tags: finalAnalysis.all_tags?.map(tag => tag.name) || []
      }, {
        where: { zoom_meeting_id: zoomMeetingId }
      });

      // Store detailed analysis in NLP service
      await axios.post(`${this.nlpServiceUrl}/meetings/${meetingSession.nlpSessionId}/finalize`, {
        final_analysis: finalAnalysis,
        meeting_stats: this.getMeetingStats(meetingSession)
      });

      logger.info(`Final analysis completed for meeting: ${zoomMeetingId}`);

    } catch (error) {
      logger.error(`Failed to perform final analysis for meeting ${zoomMeetingId}:`, error);
    }
  }

  // Get meeting statistics
  getMeetingStats(meetingSession) {
    const duration = Date.now() - meetingSession.startTime;
    const sentimentTrend = meetingSession.realTimeAnalysis.sentiment_trend;
    
    return {
      duration_ms: duration,
      participant_count: meetingSession.participants.length,
      transcription_segments: meetingSession.transcriptionBuffer.length,
      average_sentiment: sentimentTrend.length > 0 
        ? sentimentTrend.reduce((sum, s) => sum + (s.sentiment === 'positive' ? 1 : s.sentiment === 'negative' ? -1 : 0), 0) / sentimentTrend.length
        : 0,
      top_topics: meetingSession.realTimeAnalysis.key_topics
        .sort((a, b) => b.frequency - a.frequency)
        .slice(0, 5),
      entity_count: meetingSession.realTimeAnalysis.entities.length
    };
  }

  // Broadcast real-time updates to connected clients
  async broadcastRealTimeUpdate(zoomMeetingId, update) {
    try {
      // This would integrate with your WebSocket service
      // For now, we'll log the update
      logger.info(`Real-time update for meeting ${zoomMeetingId}:`, update.type);
      
      // You can implement WebSocket broadcasting here
      // Example: this.websocketService.broadcast(`meeting:${zoomMeetingId}`, update);
      
    } catch (error) {
      logger.error(`Failed to broadcast update for meeting ${zoomMeetingId}:`, error);
    }
  }

  // Start real-time monitoring for a meeting
  startRealTimeMonitoring(zoomMeetingId) {
    const meetingSession = this.activeMeetings.get(zoomMeetingId);
    if (!meetingSession) return;

    // Set up periodic analysis updates
    const monitoringInterval = setInterval(async () => {
      try {
        if (!this.activeMeetings.has(zoomMeetingId)) {
          clearInterval(monitoringInterval);
          return;
        }

        // Get real-time meeting insights
        const insights = await this.generateRealTimeInsights(zoomMeetingId);
        
        await this.broadcastRealTimeUpdate(zoomMeetingId, {
          type: 'meeting_insights',
          data: insights
        });

      } catch (error) {
        logger.error(`Real-time monitoring error for meeting ${zoomMeetingId}:`, error);
      }
    }, 30000); // Every 30 seconds

    meetingSession.monitoringInterval = monitoringInterval;
  }

  // Generate real-time insights
  async generateRealTimeInsights(zoomMeetingId) {
    const meetingSession = this.activeMeetings.get(zoomMeetingId);
    if (!meetingSession) return null;

    const stats = this.getMeetingStats(meetingSession);
    const recentSentiment = meetingSession.realTimeAnalysis.sentiment_trend.slice(-5);
    
    return {
      meeting_id: zoomMeetingId,
      duration: Math.round(stats.duration_ms / 60000),
      participant_count: stats.participant_count,
      current_sentiment: recentSentiment.length > 0 ? recentSentiment[recentSentiment.length - 1].sentiment : 'neutral',
      sentiment_trend: recentSentiment.map(s => s.sentiment),
      top_topics: stats.top_topics,
      engagement_level: this.calculateEngagementLevel(meetingSession),
      key_insights: await this.generateKeyInsights(meetingSession)
    };
  }

  // Calculate engagement level
  calculateEngagementLevel(meetingSession) {
    const transcriptionRate = meetingSession.transcriptionBuffer.length / Math.max(1, (Date.now() - meetingSession.startTime) / 60000);
    const participantEngagement = meetingSession.participants.length > 0 ? transcriptionRate / meetingSession.participants.length : 0;
    
    if (participantEngagement > 5) return 'high';
    if (participantEngagement > 2) return 'medium';
    return 'low';
  }

  // Generate key insights
  async generateKeyInsights(meetingSession) {
    const insights = [];
    
    // Sentiment insights
    const sentimentTrend = meetingSession.realTimeAnalysis.sentiment_trend;
    if (sentimentTrend.length > 5) {
      const recentSentiment = sentimentTrend.slice(-5);
      const positiveCount = recentSentiment.filter(s => s.sentiment === 'positive').length;
      
      if (positiveCount >= 4) {
        insights.push('Meeting sentiment is very positive');
      } else if (positiveCount <= 1) {
        insights.push('Meeting sentiment needs attention');
      }
    }

    // Topic insights
    const topTopics = meetingSession.realTimeAnalysis.key_topics
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 3);
    
    if (topTopics.length > 0) {
      insights.push(`Main discussion topics: ${topTopics.map(t => t.name).join(', ')}`);
    }

    // Duration insights
    const duration = Date.now() - meetingSession.startTime;
    const expectedDuration = meetingSession.context.expected_duration * 60000;
    
    if (duration > expectedDuration * 1.2) {
      insights.push('Meeting is running longer than scheduled');
    }

    return insights;
  }
}

module.exports = new ZoomNLPService();
