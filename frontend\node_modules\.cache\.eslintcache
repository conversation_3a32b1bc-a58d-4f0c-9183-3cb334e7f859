[{"C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\MeetingDetailPage.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\AnalyticsPage.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\LoginPage.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\MeetingsPage.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\DashboardPage.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\ClientsPage.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\SettingsPage.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\ReportsPage.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\ClientDetailPage.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\Common\\LoadingSpinner.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\Layout\\Layout.js": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\api.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\Layout\\Sidebar.js": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\dashboards\\AdminDashboard.js": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\dashboards\\ManagerDashboard.js": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\dashboards\\ClientDashboard.js": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\zoomService.js": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\meetingService.js": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\userService.js": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\analyticsService.js": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\utils\\chartSetup.js": "24"}, {"size": 2671, "mtime": 1748622592940, "results": "25", "hashOfConfig": "26"}, {"size": 2043, "mtime": 1748622603809, "results": "27", "hashOfConfig": "26"}, {"size": 3199, "mtime": 1748622636283, "results": "28", "hashOfConfig": "26"}, {"size": 673, "mtime": 1748622768352, "results": "29", "hashOfConfig": "26"}, {"size": 568, "mtime": 1748622790644, "results": "30", "hashOfConfig": "26"}, {"size": 6636, "mtime": 1748622711797, "results": "31", "hashOfConfig": "26"}, {"size": 576, "mtime": 1748622761932, "results": "32", "hashOfConfig": "26"}, {"size": 1286, "mtime": 1748625172566, "results": "33", "hashOfConfig": "26"}, {"size": 558, "mtime": 1748622776662, "results": "34", "hashOfConfig": "26"}, {"size": 572, "mtime": 1748622804114, "results": "35", "hashOfConfig": "26"}, {"size": 572, "mtime": 1748622797454, "results": "36", "hashOfConfig": "26"}, {"size": 663, "mtime": 1748622783740, "results": "37", "hashOfConfig": "26"}, {"size": 507, "mtime": 1748622643615, "results": "38", "hashOfConfig": "26"}, {"size": 4787, "mtime": 1748622663760, "results": "39", "hashOfConfig": "26"}, {"size": 3045, "mtime": 1748622621292, "results": "40", "hashOfConfig": "26"}, {"size": 4230, "mtime": 1748622685593, "results": "41", "hashOfConfig": "26"}, {"size": 16330, "mtime": 1748668031715, "results": "42", "hashOfConfig": "26"}, {"size": 22375, "mtime": 1748668047276, "results": "43", "hashOfConfig": "26"}, {"size": 21186, "mtime": 1748625042574, "results": "44", "hashOfConfig": "26"}, {"size": 9003, "mtime": 1748625222565, "results": "45", "hashOfConfig": "26"}, {"size": 8496, "mtime": 1748667913667, "results": "46", "hashOfConfig": "26"}, {"size": 9253, "mtime": 1748667940600, "results": "47", "hashOfConfig": "26"}, {"size": 10078, "mtime": 1748667971646, "results": "48", "hashOfConfig": "26"}, {"size": 1285, "mtime": 1748668021412, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "evopuw", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\MeetingDetailPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\AnalyticsPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\MeetingsPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\DashboardPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\ClientsPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\SettingsPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\ReportsPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\pages\\ClientDetailPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\Common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\Layout\\Layout.js", ["122"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\Layout\\Sidebar.js", ["123"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\dashboards\\AdminDashboard.js", ["124", "125", "126", "127", "128", "129"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\dashboards\\ManagerDashboard.js", ["130", "131", "132", "133", "134", "135", "136"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\components\\dashboards\\ClientDashboard.js", ["137", "138", "139"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\zoomService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\meetingService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\userService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\services\\analyticsService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\frontend\\src\\utils\\chartSetup.js", [], [], {"ruleId": "140", "severity": 1, "message": "141", "line": 30, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 30, "endColumn": 17}, {"ruleId": "140", "severity": 1, "message": "144", "line": 18, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 18, "endColumn": 9}, {"ruleId": "140", "severity": 1, "message": "145", "line": 30, "column": 16, "nodeType": "142", "messageId": "143", "endLine": 30, "endColumn": 29}, {"ruleId": "140", "severity": 1, "message": "146", "line": 34, "column": 15, "nodeType": "142", "messageId": "143", "endLine": 34, "endColumn": 27}, {"ruleId": "140", "severity": 1, "message": "147", "line": 39, "column": 11, "nodeType": "142", "messageId": "143", "endLine": 39, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "148", "line": 41, "column": 16, "nodeType": "142", "messageId": "143", "endLine": 41, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "149", "line": 59, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 59, "endColumn": 17}, {"ruleId": "150", "severity": 1, "message": "151", "line": 73, "column": 6, "nodeType": "152", "endLine": 73, "endColumn": 8, "suggestions": "153"}, {"ruleId": "140", "severity": 1, "message": "154", "line": 38, "column": 17, "nodeType": "142", "messageId": "143", "endLine": 38, "endColumn": 31}, {"ruleId": "140", "severity": 1, "message": "155", "line": 45, "column": 13, "nodeType": "142", "messageId": "143", "endLine": 45, "endColumn": 23}, {"ruleId": "140", "severity": 1, "message": "148", "line": 50, "column": 16, "nodeType": "142", "messageId": "143", "endLine": 50, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "149", "line": 62, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 62, "endColumn": 17}, {"ruleId": "140", "severity": 1, "message": "156", "line": 64, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 64, "endColumn": 20}, {"ruleId": "140", "severity": 1, "message": "157", "line": 65, "column": 19, "nodeType": "142", "messageId": "143", "endLine": 65, "endColumn": 29}, {"ruleId": "150", "severity": 1, "message": "158", "line": 83, "column": 6, "nodeType": "152", "endLine": 83, "endColumn": 15, "suggestions": "159"}, {"ruleId": "140", "severity": 1, "message": "160", "line": 15, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 15, "endColumn": 8}, {"ruleId": "140", "severity": 1, "message": "149", "line": 49, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 49, "endColumn": 17}, {"ruleId": "150", "severity": 1, "message": "161", "line": 55, "column": 6, "nodeType": "152", "endLine": 55, "endColumn": 8, "suggestions": "162"}, "no-unused-vars", "'isMobile' is assigned a value but never used.", "Identifier", "unusedVar", "'People' is defined but never used.", "'DashboardIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'StopIcon' is defined but never used.", "'Bar' is defined but never used.", "'loading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["163"], "'AssignmentIcon' is defined but never used.", "'SearchIcon' is defined but never used.", "'filterOpen' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadManagerData'. Either include it or remove the dependency array.", ["164"], "'Paper' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadClientData'. Either include it or remove the dependency array.", ["165"], {"desc": "166", "fix": "167"}, {"desc": "168", "fix": "169"}, {"desc": "170", "fix": "171"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "172", "text": "173"}, "Update the dependencies array to be: [filters, loadManagerData]", {"range": "174", "text": "175"}, "Update the dependencies array to be: [loadClientData]", {"range": "176", "text": "177"}, [1717, 1719], "[loadDashboardData]", [1970, 1979], "[filters, loadManagerData]", [1375, 1377], "[loadClientData]"]