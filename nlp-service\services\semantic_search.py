import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
from typing import Dict, List, Any, Optional, Tuple
import logging
import pickle
import json
from pathlib import Path
import asyncio
from datetime import datetime
import sqlite3
import threading
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Search result data structure"""
    content: str
    score: float
    metadata: Dict[str, Any]
    meeting_id: Optional[int] = None
    chunk_id: Optional[int] = None

class SemanticSearchEngine:
    """Advanced semantic search using FAISS and Sentence-BERT"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.embedding_model = None
        self.index = None
        self.metadata_store = {}
        self.embedding_dim = 384  # MiniLM dimension
        self.index_path = Path(config.get('index_path', 'data/faiss_index'))
        self.metadata_path = Path(config.get('metadata_path', 'data/metadata.pkl'))
        self.db_path = Path(config.get('db_path', 'data/search_db.sqlite'))
        
        # Thread lock for index updates
        self.index_lock = threading.RLock()
        
        # Initialize SQLite for metadata
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for metadata"""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(str(self.db_path)) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    meeting_id INTEGER,
                    chunk_id INTEGER,
                    content TEXT,
                    metadata TEXT,
                    embedding_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_meeting_id ON documents(meeting_id)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_embedding_id ON documents(embedding_id)
            ''')
            
            conn.commit()
    
    async def initialize(self):
        """Initialize the search engine"""
        logger.info("Initializing Semantic Search Engine...")
        
        try:
            # Load embedding model
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("✅ Embedding model loaded")
            
            # Load or create FAISS index
            await self._load_or_create_index()
            
            logger.info("✅ Semantic Search Engine initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize search engine: {e}")
            raise
    
    async def _load_or_create_index(self):
        """Load existing index or create new one"""
        if self.index_path.exists():
            try:
                self.index = faiss.read_index(str(self.index_path))
                logger.info(f"✅ Loaded existing FAISS index with {self.index.ntotal} vectors")
            except Exception as e:
                logger.warning(f"Failed to load index: {e}. Creating new index.")
                self._create_new_index()
        else:
            self._create_new_index()
    
    def _create_new_index(self):
        """Create new FAISS index"""
        # Use IndexFlatIP for cosine similarity
        self.index = faiss.IndexFlatIP(self.embedding_dim)
        logger.info("✅ Created new FAISS index")
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> List[int]:
        """Add documents to the search index"""
        if not documents:
            return []
        
        try:
            # Extract text content
            texts = [doc['content'] for doc in documents]
            
            # Generate embeddings
            embeddings = await self._generate_embeddings_batch(texts)
            
            # Normalize embeddings for cosine similarity
            faiss.normalize_L2(embeddings)
            
            with self.index_lock:
                # Add to FAISS index
                start_id = self.index.ntotal
                self.index.add(embeddings)
                
                # Store metadata in database
                doc_ids = []
                with sqlite3.connect(str(self.db_path)) as conn:
                    for i, doc in enumerate(documents):
                        embedding_id = start_id + i
                        
                        cursor = conn.execute('''
                            INSERT INTO documents (meeting_id, chunk_id, content, metadata, embedding_id)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (
                            doc.get('meeting_id'),
                            doc.get('chunk_id'),
                            doc['content'],
                            json.dumps(doc.get('metadata', {})),
                            embedding_id
                        ))
                        
                        doc_ids.append(cursor.lastrowid)
                    
                    conn.commit()
            
            logger.info(f"✅ Added {len(documents)} documents to search index")
            return doc_ids
            
        except Exception as e:
            logger.error(f"❌ Failed to add documents: {e}")
            raise
    
    async def search(self, query: str, top_k: int = 10, filters: Optional[Dict[str, Any]] = None) -> List[SearchResult]:
        """Search for similar documents"""
        try:
            # Generate query embedding
            query_embedding = await self._generate_embeddings_batch([query])
            faiss.normalize_L2(query_embedding)
            
            with self.index_lock:
                # Search in FAISS index
                scores, indices = self.index.search(query_embedding, min(top_k * 2, self.index.ntotal))
            
            # Retrieve metadata and apply filters
            results = []
            with sqlite3.connect(str(self.db_path)) as conn:
                for score, idx in zip(scores[0], indices[0]):
                    if idx == -1:  # Invalid index
                        continue
                    
                    cursor = conn.execute('''
                        SELECT id, meeting_id, chunk_id, content, metadata
                        FROM documents WHERE embedding_id = ?
                    ''', (int(idx),))
                    
                    row = cursor.fetchone()
                    if not row:
                        continue
                    
                    doc_id, meeting_id, chunk_id, content, metadata_json = row
                    metadata = json.loads(metadata_json) if metadata_json else {}
                    
                    # Apply filters
                    if filters and not self._apply_filters(metadata, meeting_id, filters):
                        continue
                    
                    results.append(SearchResult(
                        content=content,
                        score=float(score),
                        metadata=metadata,
                        meeting_id=meeting_id,
                        chunk_id=chunk_id
                    ))
                    
                    if len(results) >= top_k:
                        break
            
            # Sort by score (descending)
            results.sort(key=lambda x: x.score, reverse=True)
            
            logger.info(f"✅ Found {len(results)} results for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"❌ Search failed: {e}")
            return []
    
    async def semantic_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Advanced semantic query with context understanding"""
        try:
            # Parse query intent
            query_intent = await self._parse_query_intent(query)
            
            # Enhance query with context
            enhanced_query = self._enhance_query_with_context(query, context, query_intent)
            
            # Perform search
            search_results = await self.search(enhanced_query, top_k=15)
            
            # Rank and filter results
            ranked_results = await self._rank_results(search_results, query, query_intent)
            
            # Generate response
            response = await self._generate_response(query, ranked_results, query_intent)
            
            return {
                'query': query,
                'intent': query_intent,
                'results': [
                    {
                        'content': r.content,
                        'score': r.score,
                        'meeting_id': r.meeting_id,
                        'metadata': r.metadata
                    } for r in ranked_results[:5]
                ],
                'response': response,
                'total_results': len(search_results)
            }
            
        except Exception as e:
            logger.error(f"❌ Semantic query failed: {e}")
            return {
                'query': query,
                'error': str(e),
                'results': [],
                'response': "I'm sorry, I couldn't process your query at the moment."
            }
    
    async def _generate_embeddings_batch(self, texts: List[str]) -> np.ndarray:
        """Generate embeddings for a batch of texts"""
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        embeddings = await loop.run_in_executor(
            None,
            self.embedding_model.encode,
            texts
        )
        return embeddings.astype('float32')
    
    async def _parse_query_intent(self, query: str) -> Dict[str, Any]:
        """Parse query to understand intent"""
        query_lower = query.lower()
        
        intent = {
            'type': 'general',
            'entities': [],
            'time_filter': None,
            'client_filter': None,
            'topic_filter': None
        }
        
        # Detect question types
        if any(word in query_lower for word in ['what', 'how', 'why', 'when', 'where', 'who']):
            intent['type'] = 'question'
        elif any(word in query_lower for word in ['find', 'search', 'show', 'list']):
            intent['type'] = 'search'
        elif any(word in query_lower for word in ['summarize', 'summary', 'overview']):
            intent['type'] = 'summarization'
        
        # Extract entities (simple keyword matching)
        financial_terms = ['investment', 'roi', 'profit', 'revenue', 'funding', 'valuation']
        for term in financial_terms:
            if term in query_lower:
                intent['entities'].append({'type': 'financial', 'value': term})
        
        # Extract time filters
        time_terms = ['today', 'yesterday', 'last week', 'last month', 'this year']
        for term in time_terms:
            if term in query_lower:
                intent['time_filter'] = term
        
        return intent
    
    def _enhance_query_with_context(self, query: str, context: Optional[Dict], intent: Dict) -> str:
        """Enhance query with additional context"""
        enhanced_parts = [query]
        
        if context:
            if context.get('client_name'):
                enhanced_parts.append(f"client: {context['client_name']}")
            
            if context.get('time_period'):
                enhanced_parts.append(f"time: {context['time_period']}")
            
            if context.get('topics'):
                enhanced_parts.append(f"topics: {', '.join(context['topics'][:3])}")
        
        return ' '.join(enhanced_parts)
    
    async def _rank_results(self, results: List[SearchResult], query: str, intent: Dict) -> List[SearchResult]:
        """Re-rank results based on query intent and additional factors"""
        # Simple re-ranking based on intent
        for result in results:
            # Boost score for intent-specific content
            if intent['type'] == 'financial' and any(term in result.content.lower() 
                                                   for term in ['investment', 'roi', 'profit']):
                result.score *= 1.2
            
            # Boost recent content
            if result.metadata.get('date'):
                try:
                    doc_date = datetime.fromisoformat(result.metadata['date'])
                    days_old = (datetime.now() - doc_date).days
                    if days_old < 30:  # Recent content
                        result.score *= 1.1
                except:
                    pass
        
        return sorted(results, key=lambda x: x.score, reverse=True)
    
    async def _generate_response(self, query: str, results: List[SearchResult], intent: Dict) -> str:
        """Generate natural language response"""
        if not results:
            return "I couldn't find any relevant information for your query."
        
        if intent['type'] == 'question':
            # Extract relevant snippets
            snippets = [r.content[:200] + "..." for r in results[:3]]
            return f"Based on the meeting records, here's what I found: {' '.join(snippets)}"
        
        elif intent['type'] == 'summarization':
            return f"I found {len(results)} relevant meetings. The main topics discussed include: {self._extract_key_topics(results)}"
        
        else:
            return f"I found {len(results)} relevant results. The most relevant content discusses: {results[0].content[:200]}..."
    
    def _extract_key_topics(self, results: List[SearchResult]) -> str:
        """Extract key topics from search results"""
        # Simple keyword extraction
        all_text = ' '.join([r.content for r in results[:5]])
        words = all_text.lower().split()
        
        # Count important terms
        important_terms = ['investment', 'blockchain', 'ai', 'technology', 'growth', 'strategy']
        term_counts = {term: words.count(term) for term in important_terms if words.count(term) > 0}
        
        if term_counts:
            top_terms = sorted(term_counts.items(), key=lambda x: x[1], reverse=True)[:3]
            return ', '.join([term for term, count in top_terms])
        
        return "various business topics"
    
    def _apply_filters(self, metadata: Dict, meeting_id: Optional[int], filters: Dict[str, Any]) -> bool:
        """Apply search filters"""
        if filters.get('meeting_id') and meeting_id != filters['meeting_id']:
            return False
        
        if filters.get('client_name'):
            client_name = metadata.get('client_name', '').lower()
            if filters['client_name'].lower() not in client_name:
                return False
        
        if filters.get('date_range'):
            doc_date = metadata.get('date')
            if doc_date:
                try:
                    doc_datetime = datetime.fromisoformat(doc_date)
                    start_date = datetime.fromisoformat(filters['date_range']['start'])
                    end_date = datetime.fromisoformat(filters['date_range']['end'])
                    if not (start_date <= doc_datetime <= end_date):
                        return False
                except:
                    return False
        
        return True
    
    async def update_document(self, doc_id: int, new_content: str, new_metadata: Dict[str, Any]):
        """Update an existing document"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                # Get current embedding_id
                cursor = conn.execute('SELECT embedding_id FROM documents WHERE id = ?', (doc_id,))
                row = cursor.fetchone()
                
                if not row:
                    raise ValueError(f"Document {doc_id} not found")
                
                embedding_id = row[0]
                
                # Generate new embedding
                new_embedding = await self._generate_embeddings_batch([new_content])
                faiss.normalize_L2(new_embedding)
                
                with self.index_lock:
                    # Update in FAISS index (reconstruct if needed)
                    # For simplicity, we'll add new and mark old as invalid
                    self.index.add(new_embedding)
                    new_embedding_id = self.index.ntotal - 1
                    
                    # Update database
                    conn.execute('''
                        UPDATE documents 
                        SET content = ?, metadata = ?, embedding_id = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', (new_content, json.dumps(new_metadata), new_embedding_id, doc_id))
                    
                    conn.commit()
            
            logger.info(f"✅ Updated document {doc_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to update document {doc_id}: {e}")
            raise
    
    async def delete_document(self, doc_id: int):
        """Delete a document from the index"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.execute('DELETE FROM documents WHERE id = ?', (doc_id,))
                conn.commit()
            
            logger.info(f"✅ Deleted document {doc_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to delete document {doc_id}: {e}")
            raise
    
    async def save_index(self):
        """Save the FAISS index to disk"""
        try:
            self.index_path.parent.mkdir(parents=True, exist_ok=True)
            
            with self.index_lock:
                faiss.write_index(self.index, str(self.index_path))
            
            logger.info(f"✅ Saved FAISS index to {self.index_path}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save index: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """Get search engine statistics"""
        with sqlite3.connect(str(self.db_path)) as conn:
            cursor = conn.execute('SELECT COUNT(*) FROM documents')
            doc_count = cursor.fetchone()[0]
            
            cursor = conn.execute('SELECT COUNT(DISTINCT meeting_id) FROM documents WHERE meeting_id IS NOT NULL')
            meeting_count = cursor.fetchone()[0]
        
        return {
            'total_documents': doc_count,
            'total_meetings': meeting_count,
            'index_size': self.index.ntotal if self.index else 0,
            'embedding_dimension': self.embedding_dim
        }
