const mongoose = require('mongoose');
const logger = require('../utils/logger');

// MongoDB connection configuration
const mongoOptions = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferCommands: false,
  bufferMaxEntries: 0,
};

// Connect to MongoDB
async function connectMongoDB() {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/smartcoverage';
    await mongoose.connect(mongoUri, mongoOptions);
    logger.info('✅ MongoDB connection established successfully');
  } catch (error) {
    logger.error('❌ Unable to connect to MongoDB:', error);
    throw error;
  }
}

// MongoDB connection event handlers
mongoose.connection.on('connected', () => {
  logger.info('🔗 MongoDB connected');
});

mongoose.connection.on('error', (error) => {
  logger.error('❌ MongoDB connection error:', error);
});

mongoose.connection.on('disconnected', () => {
  logger.warn('⚠️ MongoDB disconnected');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  await mongoose.connection.close();
  logger.info('📴 MongoDB connection closed through app termination');
  process.exit(0);
});

module.exports = {
  connectMongoDB,
  mongoose,
};
