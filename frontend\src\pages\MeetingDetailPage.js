import React from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography, Card, CardContent } from '@mui/material';

const MeetingDetailPage = () => {
  const { id } = useParams();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Meeting Details - {id}
      </Typography>
      <Card>
        <CardContent>
          <Typography variant="body1">
            Detailed meeting view will be implemented here.
            This will show meeting notes, tags, sentiment analysis, and transcripts.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MeetingDetailPage;
