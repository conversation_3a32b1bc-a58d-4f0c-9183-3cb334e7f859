import torch
from transformers import AutoTokenizer, AutoModelForSeq2SeqLM, pipeline
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path
import re
import numpy as np
from .base_model import BaseNLPModel

logger = logging.getLogger(__name__)

class MeetingSummarizer(BaseNLPModel):
    """Advanced meeting summarization using transformer models"""

    def __init__(self, model_name: str = "facebook/bart-large-cnn", model_path: Optional[Path] = None):
        super().__init__(model_name, model_path)
        self.summarization_pipeline = None
        self.max_chunk_length = 1024
        self.min_summary_length = 50
        self.max_summary_length = 300

    def load_model(self):
        """Load summarization model"""
        try:
            if self.model_path and self.model_path.exists():
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
                self.model = AutoModelForSeq2SeqLM.from_pretrained(self.model_path)
            else:
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.model = AutoModelForSeq2SeqLM.from_pretrained(self.model_name)

            self.model.to(self.device)

            # Create summarization pipeline
            self.summarization_pipeline = pipeline(
                "summarization",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if torch.cuda.is_available() else -1
            )

            logger.info(f"Summarization model loaded: {self.model_name}")

        except Exception as e:
            logger.error(f"Error loading summarization model: {e}")
            raise

    def predict(self, text: str, meeting_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate comprehensive meeting summary"""
        if not self.summarization_pipeline:
            raise ValueError("Model not loaded")

        # Preprocess text
        cleaned_text = self._preprocess_text(text)

        # Split into chunks if text is too long
        chunks = self._split_text_into_chunks(cleaned_text)

        # Generate summaries for each chunk
        chunk_summaries = []
        for i, chunk in enumerate(chunks):
            try:
                summary = self._summarize_chunk(chunk, meeting_context)
                chunk_summaries.append({
                    "chunk_id": i,
                    "summary": summary,
                    "original_length": len(chunk),
                    "summary_length": len(summary)
                })
            except Exception as e:
                logger.warning(f"Failed to summarize chunk {i}: {e}")
                continue

        # Combine chunk summaries
        if len(chunk_summaries) > 1:
            combined_text = " ".join([cs["summary"] for cs in chunk_summaries])
            final_summary = self._summarize_chunk(combined_text, meeting_context)
        elif len(chunk_summaries) == 1:
            final_summary = chunk_summaries[0]["summary"]
        else:
            final_summary = "Unable to generate summary."

        # Extract key insights
        key_insights = self._extract_key_insights(cleaned_text, final_summary)

        # Generate structured summary
        structured_summary = self._create_structured_summary(
            cleaned_text, final_summary, key_insights, meeting_context
        )

        return {
            "summary": final_summary,
            "structured_summary": structured_summary,
            "key_insights": key_insights,
            "chunk_summaries": chunk_summaries,
            "original_length": len(text),
            "summary_length": len(final_summary),
            "compression_ratio": len(final_summary) / len(text) if len(text) > 0 else 0,
            "processing_stats": {
                "chunks_processed": len(chunks),
                "successful_chunks": len(chunk_summaries)
            }
        }

    def _summarize_chunk(self, text: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Summarize a single text chunk"""
        # Adjust summary length based on input length
        input_length = len(text.split())
        max_length = min(self.max_summary_length, max(self.min_summary_length, input_length // 3))
        min_length = min(self.min_summary_length, max_length // 2)

        try:
            # Add context-aware prompt if available
            if context:
                prompt = self._create_context_prompt(text, context)
            else:
                prompt = text

            summary_result = self.summarization_pipeline(
                prompt,
                max_length=max_length,
                min_length=min_length,
                do_sample=False,
                truncation=True
            )

            summary = summary_result[0]['summary_text']

            # Post-process summary
            summary = self._post_process_summary(summary, context)

            return summary

        except Exception as e:
            logger.error(f"Error in chunk summarization: {e}")
            return "Summary generation failed for this section."

    def _create_context_prompt(self, text: str, context: Dict[str, Any]) -> str:
        """Create context-aware prompt for summarization"""
        prompt_parts = []

        if context.get("meeting_type"):
            prompt_parts.append(f"This is a {context['meeting_type']} meeting.")

        if context.get("client_name"):
            prompt_parts.append(f"Client: {context['client_name']}")

        if context.get("subject"):
            prompt_parts.append(f"Subject: {context['subject']}")

        if context.get("key_topics"):
            topics = ", ".join(context["key_topics"][:3])  # Top 3 topics
            prompt_parts.append(f"Key topics: {topics}")

        if prompt_parts:
            context_info = " ".join(prompt_parts)
            return f"{context_info}\n\nMeeting content: {text}"

        return text

    def _extract_key_insights(self, original_text: str, summary: str) -> List[Dict[str, Any]]:
        """Extract key insights from the meeting"""
        insights = []

        # Extract decisions
        decisions = self._extract_decisions(original_text)
        if decisions:
            insights.append({
                "type": "decisions",
                "title": "Key Decisions",
                "items": decisions,
                "importance": "high"
            })

        # Extract action items
        action_items = self._extract_action_items(original_text)
        if action_items:
            insights.append({
                "type": "action_items",
                "title": "Action Items",
                "items": action_items,
                "importance": "high"
            })

        # Extract financial mentions
        financial_info = self._extract_financial_mentions(original_text)
        if financial_info:
            insights.append({
                "type": "financial",
                "title": "Financial Information",
                "items": financial_info,
                "importance": "medium"
            })

        # Extract risks and concerns
        risks = self._extract_risks_concerns(original_text)
        if risks:
            insights.append({
                "type": "risks",
                "title": "Risks and Concerns",
                "items": risks,
                "importance": "high"
            })

        # Extract opportunities
        opportunities = self._extract_opportunities(original_text)
        if opportunities:
            insights.append({
                "type": "opportunities",
                "title": "Opportunities",
                "items": opportunities,
                "importance": "medium"
            })

        return insights

    def _create_structured_summary(self, text: str, summary: str, insights: List[Dict], context: Optional[Dict] = None) -> Dict[str, Any]:
        """Create a structured summary with different sections"""
        structured = {
            "executive_summary": summary,
            "meeting_overview": {},
            "key_points": [],
            "outcomes": {},
            "next_steps": []
        }

        # Meeting overview
        if context:
            structured["meeting_overview"] = {
                "client": context.get("client_name", "Unknown"),
                "subject": context.get("subject", "Meeting Discussion"),
                "organizer": context.get("organizer_name", "Unknown"),
                "date": context.get("meeting_date", "Unknown")
            }

        # Extract key points from insights
        for insight in insights:
            if insight["type"] in ["decisions", "financial", "opportunities"]:
                structured["key_points"].extend(insight["items"][:3])  # Top 3 items

        # Outcomes
        decision_insights = [i for i in insights if i["type"] == "decisions"]
        if decision_insights:
            structured["outcomes"]["decisions"] = decision_insights[0]["items"]

        financial_insights = [i for i in insights if i["type"] == "financial"]
        if financial_insights:
            structured["outcomes"]["financial_commitments"] = financial_insights[0]["items"]

        # Next steps
        action_insights = [i for i in insights if i["type"] == "action_items"]
        if action_insights:
            structured["next_steps"] = action_insights[0]["items"]

        return structured

    def _extract_decisions(self, text: str) -> List[str]:
        """Extract decisions made during the meeting"""
        decision_patterns = [
            r"decided to\s+([^.!?]+)",
            r"decision\s+(?:was\s+)?(?:made\s+)?to\s+([^.!?]+)",
            r"agreed to\s+([^.!?]+)",
            r"concluded that\s+([^.!?]+)",
            r"resolved to\s+([^.!?]+)"
        ]

        decisions = []
        for pattern in decision_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                decision = match.group(1).strip()
                if len(decision) > 10 and decision not in decisions:
                    decisions.append(decision)

        return decisions[:5]  # Top 5 decisions

    def _extract_action_items(self, text: str) -> List[str]:
        """Extract action items from the meeting"""
        action_patterns = [
            r"(?:will|should|need to|must)\s+([^.!?]+)",
            r"action item:?\s*([^.!?]+)",
            r"follow[- ]?up:?\s*([^.!?]+)",
            r"next steps?:?\s*([^.!?]+)",
            r"to[- ]?do:?\s*([^.!?]+)"
        ]

        actions = []
        for pattern in action_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                action = match.group(1).strip()
                if len(action) > 10 and action not in actions:
                    actions.append(action)

        return actions[:5]  # Top 5 action items

    def _extract_financial_mentions(self, text: str) -> List[str]:
        """Extract financial information mentions"""
        financial_patterns = [
            r"\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion|thousand|k|m|b))?",
            r"\d+(?:\.\d+)?%\s*(?:roi|return|interest|growth)",
            r"(?:budget|investment|funding|revenue|profit|cost)\s+of\s+\$?[\d,]+",
            r"(?:valuation|market cap)\s+(?:of\s+)?\$?[\d,]+(?:\s*(?:million|billion))?"
        ]

        financial_info = []
        for pattern in financial_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                info = match.group().strip()
                if info not in financial_info:
                    financial_info.append(info)

        return financial_info[:5]

    def _extract_risks_concerns(self, text: str) -> List[str]:
        """Extract risks and concerns mentioned"""
        risk_patterns = [
            r"(?:risk|concern|issue|problem|challenge)\s+(?:is|was|about|with|regarding)\s+([^.!?]+)",
            r"worried about\s+([^.!?]+)",
            r"potential\s+(?:risk|issue|problem)\s+([^.!?]+)",
            r"(?:downside|negative|drawback)\s+(?:is|was)\s+([^.!?]+)"
        ]

        risks = []
        for pattern in risk_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                risk = match.group(1).strip()
                if len(risk) > 10 and risk not in risks:
                    risks.append(risk)

        return risks[:3]

    def _extract_opportunities(self, text: str) -> List[str]:
        """Extract opportunities mentioned"""
        opportunity_patterns = [
            r"opportunity\s+(?:to|for)\s+([^.!?]+)",
            r"potential\s+(?:for|to)\s+([^.!?]+)",
            r"could\s+(?:lead to|result in|enable)\s+([^.!?]+)",
            r"upside\s+(?:is|would be)\s+([^.!?]+)"
        ]

        opportunities = []
        for pattern in opportunity_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                opportunity = match.group(1).strip()
                if len(opportunity) > 10 and opportunity not in opportunities:
                    opportunities.append(opportunity)

        return opportunities[:3]

    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for summarization"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove speaker labels (e.g., "John: ", "Speaker 1: ")
        text = re.sub(r'^[A-Za-z\s]+:\s*', '', text, flags=re.MULTILINE)

        # Remove timestamps
        text = re.sub(r'\[\d{2}:\d{2}:\d{2}\]', '', text)

        # Remove filler words and phrases
        filler_patterns = [
            r'\b(?:um|uh|er|ah|like|you know|sort of|kind of)\b',
            r'\b(?:basically|actually|literally|obviously)\b'
        ]
        for pattern in filler_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        # Clean up extra spaces
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def _post_process_summary(self, summary: str, context: Optional[Dict] = None) -> str:
        """Post-process generated summary"""
        # Ensure proper capitalization
        summary = summary.strip()
        if summary and not summary[0].isupper():
            summary = summary[0].upper() + summary[1:]

        # Ensure it ends with proper punctuation
        if summary and summary[-1] not in '.!?':
            summary += '.'

        # Add context-specific information if available
        if context and context.get("client_name"):
            if context["client_name"].lower() not in summary.lower():
                summary = f"Meeting with {context['client_name']}. {summary}"

        return summary

    def _split_text_into_chunks(self, text: str) -> List[str]:
        """Split long text into manageable chunks"""
        # Tokenize to check length
        tokens = self.tokenizer.encode(text, add_special_tokens=False)

        if len(tokens) <= self.max_chunk_length:
            return [text]

        # Split by sentences first
        sentences = re.split(r'[.!?]+', text)
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # Check if adding this sentence would exceed the limit
            test_chunk = current_chunk + " " + sentence if current_chunk else sentence
            test_tokens = self.tokenizer.encode(test_chunk, add_special_tokens=False)

            if len(test_tokens) <= self.max_chunk_length:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = sentence

        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    def train(self, train_data: List[Dict], validation_data: Optional[List[Dict]] = None):
        """Fine-tune summarization model on domain-specific data"""
        from torch.utils.data import DataLoader
        from transformers import AdamW, get_linear_schedule_with_warmup

        # Prepare datasets
        train_dataset = SummarizationDataset(train_data, self.tokenizer)
        train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)

        val_loader = None
        if validation_data:
            val_dataset = SummarizationDataset(validation_data, self.tokenizer)
            val_loader = DataLoader(val_dataset, batch_size=4)

        # Setup training
        optimizer = AdamW(self.model.parameters(), lr=3e-5, weight_decay=0.01)
        total_steps = len(train_loader) * 3
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=100,
            num_training_steps=total_steps
        )

        # Training loop
        self.model.train()
        for epoch in range(3):
            total_loss = 0
            for batch in train_loader:
                optimizer.zero_grad()

                inputs = {k: v.to(self.device) for k, v in batch.items()}
                outputs = self.model(**inputs)
                loss = outputs.loss

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                optimizer.step()
                scheduler.step()

                total_loss += loss.item()

            avg_loss = total_loss / len(train_loader)
            logger.info(f"Epoch {epoch + 1}, Average Loss: {avg_loss:.4f}")

            if val_loader:
                val_loss = self._evaluate_summarization(val_loader)
                logger.info(f"Validation Loss: {val_loss:.4f}")

class SummarizationDataset(torch.utils.data.Dataset):
    """Dataset for summarization training"""

    def __init__(self, data: List[Dict], tokenizer, max_input_length: int = 1024, max_target_length: int = 256):
        self.data = data
        self.tokenizer = tokenizer
        self.max_input_length = max_input_length
        self.max_target_length = max_target_length

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        input_text = item['input_text']
        target_text = item['target_summary']

        # Tokenize input
        input_encoding = self.tokenizer(
            input_text,
            truncation=True,
            padding='max_length',
            max_length=self.max_input_length,
            return_tensors='pt'
        )

        # Tokenize target
        target_encoding = self.tokenizer(
            target_text,
            truncation=True,
            padding='max_length',
            max_length=self.max_target_length,
            return_tensors='pt'
        )

        return {
            'input_ids': input_encoding['input_ids'].flatten(),
            'attention_mask': input_encoding['attention_mask'].flatten(),
            'labels': target_encoding['input_ids'].flatten()
        }
