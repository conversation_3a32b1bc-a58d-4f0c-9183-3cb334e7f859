{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\smartcoverage\\\\frontend\\\\src\\\\components\\\\dashboards\\\\ManagerDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Grid, Card, CardContent, Typography, Box, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Alert, Tabs, Tab, List, ListItem, ListItemText, ListItemIcon } from '@mui/material';\nimport { VideoCall as VideoCallIcon, Analytics as AnalyticsIcon, Assignment as AssignmentIcon, TrendingUp as TrendingUpIcon, Add as AddIcon, Edit as EditIcon, PlayArrow as PlayIcon, Visibility as ViewIcon, Download as DownloadIcon, Search as SearchIcon, FilterList as FilterIcon, Schedule as ScheduleIcon, Group as GroupIcon } from '@mui/icons-material';\nimport { Line, Bar, Pie } from 'react-chartjs-2';\nimport '../../utils/chartSetup'; // Import Chart.js setup\nimport { useAuth } from '../../contexts/AuthContext';\nimport { meetingService } from '../../services/meetingService';\nimport { zoomService } from '../../services/zoomService';\nimport { analyticsService } from '../../services/analyticsService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ManagerDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState(0);\n  const [meetings, setMeetings] = useState([]);\n  const [analytics, setAnalytics] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [createMeetingOpen, setCreateMeetingOpen] = useState(false);\n  const [filterOpen, setFilterOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    dateRange: 'week',\n    status: 'all',\n    client: '',\n    meetingType: 'all'\n  });\n  const [newMeeting, setNewMeeting] = useState({\n    topic: '',\n    client_name: '',\n    client_industry: '',\n    start_time: '',\n    duration: 60,\n    host_email: (user === null || user === void 0 ? void 0 : user.email) || '',\n    meeting_type: 'client_call'\n  });\n  useEffect(() => {\n    loadManagerData();\n  }, [filters]);\n  const loadManagerData = async () => {\n    try {\n      setLoading(true);\n      const [meetingsData, analyticsData] = await Promise.all([meetingService.getMeetingsByManager(user.id, filters), analyticsService.getManagerAnalytics(user.id, filters)]);\n      setMeetings(meetingsData.data || []);\n      setAnalytics(analyticsData.data || {});\n    } catch (error) {\n      console.error('Failed to load manager data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateMeeting = async () => {\n    try {\n      const meetingData = {\n        ...newMeeting,\n        enable_nlp_analysis: true,\n        created_by: user.id,\n        settings: {\n          auto_recording: 'cloud',\n          waiting_room: true,\n          mute_upon_entry: true,\n          participant_video: true\n        }\n      };\n      await zoomService.createMeetingWithNLP(meetingData);\n      setCreateMeetingOpen(false);\n      resetNewMeeting();\n      loadManagerData();\n    } catch (error) {\n      console.error('Failed to create meeting:', error);\n    }\n  };\n  const resetNewMeeting = () => {\n    setNewMeeting({\n      topic: '',\n      client_name: '',\n      client_industry: '',\n      start_time: '',\n      duration: 60,\n      host_email: (user === null || user === void 0 ? void 0 : user.email) || '',\n      meeting_type: 'client_call'\n    });\n  };\n  const handleJoinMeeting = async meetingId => {\n    try {\n      const joinInfo = await zoomService.joinMeeting(meetingId);\n      window.open(joinInfo.join_url, '_blank');\n    } catch (error) {\n      console.error('Failed to join meeting:', error);\n    }\n  };\n  const handleStartMeeting = async meetingId => {\n    try {\n      const startInfo = await zoomService.startMeeting(meetingId);\n      window.open(startInfo.start_url, '_blank');\n    } catch (error) {\n      console.error('Failed to start meeting:', error);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'scheduled':\n        return 'primary';\n      case 'in_progress':\n        return 'success';\n      case 'completed':\n        return 'default';\n      case 'cancelled':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const chartData = {\n    meetingTrends: {\n      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n      datasets: [{\n        label: 'Meetings Conducted',\n        data: analytics.weeklyMeetings || [8, 12, 15, 10],\n        borderColor: 'rgb(54, 162, 235)',\n        backgroundColor: 'rgba(54, 162, 235, 0.2)',\n        tension: 0.1\n      }]\n    },\n    clientSentiment: {\n      labels: ['Very Positive', 'Positive', 'Neutral', 'Negative', 'Very Negative'],\n      datasets: [{\n        data: analytics.sentimentDistribution || [25, 35, 25, 10, 5],\n        backgroundColor: ['#4CAF50', '#8BC34A', '#FFC107', '#FF9800', '#F44336']\n      }]\n    },\n    meetingTypes: {\n      labels: ['Client Calls', 'Internal', 'Presentations', 'Training'],\n      datasets: [{\n        data: analytics.meetingTypeDistribution || [45, 25, 20, 10],\n        backgroundColor: ['#2196F3', '#9C27B0', '#FF5722', '#607D8B']\n      }]\n    }\n  };\n  const TabPanel = ({\n    children,\n    value,\n    index\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    hidden: value !== index,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Manager Dashboard - Records & Analytics\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(VideoCallIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"This Month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: analytics.monthlyMeetings || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"success.main\",\n                  children: \"+12% from last month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n                color: \"success\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Avg Sentiment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: analytics.averageSentiment || 'Positive'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"success.main\",\n                  children: \"+5% improvement\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(GroupIcon, {\n                color: \"info\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Active Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: analytics.activeClients || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"info.main\",\n                  children: \"Across all meetings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(ScheduleIcon, {\n                color: \"warning\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Avg Duration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: analytics.averageDuration || '45m'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"warning.main\",\n                  children: \"Optimal range\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: (e, newValue) => setActiveTab(newValue),\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Meeting Records\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Analytics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Client Insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: activeTab,\n        index: 0,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Meeting Records Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              startIcon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 28\n              }, this),\n              onClick: () => setFilterOpen(true),\n              sx: {\n                mr: 1\n              },\n              children: \"Filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 28\n              }, this),\n              onClick: () => setCreateMeetingOpen(true),\n              children: \"Schedule Meeting\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Meeting Topic\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Client\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Date/Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Duration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Sentiment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: meetings.map(meeting => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      children: meeting.subject\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"textSecondary\",\n                      children: meeting.meeting_type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: meeting.client_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"textSecondary\",\n                      children: meeting.client_industry\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: new Date(meeting.meeting_date).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [meeting.meeting_duration_minutes || 'N/A', \" min\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: meeting.status,\n                    color: getStatusColor(meeting.status),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: meeting.sentiment_score > 0 ? 'Positive' : meeting.sentiment_score < 0 ? 'Negative' : 'Neutral',\n                    color: meeting.sentiment_score > 0 ? 'success' : meeting.sentiment_score < 0 ? 'error' : 'default',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [meeting.status === 'scheduled' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"primary\",\n                      onClick: () => handleStartMeeting(meeting.zoom_meeting_id),\n                      title: \"Start Meeting\",\n                      children: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 388,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"success\",\n                      onClick: () => handleJoinMeeting(meeting.zoom_meeting_id),\n                      title: \"Join Meeting\",\n                      children: /*#__PURE__*/_jsxDEV(VideoCallIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 396,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    color: \"default\",\n                    title: \"View Details\",\n                    children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    color: \"default\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this)]\n              }, meeting.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: activeTab,\n        index: 1,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Meeting Trends\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Line, {\n                  data: chartData.meetingTrends\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Client Sentiment Distribution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Pie, {\n                  data: chartData.clientSentiment\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Meeting Types\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Pie, {\n                  data: chartData.meetingTypes\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Key Insights\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n                        color: \"success\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 456,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Client satisfaction improved by 15%\",\n                      secondary: \"Based on sentiment analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n                        color: \"info\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 465,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Average meeting duration optimized\",\n                      secondary: \"45 minutes is the sweet spot\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(GroupIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 474,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Top performing client: TechCorp\",\n                      secondary: \"Highest engagement scores\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 476,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: activeTab,\n        index: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Client Relationship Insights\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Client Engagement Timeline\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"info\",\n                  children: \"AI-powered insights show client engagement patterns and recommend optimal follow-up timing.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Top Clients This Month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  children: (analytics.topClients || []).map((client, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: client.name,\n                      secondary: `${client.meetings} meetings, ${client.sentiment} sentiment`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 25\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: activeTab,\n        index: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Generate Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Available Reports\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Monthly Meeting Summary\",\n                      secondary: \"Comprehensive overview of all meetings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 547,\n                        columnNumber: 42\n                      }, this),\n                      size: \"small\",\n                      children: \"Download\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Client Sentiment Analysis\",\n                      secondary: \"Detailed sentiment trends and insights\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 42\n                      }, this),\n                      size: \"small\",\n                      children: \"Download\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 556,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Performance Metrics\",\n                      secondary: \"Meeting efficiency and outcomes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 561,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 565,\n                        columnNumber: 42\n                      }, this),\n                      size: \"small\",\n                      children: \"Download\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: createMeetingOpen,\n      onClose: () => setCreateMeetingOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Schedule New Meeting\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Meeting Topic\",\n              value: newMeeting.topic,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                topic: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Client Name\",\n              value: newMeeting.client_name,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                client_name: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Client Industry\",\n              value: newMeeting.client_industry,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                client_industry: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"datetime-local\",\n              label: \"Start Time\",\n              value: newMeeting.start_time,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                start_time: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"number\",\n              label: \"Duration (minutes)\",\n              value: newMeeting.duration,\n              onChange: e => setNewMeeting({\n                ...newMeeting,\n                duration: parseInt(e.target.value)\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Meeting Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: newMeeting.meeting_type,\n                onChange: e => setNewMeeting({\n                  ...newMeeting,\n                  meeting_type: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"client_call\",\n                  children: \"Client Call\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"internal\",\n                  children: \"Internal Meeting\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"presentation\",\n                  children: \"Presentation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"training\",\n                  children: \"Training\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setCreateMeetingOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateMeeting,\n          variant: \"contained\",\n          children: \"Schedule Meeting\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n};\n_s(ManagerDashboard, \"sk5pCXep0gUMQxGtC+eCvD2zvjs=\", false, function () {\n  return [useAuth];\n});\n_c = ManagerDashboard;\nexport default ManagerDashboard;\nvar _c;\n$RefreshReg$(_c, \"ManagerDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Tabs", "Tab", "List", "ListItem", "ListItemText", "ListItemIcon", "VideoCall", "VideoCallIcon", "Analytics", "AnalyticsIcon", "Assignment", "AssignmentIcon", "TrendingUp", "TrendingUpIcon", "Add", "AddIcon", "Edit", "EditIcon", "PlayArrow", "PlayIcon", "Visibility", "ViewIcon", "Download", "DownloadIcon", "Search", "SearchIcon", "FilterList", "FilterIcon", "Schedule", "ScheduleIcon", "Group", "GroupIcon", "Line", "Bar", "Pie", "useAuth", "meetingService", "zoomService", "analyticsService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ManagerDashboard", "_s", "user", "activeTab", "setActiveTab", "meetings", "setMeetings", "analytics", "setAnalytics", "loading", "setLoading", "createMeetingOpen", "setCreateMeetingOpen", "filterOpen", "setFilterOpen", "filters", "setFilters", "date<PERSON><PERSON><PERSON>", "status", "client", "meetingType", "newMeeting", "setNewMeeting", "topic", "client_name", "client_industry", "start_time", "duration", "host_email", "email", "meeting_type", "loadManagerData", "meetingsData", "analyticsData", "Promise", "all", "getMeetingsByManager", "id", "getManagerAnalytics", "data", "error", "console", "handleCreateMeeting", "meetingData", "enable_nlp_analysis", "created_by", "settings", "auto_recording", "waiting_room", "mute_upon_entry", "participant_video", "createMeetingWithNLP", "resetNewMeeting", "handleJoinMeeting", "meetingId", "joinInfo", "joinMeeting", "window", "open", "join_url", "handleStartMeeting", "startInfo", "startMeeting", "start_url", "getStatusColor", "chartData", "meetingTrends", "labels", "datasets", "label", "weeklyMeetings", "borderColor", "backgroundColor", "tension", "clientSentiment", "sentimentDistribution", "meetingTypes", "meetingTypeDistribution", "TabPanel", "children", "value", "index", "hidden", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "container", "spacing", "mb", "item", "xs", "sm", "md", "display", "alignItems", "color", "mr", "monthlyMeetings", "averageSentiment", "activeClients", "averageDuration", "borderBottom", "onChange", "e", "newValue", "justifyContent", "startIcon", "onClick", "component", "map", "meeting", "fontWeight", "subject", "Date", "meeting_date", "toLocaleString", "meeting_duration_minutes", "size", "sentiment_score", "zoom_meeting_id", "title", "primary", "secondary", "severity", "topClients", "name", "sentiment", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "mt", "target", "type", "InputLabelProps", "shrink", "parseInt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/smartcoverage/frontend/src/components/dashboards/ManagerDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON>,\n  <PERSON>,\n  CardContent,\n  Ty<PERSON>graphy,\n  Box,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Alert,\n  Tabs,\n  Tab,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon\n} from '@mui/material';\nimport {\n  VideoCall as VideoCallIcon,\n  Analytics as AnalyticsIcon,\n  Assignment as AssignmentIcon,\n  TrendingUp as TrendingUpIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  PlayArrow as PlayIcon,\n  Visibility as ViewIcon,\n  Download as DownloadIcon,\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  Schedule as ScheduleIcon,\n  Group as GroupIcon\n} from '@mui/icons-material';\nimport { Line, Bar, Pie } from 'react-chartjs-2';\nimport '../../utils/chartSetup'; // Import Chart.js setup\nimport { useAuth } from '../../contexts/AuthContext';\nimport { meetingService } from '../../services/meetingService';\nimport { zoomService } from '../../services/zoomService';\nimport { analyticsService } from '../../services/analyticsService';\n\nconst ManagerDashboard = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState(0);\n  const [meetings, setMeetings] = useState([]);\n  const [analytics, setAnalytics] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [createMeetingOpen, setCreateMeetingOpen] = useState(false);\n  const [filterOpen, setFilterOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    dateRange: 'week',\n    status: 'all',\n    client: '',\n    meetingType: 'all'\n  });\n  const [newMeeting, setNewMeeting] = useState({\n    topic: '',\n    client_name: '',\n    client_industry: '',\n    start_time: '',\n    duration: 60,\n    host_email: user?.email || '',\n    meeting_type: 'client_call'\n  });\n\n  useEffect(() => {\n    loadManagerData();\n  }, [filters]);\n\n  const loadManagerData = async () => {\n    try {\n      setLoading(true);\n      const [meetingsData, analyticsData] = await Promise.all([\n        meetingService.getMeetingsByManager(user.id, filters),\n        analyticsService.getManagerAnalytics(user.id, filters)\n      ]);\n\n      setMeetings(meetingsData.data || []);\n      setAnalytics(analyticsData.data || {});\n    } catch (error) {\n      console.error('Failed to load manager data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateMeeting = async () => {\n    try {\n      const meetingData = {\n        ...newMeeting,\n        enable_nlp_analysis: true,\n        created_by: user.id,\n        settings: {\n          auto_recording: 'cloud',\n          waiting_room: true,\n          mute_upon_entry: true,\n          participant_video: true\n        }\n      };\n\n      await zoomService.createMeetingWithNLP(meetingData);\n      setCreateMeetingOpen(false);\n      resetNewMeeting();\n      loadManagerData();\n    } catch (error) {\n      console.error('Failed to create meeting:', error);\n    }\n  };\n\n  const resetNewMeeting = () => {\n    setNewMeeting({\n      topic: '',\n      client_name: '',\n      client_industry: '',\n      start_time: '',\n      duration: 60,\n      host_email: user?.email || '',\n      meeting_type: 'client_call'\n    });\n  };\n\n  const handleJoinMeeting = async (meetingId) => {\n    try {\n      const joinInfo = await zoomService.joinMeeting(meetingId);\n      window.open(joinInfo.join_url, '_blank');\n    } catch (error) {\n      console.error('Failed to join meeting:', error);\n    }\n  };\n\n  const handleStartMeeting = async (meetingId) => {\n    try {\n      const startInfo = await zoomService.startMeeting(meetingId);\n      window.open(startInfo.start_url, '_blank');\n    } catch (error) {\n      console.error('Failed to start meeting:', error);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'scheduled': return 'primary';\n      case 'in_progress': return 'success';\n      case 'completed': return 'default';\n      case 'cancelled': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const chartData = {\n    meetingTrends: {\n      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n      datasets: [{\n        label: 'Meetings Conducted',\n        data: analytics.weeklyMeetings || [8, 12, 15, 10],\n        borderColor: 'rgb(54, 162, 235)',\n        backgroundColor: 'rgba(54, 162, 235, 0.2)',\n        tension: 0.1\n      }]\n    },\n    clientSentiment: {\n      labels: ['Very Positive', 'Positive', 'Neutral', 'Negative', 'Very Negative'],\n      datasets: [{\n        data: analytics.sentimentDistribution || [25, 35, 25, 10, 5],\n        backgroundColor: ['#4CAF50', '#8BC34A', '#FFC107', '#FF9800', '#F44336']\n      }]\n    },\n    meetingTypes: {\n      labels: ['Client Calls', 'Internal', 'Presentations', 'Training'],\n      datasets: [{\n        data: analytics.meetingTypeDistribution || [45, 25, 20, 10],\n        backgroundColor: ['#2196F3', '#9C27B0', '#FF5722', '#607D8B']\n      }]\n    }\n  };\n\n  const TabPanel = ({ children, value, index }) => (\n    <div hidden={value !== index}>\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Manager Dashboard - Records & Analytics\n      </Typography>\n\n      {/* Quick Stats */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <VideoCallIcon color=\"primary\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    This Month\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {analytics.monthlyMeetings || 0}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"success.main\">\n                    +12% from last month\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <TrendingUpIcon color=\"success\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Avg Sentiment\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {analytics.averageSentiment || 'Positive'}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"success.main\">\n                    +5% improvement\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <GroupIcon color=\"info\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Active Clients\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {analytics.activeClients || 0}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"info.main\">\n                    Across all meetings\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <ScheduleIcon color=\"warning\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Avg Duration\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {analytics.averageDuration || '45m'}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"warning.main\">\n                    Optimal range\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Tabs for different views */}\n      <Card>\n        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>\n            <Tab label=\"Meeting Records\" />\n            <Tab label=\"Analytics\" />\n            <Tab label=\"Client Insights\" />\n            <Tab label=\"Reports\" />\n          </Tabs>\n        </Box>\n\n        {/* Meeting Records Tab */}\n        <TabPanel value={activeTab} index={0}>\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n            <Typography variant=\"h6\">\n              Meeting Records Management\n            </Typography>\n            <Box>\n              <Button\n                startIcon={<FilterIcon />}\n                onClick={() => setFilterOpen(true)}\n                sx={{ mr: 1 }}\n              >\n                Filter\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setCreateMeetingOpen(true)}\n              >\n                Schedule Meeting\n              </Button>\n            </Box>\n          </Box>\n\n          <TableContainer component={Paper}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Meeting Topic</TableCell>\n                  <TableCell>Client</TableCell>\n                  <TableCell>Date/Time</TableCell>\n                  <TableCell>Duration</TableCell>\n                  <TableCell>Status</TableCell>\n                  <TableCell>Sentiment</TableCell>\n                  <TableCell>Actions</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {meetings.map((meeting) => (\n                  <TableRow key={meeting.id}>\n                    <TableCell>\n                      <Box>\n                        <Typography variant=\"body2\" fontWeight=\"bold\">\n                          {meeting.subject}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"textSecondary\">\n                          {meeting.meeting_type}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>\n                      <Box>\n                        <Typography variant=\"body2\">\n                          {meeting.client_name}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"textSecondary\">\n                          {meeting.client_industry}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>\n                      {new Date(meeting.meeting_date).toLocaleString()}\n                    </TableCell>\n                    <TableCell>\n                      {meeting.meeting_duration_minutes || 'N/A'} min\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={meeting.status}\n                        color={getStatusColor(meeting.status)}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={meeting.sentiment_score > 0 ? 'Positive' : meeting.sentiment_score < 0 ? 'Negative' : 'Neutral'}\n                        color={meeting.sentiment_score > 0 ? 'success' : meeting.sentiment_score < 0 ? 'error' : 'default'}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {meeting.status === 'scheduled' && (\n                        <>\n                          <IconButton\n                            size=\"small\"\n                            color=\"primary\"\n                            onClick={() => handleStartMeeting(meeting.zoom_meeting_id)}\n                            title=\"Start Meeting\"\n                          >\n                            <PlayIcon />\n                          </IconButton>\n                          <IconButton\n                            size=\"small\"\n                            color=\"success\"\n                            onClick={() => handleJoinMeeting(meeting.zoom_meeting_id)}\n                            title=\"Join Meeting\"\n                          >\n                            <VideoCallIcon />\n                          </IconButton>\n                        </>\n                      )}\n                      <IconButton size=\"small\" color=\"default\" title=\"View Details\">\n                        <ViewIcon />\n                      </IconButton>\n                      <IconButton size=\"small\" color=\"default\" title=\"Edit\">\n                        <EditIcon />\n                      </IconButton>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </TabPanel>\n\n        {/* Analytics Tab */}\n        <TabPanel value={activeTab} index={1}>\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Meeting Trends\n                  </Typography>\n                  <Line data={chartData.meetingTrends} />\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Client Sentiment Distribution\n                  </Typography>\n                  <Pie data={chartData.clientSentiment} />\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Meeting Types\n                  </Typography>\n                  <Pie data={chartData.meetingTypes} />\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Key Insights\n                  </Typography>\n                  <List>\n                    <ListItem>\n                      <ListItemIcon>\n                        <TrendingUpIcon color=\"success\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary=\"Client satisfaction improved by 15%\"\n                        secondary=\"Based on sentiment analysis\"\n                      />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <AnalyticsIcon color=\"info\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary=\"Average meeting duration optimized\"\n                        secondary=\"45 minutes is the sweet spot\"\n                      />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <GroupIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary=\"Top performing client: TechCorp\"\n                        secondary=\"Highest engagement scores\"\n                      />\n                    </ListItem>\n                  </List>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </TabPanel>\n\n        {/* Client Insights Tab */}\n        <TabPanel value={activeTab} index={2}>\n          <Typography variant=\"h6\" gutterBottom>\n            Client Relationship Insights\n          </Typography>\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={8}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Client Engagement Timeline\n                  </Typography>\n                  {/* Client engagement chart would go here */}\n                  <Alert severity=\"info\">\n                    AI-powered insights show client engagement patterns and recommend optimal follow-up timing.\n                  </Alert>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Top Clients This Month\n                  </Typography>\n                  <List>\n                    {(analytics.topClients || []).map((client, index) => (\n                      <ListItem key={index}>\n                        <ListItemText\n                          primary={client.name}\n                          secondary={`${client.meetings} meetings, ${client.sentiment} sentiment`}\n                        />\n                      </ListItem>\n                    ))}\n                  </List>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </TabPanel>\n\n        {/* Reports Tab */}\n        <TabPanel value={activeTab} index={3}>\n          <Typography variant=\"h6\" gutterBottom>\n            Generate Reports\n          </Typography>\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Available Reports\n                  </Typography>\n                  <List>\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Monthly Meeting Summary\"\n                        secondary=\"Comprehensive overview of all meetings\"\n                      />\n                      <Button startIcon={<DownloadIcon />} size=\"small\">\n                        Download\n                      </Button>\n                    </ListItem>\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Client Sentiment Analysis\"\n                        secondary=\"Detailed sentiment trends and insights\"\n                      />\n                      <Button startIcon={<DownloadIcon />} size=\"small\">\n                        Download\n                      </Button>\n                    </ListItem>\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Performance Metrics\"\n                        secondary=\"Meeting efficiency and outcomes\"\n                      />\n                      <Button startIcon={<DownloadIcon />} size=\"small\">\n                        Download\n                      </Button>\n                    </ListItem>\n                  </List>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </TabPanel>\n      </Card>\n\n      {/* Create Meeting Dialog */}\n      <Dialog open={createMeetingOpen} onClose={() => setCreateMeetingOpen(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>Schedule New Meeting</DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Meeting Topic\"\n                value={newMeeting.topic}\n                onChange={(e) => setNewMeeting({...newMeeting, topic: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                label=\"Client Name\"\n                value={newMeeting.client_name}\n                onChange={(e) => setNewMeeting({...newMeeting, client_name: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                label=\"Client Industry\"\n                value={newMeeting.client_industry}\n                onChange={(e) => setNewMeeting({...newMeeting, client_industry: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                type=\"datetime-local\"\n                label=\"Start Time\"\n                value={newMeeting.start_time}\n                onChange={(e) => setNewMeeting({...newMeeting, start_time: e.target.value})}\n                InputLabelProps={{ shrink: true }}\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                type=\"number\"\n                label=\"Duration (minutes)\"\n                value={newMeeting.duration}\n                onChange={(e) => setNewMeeting({...newMeeting, duration: parseInt(e.target.value)})}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <FormControl fullWidth>\n                <InputLabel>Meeting Type</InputLabel>\n                <Select\n                  value={newMeeting.meeting_type}\n                  onChange={(e) => setNewMeeting({...newMeeting, meeting_type: e.target.value})}\n                >\n                  <MenuItem value=\"client_call\">Client Call</MenuItem>\n                  <MenuItem value=\"internal\">Internal Meeting</MenuItem>\n                  <MenuItem value=\"presentation\">Presentation</MenuItem>\n                  <MenuItem value=\"training\">Training</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setCreateMeetingOpen(false)}>Cancel</Button>\n          <Button onClick={handleCreateMeeting} variant=\"contained\">\n            Schedule Meeting\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ManagerDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,QACP,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,QAAQ,EACrBC,UAAU,IAAIC,QAAQ,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AAChD,OAAO,wBAAwB,CAAC,CAAC;AACjC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,gBAAgB,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6E,SAAS,EAAEC,YAAY,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC+E,OAAO,EAAEC,UAAU,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmF,UAAU,EAAEC,aAAa,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqF,OAAO,EAAEC,UAAU,CAAC,GAAGtF,QAAQ,CAAC;IACrCuF,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC;IAC3C6F,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK,KAAI,EAAE;IAC7BC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFnG,SAAS,CAAC,MAAM;IACdoG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAMgB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFrB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACsB,YAAY,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACtD1C,cAAc,CAAC2C,oBAAoB,CAAClC,IAAI,CAACmC,EAAE,EAAEtB,OAAO,CAAC,EACrDpB,gBAAgB,CAAC2C,mBAAmB,CAACpC,IAAI,CAACmC,EAAE,EAAEtB,OAAO,CAAC,CACvD,CAAC;MAEFT,WAAW,CAAC0B,YAAY,CAACO,IAAI,IAAI,EAAE,CAAC;MACpC/B,YAAY,CAACyB,aAAa,CAACM,IAAI,IAAI,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,WAAW,GAAG;QAClB,GAAGtB,UAAU;QACbuB,mBAAmB,EAAE,IAAI;QACzBC,UAAU,EAAE3C,IAAI,CAACmC,EAAE;QACnBS,QAAQ,EAAE;UACRC,cAAc,EAAE,OAAO;UACvBC,YAAY,EAAE,IAAI;UAClBC,eAAe,EAAE,IAAI;UACrBC,iBAAiB,EAAE;QACrB;MACF,CAAC;MAED,MAAMxD,WAAW,CAACyD,oBAAoB,CAACR,WAAW,CAAC;MACnD/B,oBAAoB,CAAC,KAAK,CAAC;MAC3BwC,eAAe,CAAC,CAAC;MACjBrB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5B9B,aAAa,CAAC;MACZC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnBC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK,KAAI,EAAE;MAC7BC,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMuB,iBAAiB,GAAG,MAAOC,SAAS,IAAK;IAC7C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM7D,WAAW,CAAC8D,WAAW,CAACF,SAAS,CAAC;MACzDG,MAAM,CAACC,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE,QAAQ,CAAC;IAC1C,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMoB,kBAAkB,GAAG,MAAON,SAAS,IAAK;IAC9C,IAAI;MACF,MAAMO,SAAS,GAAG,MAAMnE,WAAW,CAACoE,YAAY,CAACR,SAAS,CAAC;MAC3DG,MAAM,CAACC,IAAI,CAACG,SAAS,CAACE,SAAS,EAAE,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMwB,cAAc,GAAI9C,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM+C,SAAS,GAAG;IAChBC,aAAa,EAAE;MACbC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;MAChDC,QAAQ,EAAE,CAAC;QACTC,KAAK,EAAE,oBAAoB;QAC3B9B,IAAI,EAAEhC,SAAS,CAAC+D,cAAc,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACjDC,WAAW,EAAE,mBAAmB;QAChCC,eAAe,EAAE,yBAAyB;QAC1CC,OAAO,EAAE;MACX,CAAC;IACH,CAAC;IACDC,eAAe,EAAE;MACfP,MAAM,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,CAAC;MAC7EC,QAAQ,EAAE,CAAC;QACT7B,IAAI,EAAEhC,SAAS,CAACoE,qBAAqB,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC5DH,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;MACzE,CAAC;IACH,CAAC;IACDI,YAAY,EAAE;MACZT,MAAM,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC;MACjEC,QAAQ,EAAE,CAAC;QACT7B,IAAI,EAAEhC,SAAS,CAACsE,uBAAuB,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAC3DL,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;MAC9D,CAAC;IACH;EACF,CAAC;EAED,MAAMM,QAAQ,GAAGA,CAAC;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,kBAC1CpF,OAAA;IAAKqF,MAAM,EAAEF,KAAK,KAAKC,KAAM;IAAAF,QAAA,EAC1BC,KAAK,KAAKC,KAAK,iBAAIpF,OAAA,CAAC7D,GAAG;MAACmJ,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAL,QAAA,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CACN;EAED,oBACE3F,OAAA,CAAC7D,GAAG;IAACmJ,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAL,QAAA,gBAChBlF,OAAA,CAAC9D,UAAU;MAAC0J,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAX,QAAA,EAAC;IAEtC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb3F,OAAA,CAACjE,IAAI;MAAC+J,SAAS;MAACC,OAAO,EAAE,CAAE;MAACT,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAd,QAAA,gBACxClF,OAAA,CAACjE,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eAC9BlF,OAAA,CAAChE,IAAI;UAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;YAAAiJ,QAAA,eACVlF,OAAA,CAAC7D,GAAG;cAACkK,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAApB,QAAA,gBACrClF,OAAA,CAACjC,aAAa;gBAACwI,KAAK,EAAC,SAAS;gBAACjB,EAAE,EAAE;kBAAEkB,EAAE,EAAE;gBAAE;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChD3F,OAAA,CAAC7D,GAAG;gBAAA+I,QAAA,gBACFlF,OAAA,CAAC9D,UAAU;kBAACqK,KAAK,EAAC,eAAe;kBAACV,YAAY;kBAAAX,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAAAV,QAAA,EACrBxE,SAAS,CAAC+F,eAAe,IAAI;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACb3F,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,SAAS;kBAACW,KAAK,EAAC,cAAc;kBAAArB,QAAA,EAAC;gBAEnD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP3F,OAAA,CAACjE,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eAC9BlF,OAAA,CAAChE,IAAI;UAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;YAAAiJ,QAAA,eACVlF,OAAA,CAAC7D,GAAG;cAACkK,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAApB,QAAA,gBACrClF,OAAA,CAAC3B,cAAc;gBAACkI,KAAK,EAAC,SAAS;gBAACjB,EAAE,EAAE;kBAAEkB,EAAE,EAAE;gBAAE;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjD3F,OAAA,CAAC7D,GAAG;gBAAA+I,QAAA,gBACFlF,OAAA,CAAC9D,UAAU;kBAACqK,KAAK,EAAC,eAAe;kBAACV,YAAY;kBAAAX,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAAAV,QAAA,EACrBxE,SAAS,CAACgG,gBAAgB,IAAI;gBAAU;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACb3F,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,SAAS;kBAACW,KAAK,EAAC,cAAc;kBAAArB,QAAA,EAAC;gBAEnD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP3F,OAAA,CAACjE,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eAC9BlF,OAAA,CAAChE,IAAI;UAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;YAAAiJ,QAAA,eACVlF,OAAA,CAAC7D,GAAG;cAACkK,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAApB,QAAA,gBACrClF,OAAA,CAACT,SAAS;gBAACgH,KAAK,EAAC,MAAM;gBAACjB,EAAE,EAAE;kBAAEkB,EAAE,EAAE;gBAAE;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC3F,OAAA,CAAC7D,GAAG;gBAAA+I,QAAA,gBACFlF,OAAA,CAAC9D,UAAU;kBAACqK,KAAK,EAAC,eAAe;kBAACV,YAAY;kBAAAX,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAAAV,QAAA,EACrBxE,SAAS,CAACiG,aAAa,IAAI;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACb3F,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,SAAS;kBAACW,KAAK,EAAC,WAAW;kBAAArB,QAAA,EAAC;gBAEhD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP3F,OAAA,CAACjE,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eAC9BlF,OAAA,CAAChE,IAAI;UAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;YAAAiJ,QAAA,eACVlF,OAAA,CAAC7D,GAAG;cAACkK,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAApB,QAAA,gBACrClF,OAAA,CAACX,YAAY;gBAACkH,KAAK,EAAC,SAAS;gBAACjB,EAAE,EAAE;kBAAEkB,EAAE,EAAE;gBAAE;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C3F,OAAA,CAAC7D,GAAG;gBAAA+I,QAAA,gBACFlF,OAAA,CAAC9D,UAAU;kBAACqK,KAAK,EAAC,eAAe;kBAACV,YAAY;kBAAAX,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAAAV,QAAA,EACrBxE,SAAS,CAACkG,eAAe,IAAI;gBAAK;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACb3F,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,SAAS;kBAACW,KAAK,EAAC,cAAc;kBAAArB,QAAA,EAAC;gBAEnD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP3F,OAAA,CAAChE,IAAI;MAAAkJ,QAAA,gBACHlF,OAAA,CAAC7D,GAAG;QAACmJ,EAAE,EAAE;UAAEuB,YAAY,EAAE,CAAC;UAAEnC,WAAW,EAAE;QAAU,CAAE;QAAAQ,QAAA,eACnDlF,OAAA,CAACxC,IAAI;UAAC2H,KAAK,EAAE7E,SAAU;UAACwG,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAKzG,YAAY,CAACyG,QAAQ,CAAE;UAAA9B,QAAA,gBACxElF,OAAA,CAACvC,GAAG;YAAC+G,KAAK,EAAC;UAAiB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B3F,OAAA,CAACvC,GAAG;YAAC+G,KAAK,EAAC;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzB3F,OAAA,CAACvC,GAAG;YAAC+G,KAAK,EAAC;UAAiB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B3F,OAAA,CAACvC,GAAG;YAAC+G,KAAK,EAAC;UAAS;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3F,OAAA,CAACiF,QAAQ;QAACE,KAAK,EAAE7E,SAAU;QAAC8E,KAAK,EAAE,CAAE;QAAAF,QAAA,gBACnClF,OAAA,CAAC7D,GAAG;UAACkK,OAAO,EAAC,MAAM;UAACY,cAAc,EAAC,eAAe;UAACX,UAAU,EAAC,QAAQ;UAACN,EAAE,EAAE,CAAE;UAAAd,QAAA,gBAC3ElF,OAAA,CAAC9D,UAAU;YAAC0J,OAAO,EAAC,IAAI;YAAAV,QAAA,EAAC;UAEzB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3F,OAAA,CAAC7D,GAAG;YAAA+I,QAAA,gBACFlF,OAAA,CAAC5D,MAAM;cACL8K,SAAS,eAAElH,OAAA,CAACb,UAAU;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BwB,OAAO,EAAEA,CAAA,KAAMlG,aAAa,CAAC,IAAI,CAAE;cACnCqE,EAAE,EAAE;gBAAEkB,EAAE,EAAE;cAAE,CAAE;cAAAtB,QAAA,EACf;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3F,OAAA,CAAC5D,MAAM;cACLwJ,OAAO,EAAC,WAAW;cACnBsB,SAAS,eAAElH,OAAA,CAACzB,OAAO;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBwB,OAAO,EAAEA,CAAA,KAAMpG,oBAAoB,CAAC,IAAI,CAAE;cAAAmE,QAAA,EAC3C;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3F,OAAA,CAACxD,cAAc;UAAC4K,SAAS,EAAEzK,KAAM;UAAAuI,QAAA,eAC/BlF,OAAA,CAAC3D,KAAK;YAAA6I,QAAA,gBACJlF,OAAA,CAACvD,SAAS;cAAAyI,QAAA,eACRlF,OAAA,CAACtD,QAAQ;gBAAAwI,QAAA,gBACPlF,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpC3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7B3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChC3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/B3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7B3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChC3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ3F,OAAA,CAAC1D,SAAS;cAAA4I,QAAA,EACP1E,QAAQ,CAAC6G,GAAG,CAAEC,OAAO,iBACpBtH,OAAA,CAACtD,QAAQ;gBAAAwI,QAAA,gBACPlF,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,eACRlF,OAAA,CAAC7D,GAAG;oBAAA+I,QAAA,gBACFlF,OAAA,CAAC9D,UAAU;sBAAC0J,OAAO,EAAC,OAAO;sBAAC2B,UAAU,EAAC,MAAM;sBAAArC,QAAA,EAC1CoC,OAAO,CAACE;oBAAO;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACb3F,OAAA,CAAC9D,UAAU;sBAAC0J,OAAO,EAAC,SAAS;sBAACW,KAAK,EAAC,eAAe;sBAAArB,QAAA,EAChDoC,OAAO,CAACrF;oBAAY;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZ3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,eACRlF,OAAA,CAAC7D,GAAG;oBAAA+I,QAAA,gBACFlF,OAAA,CAAC9D,UAAU;sBAAC0J,OAAO,EAAC,OAAO;sBAAAV,QAAA,EACxBoC,OAAO,CAAC3F;oBAAW;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACb3F,OAAA,CAAC9D,UAAU;sBAAC0J,OAAO,EAAC,SAAS;sBAACW,KAAK,EAAC,eAAe;sBAAArB,QAAA,EAChDoC,OAAO,CAAC1F;oBAAe;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZ3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,EACP,IAAIuC,IAAI,CAACH,OAAO,CAACI,YAAY,CAAC,CAACC,cAAc,CAAC;gBAAC;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACZ3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,GACPoC,OAAO,CAACM,wBAAwB,IAAI,KAAK,EAAC,MAC7C;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZ3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,eACRlF,OAAA,CAACpD,IAAI;oBACH4H,KAAK,EAAE8C,OAAO,CAACjG,MAAO;oBACtBkF,KAAK,EAAEpC,cAAc,CAACmD,OAAO,CAACjG,MAAM,CAAE;oBACtCwG,IAAI,EAAC;kBAAO;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,eACRlF,OAAA,CAACpD,IAAI;oBACH4H,KAAK,EAAE8C,OAAO,CAACQ,eAAe,GAAG,CAAC,GAAG,UAAU,GAAGR,OAAO,CAACQ,eAAe,GAAG,CAAC,GAAG,UAAU,GAAG,SAAU;oBACvGvB,KAAK,EAAEe,OAAO,CAACQ,eAAe,GAAG,CAAC,GAAG,SAAS,GAAGR,OAAO,CAACQ,eAAe,GAAG,CAAC,GAAG,OAAO,GAAG,SAAU;oBACnGD,IAAI,EAAC;kBAAO;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ3F,OAAA,CAACzD,SAAS;kBAAA2I,QAAA,GACPoC,OAAO,CAACjG,MAAM,KAAK,WAAW,iBAC7BrB,OAAA,CAAAE,SAAA;oBAAAgF,QAAA,gBACElF,OAAA,CAACnD,UAAU;sBACTgL,IAAI,EAAC,OAAO;sBACZtB,KAAK,EAAC,SAAS;sBACfY,OAAO,EAAEA,CAAA,KAAMpD,kBAAkB,CAACuD,OAAO,CAACS,eAAe,CAAE;sBAC3DC,KAAK,EAAC,eAAe;sBAAA9C,QAAA,eAErBlF,OAAA,CAACrB,QAAQ;wBAAA6G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACb3F,OAAA,CAACnD,UAAU;sBACTgL,IAAI,EAAC,OAAO;sBACZtB,KAAK,EAAC,SAAS;sBACfY,OAAO,EAAEA,CAAA,KAAM3D,iBAAiB,CAAC8D,OAAO,CAACS,eAAe,CAAE;sBAC1DC,KAAK,EAAC,cAAc;sBAAA9C,QAAA,eAEpBlF,OAAA,CAACjC,aAAa;wBAAAyH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA,eACb,CACH,eACD3F,OAAA,CAACnD,UAAU;oBAACgL,IAAI,EAAC,OAAO;oBAACtB,KAAK,EAAC,SAAS;oBAACyB,KAAK,EAAC,cAAc;oBAAA9C,QAAA,eAC3DlF,OAAA,CAACnB,QAAQ;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACb3F,OAAA,CAACnD,UAAU;oBAACgL,IAAI,EAAC,OAAO;oBAACtB,KAAK,EAAC,SAAS;oBAACyB,KAAK,EAAC,MAAM;oBAAA9C,QAAA,eACnDlF,OAAA,CAACvB,QAAQ;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GApEC2B,OAAO,CAAC9E,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqEf,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGX3F,OAAA,CAACiF,QAAQ;QAACE,KAAK,EAAE7E,SAAU;QAAC8E,KAAK,EAAE,CAAE;QAAAF,QAAA,eACnClF,OAAA,CAACjE,IAAI;UAAC+J,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAb,QAAA,gBACzBlF,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACvBlF,OAAA,CAAChE,IAAI;cAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;gBAAAiJ,QAAA,gBACVlF,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAX,QAAA,EAAC;gBAEtC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAACR,IAAI;kBAACkD,IAAI,EAAE0B,SAAS,CAACC;gBAAc;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP3F,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACvBlF,OAAA,CAAChE,IAAI;cAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;gBAAAiJ,QAAA,gBACVlF,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAX,QAAA,EAAC;gBAEtC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAACN,GAAG;kBAACgD,IAAI,EAAE0B,SAAS,CAACS;gBAAgB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP3F,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACvBlF,OAAA,CAAChE,IAAI;cAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;gBAAAiJ,QAAA,gBACVlF,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAX,QAAA,EAAC;gBAEtC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAACN,GAAG;kBAACgD,IAAI,EAAE0B,SAAS,CAACW;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP3F,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACvBlF,OAAA,CAAChE,IAAI;cAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;gBAAAiJ,QAAA,gBACVlF,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAX,QAAA,EAAC;gBAEtC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAACtC,IAAI;kBAAAwH,QAAA,gBACHlF,OAAA,CAACrC,QAAQ;oBAAAuH,QAAA,gBACPlF,OAAA,CAACnC,YAAY;sBAAAqH,QAAA,eACXlF,OAAA,CAAC3B,cAAc;wBAACkI,KAAK,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACf3F,OAAA,CAACpC,YAAY;sBACXqK,OAAO,EAAC,qCAAqC;sBAC7CC,SAAS,EAAC;oBAA6B;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACM,CAAC,eACX3F,OAAA,CAACrC,QAAQ;oBAAAuH,QAAA,gBACPlF,OAAA,CAACnC,YAAY;sBAAAqH,QAAA,eACXlF,OAAA,CAAC/B,aAAa;wBAACsI,KAAK,EAAC;sBAAM;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACf3F,OAAA,CAACpC,YAAY;sBACXqK,OAAO,EAAC,oCAAoC;sBAC5CC,SAAS,EAAC;oBAA8B;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACM,CAAC,eACX3F,OAAA,CAACrC,QAAQ;oBAAAuH,QAAA,gBACPlF,OAAA,CAACnC,YAAY;sBAAAqH,QAAA,eACXlF,OAAA,CAACT,SAAS;wBAACgH,KAAK,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACf3F,OAAA,CAACpC,YAAY;sBACXqK,OAAO,EAAC,iCAAiC;sBACzCC,SAAS,EAAC;oBAA2B;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGX3F,OAAA,CAACiF,QAAQ;QAACE,KAAK,EAAE7E,SAAU;QAAC8E,KAAK,EAAE,CAAE;QAAAF,QAAA,gBACnClF,OAAA,CAAC9D,UAAU;UAAC0J,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAX,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3F,OAAA,CAACjE,IAAI;UAAC+J,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAb,QAAA,gBACzBlF,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACvBlF,OAAA,CAAChE,IAAI;cAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;gBAAAiJ,QAAA,gBACVlF,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAX,QAAA,EAAC;gBAEtC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAEb3F,OAAA,CAACzC,KAAK;kBAAC4K,QAAQ,EAAC,MAAM;kBAAAjD,QAAA,EAAC;gBAEvB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP3F,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACvBlF,OAAA,CAAChE,IAAI;cAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;gBAAAiJ,QAAA,gBACVlF,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAX,QAAA,EAAC;gBAEtC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAACtC,IAAI;kBAAAwH,QAAA,EACF,CAACxE,SAAS,CAAC0H,UAAU,IAAI,EAAE,EAAEf,GAAG,CAAC,CAAC/F,MAAM,EAAE8D,KAAK,kBAC9CpF,OAAA,CAACrC,QAAQ;oBAAAuH,QAAA,eACPlF,OAAA,CAACpC,YAAY;sBACXqK,OAAO,EAAE3G,MAAM,CAAC+G,IAAK;sBACrBH,SAAS,EAAE,GAAG5G,MAAM,CAACd,QAAQ,cAAcc,MAAM,CAACgH,SAAS;oBAAa;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE;kBAAC,GAJWP,KAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKV,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGX3F,OAAA,CAACiF,QAAQ;QAACE,KAAK,EAAE7E,SAAU;QAAC8E,KAAK,EAAE,CAAE;QAAAF,QAAA,gBACnClF,OAAA,CAAC9D,UAAU;UAAC0J,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAX,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3F,OAAA,CAACjE,IAAI;UAAC+J,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAb,QAAA,eACzBlF,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACvBlF,OAAA,CAAChE,IAAI;cAAAkJ,QAAA,eACHlF,OAAA,CAAC/D,WAAW;gBAAAiJ,QAAA,gBACVlF,OAAA,CAAC9D,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAX,QAAA,EAAC;gBAEtC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3F,OAAA,CAACtC,IAAI;kBAAAwH,QAAA,gBACHlF,OAAA,CAACrC,QAAQ;oBAAAuH,QAAA,gBACPlF,OAAA,CAACpC,YAAY;sBACXqK,OAAO,EAAC,yBAAyB;sBACjCC,SAAS,EAAC;oBAAwC;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC,eACF3F,OAAA,CAAC5D,MAAM;sBAAC8K,SAAS,eAAElH,OAAA,CAACjB,YAAY;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAACkC,IAAI,EAAC,OAAO;sBAAA3C,QAAA,EAAC;oBAElD;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACX3F,OAAA,CAACrC,QAAQ;oBAAAuH,QAAA,gBACPlF,OAAA,CAACpC,YAAY;sBACXqK,OAAO,EAAC,2BAA2B;sBACnCC,SAAS,EAAC;oBAAwC;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC,eACF3F,OAAA,CAAC5D,MAAM;sBAAC8K,SAAS,eAAElH,OAAA,CAACjB,YAAY;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAACkC,IAAI,EAAC,OAAO;sBAAA3C,QAAA,EAAC;oBAElD;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACX3F,OAAA,CAACrC,QAAQ;oBAAAuH,QAAA,gBACPlF,OAAA,CAACpC,YAAY;sBACXqK,OAAO,EAAC,qBAAqB;sBAC7BC,SAAS,EAAC;oBAAiC;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACF3F,OAAA,CAAC5D,MAAM;sBAAC8K,SAAS,eAAElH,OAAA,CAACjB,YAAY;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAACkC,IAAI,EAAC,OAAO;sBAAA3C,QAAA,EAAC;oBAElD;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGP3F,OAAA,CAAClD,MAAM;MAAC+G,IAAI,EAAE/C,iBAAkB;MAACyH,OAAO,EAAEA,CAAA,KAAMxH,oBAAoB,CAAC,KAAK,CAAE;MAACyH,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAvD,QAAA,gBAClGlF,OAAA,CAACjD,WAAW;QAAAmI,QAAA,EAAC;MAAoB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC/C3F,OAAA,CAAChD,aAAa;QAAAkI,QAAA,eACZlF,OAAA,CAACjE,IAAI;UAAC+J,SAAS;UAACC,OAAO,EAAE,CAAE;UAACT,EAAE,EAAE;YAAEoD,EAAE,EAAE;UAAE,CAAE;UAAAxD,QAAA,gBACxClF,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAChBlF,OAAA,CAAC9C,SAAS;cACRuL,SAAS;cACTjE,KAAK,EAAC,eAAe;cACrBW,KAAK,EAAE3D,UAAU,CAACE,KAAM;cACxBoF,QAAQ,EAAGC,CAAC,IAAKtF,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEE,KAAK,EAAEqF,CAAC,CAAC4B,MAAM,CAACxD;cAAK,CAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP3F,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvBlF,OAAA,CAAC9C,SAAS;cACRuL,SAAS;cACTjE,KAAK,EAAC,aAAa;cACnBW,KAAK,EAAE3D,UAAU,CAACG,WAAY;cAC9BmF,QAAQ,EAAGC,CAAC,IAAKtF,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEG,WAAW,EAAEoF,CAAC,CAAC4B,MAAM,CAACxD;cAAK,CAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP3F,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvBlF,OAAA,CAAC9C,SAAS;cACRuL,SAAS;cACTjE,KAAK,EAAC,iBAAiB;cACvBW,KAAK,EAAE3D,UAAU,CAACI,eAAgB;cAClCkF,QAAQ,EAAGC,CAAC,IAAKtF,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEI,eAAe,EAAEmF,CAAC,CAAC4B,MAAM,CAACxD;cAAK,CAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP3F,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvBlF,OAAA,CAAC9C,SAAS;cACRuL,SAAS;cACTG,IAAI,EAAC,gBAAgB;cACrBpE,KAAK,EAAC,YAAY;cAClBW,KAAK,EAAE3D,UAAU,CAACK,UAAW;cAC7BiF,QAAQ,EAAGC,CAAC,IAAKtF,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEK,UAAU,EAAEkF,CAAC,CAAC4B,MAAM,CAACxD;cAAK,CAAC,CAAE;cAC5E0D,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP3F,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvBlF,OAAA,CAAC9C,SAAS;cACRuL,SAAS;cACTG,IAAI,EAAC,QAAQ;cACbpE,KAAK,EAAC,oBAAoB;cAC1BW,KAAK,EAAE3D,UAAU,CAACM,QAAS;cAC3BgF,QAAQ,EAAGC,CAAC,IAAKtF,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEM,QAAQ,EAAEiH,QAAQ,CAAChC,CAAC,CAAC4B,MAAM,CAACxD,KAAK;cAAC,CAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP3F,OAAA,CAACjE,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAChBlF,OAAA,CAAC7C,WAAW;cAACsL,SAAS;cAAAvD,QAAA,gBACpBlF,OAAA,CAAC5C,UAAU;gBAAA8H,QAAA,EAAC;cAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC3F,OAAA,CAAC3C,MAAM;gBACL8H,KAAK,EAAE3D,UAAU,CAACS,YAAa;gBAC/B6E,QAAQ,EAAGC,CAAC,IAAKtF,aAAa,CAAC;kBAAC,GAAGD,UAAU;kBAAES,YAAY,EAAE8E,CAAC,CAAC4B,MAAM,CAACxD;gBAAK,CAAC,CAAE;gBAAAD,QAAA,gBAE9ElF,OAAA,CAAC1C,QAAQ;kBAAC6H,KAAK,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpD3F,OAAA,CAAC1C,QAAQ;kBAAC6H,KAAK,EAAC,UAAU;kBAAAD,QAAA,EAAC;gBAAgB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtD3F,OAAA,CAAC1C,QAAQ;kBAAC6H,KAAK,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtD3F,OAAA,CAAC1C,QAAQ;kBAAC6H,KAAK,EAAC,UAAU;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB3F,OAAA,CAAC/C,aAAa;QAAAiI,QAAA,gBACZlF,OAAA,CAAC5D,MAAM;UAAC+K,OAAO,EAAEA,CAAA,KAAMpG,oBAAoB,CAAC,KAAK,CAAE;UAAAmE,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnE3F,OAAA,CAAC5D,MAAM;UAAC+K,OAAO,EAAEtE,mBAAoB;UAAC+C,OAAO,EAAC,WAAW;UAAAV,QAAA,EAAC;QAE1D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACvF,EAAA,CAjlBID,gBAAgB;EAAA,QACHR,OAAO;AAAA;AAAAqJ,EAAA,GADpB7I,gBAAgB;AAmlBtB,eAAeA,gBAAgB;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}