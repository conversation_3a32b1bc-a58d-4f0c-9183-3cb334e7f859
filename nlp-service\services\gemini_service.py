"""
Google Gemini AI Service for SmartConverge NLP
Provides advanced text analysis using Google's Gemini models
"""

import os
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

logger = logging.getLogger(__name__)

class GeminiService:
    """Service for interacting with Google Gemini AI"""
    
    def __init__(self):
        self.api_key = os.getenv('GEMINI_API_KEY')
        self.model_name = os.getenv('GEMINI_MODEL', 'gemini-1.5-pro')
        self.model = None
        self.initialized = False
        
        if self.api_key:
            self._initialize()
        else:
            logger.warning("Gemini API key not found. Gemini features will be disabled.")
    
    def _initialize(self):
        """Initialize Gemini AI client"""
        try:
            genai.configure(api_key=self.api_key)
            
            # Configure the model
            generation_config = {
                "temperature": 0.3,
                "top_p": 0.95,
                "top_k": 40,
                "max_output_tokens": 2048,
            }
            
            safety_settings = [
                {
                    "category": HarmCategory.HARM_CATEGORY_HARASSMENT,
                    "threshold": HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
                },
                {
                    "category": HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                    "threshold": HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
                },
                {
                    "category": HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                    "threshold": HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
                },
                {
                    "category": HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    "threshold": HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
                }
            ]
            
            self.model = genai.GenerativeModel(
                model_name=self.model_name,
                generation_config=generation_config,
                safety_settings=safety_settings
            )
            
            self.initialized = True
            logger.info(f"✅ Gemini AI initialized with model: {self.model_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini AI: {e}")
            self.initialized = False
    
    async def generate_content(self, prompt: str, **kwargs) -> str:
        """Generate content using Gemini AI"""
        if not self.initialized:
            raise Exception("Gemini AI not initialized")
        
        try:
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt
            )
            
            if response.text:
                return response.text
            else:
                raise Exception("No text generated from Gemini")
                
        except Exception as e:
            logger.error(f"Gemini content generation failed: {e}")
            raise
    
    async def extract_implicit_tags(self, text: str, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Extract implicit tags and themes from text"""
        if not self.initialized:
            return []
        
        context = context or {}
        
        prompt = f"""
        Analyze the following business meeting text and extract implicit topics, themes, and concepts that are discussed but not explicitly mentioned.
        
        Meeting Context:
        - Client: {context.get('client_name', 'Unknown')}
        - Industry: {context.get('client_industry', 'Unknown')}
        - Meeting Type: {context.get('meeting_type', 'business')}
        
        Focus on:
        - Business strategies and approaches
        - Market conditions and trends
        - Risk factors and concerns
        - Opportunities and potential outcomes
        - Stakeholder relationships
        - Industry-specific concepts
        - Investment themes and financial implications
        
        Return ONLY a valid JSON array of objects with the following structure:
        [{{"name": "tag_name", "confidence": 0.8, "category": "strategy", "reasoning": "brief explanation"}}]
        
        Text: {text}
        """
        
        try:
            response = await self.generate_content(prompt)
            
            # Extract JSON from response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = response[json_start:json_end]
                tags = json.loads(json_str)
                
                # Normalize the tags
                normalized_tags = []
                for tag in tags:
                    normalized_tags.append({
                        'name': tag.get('name', '').lower(),
                        'type': 'implicit',
                        'confidence': min(max(tag.get('confidence', 0.7), 0), 1),
                        'source': 'gemini_ai',
                        'category': tag.get('category', 'general'),
                        'reasoning': tag.get('reasoning', ''),
                        'frequency': 1
                    })
                
                return normalized_tags
            else:
                logger.warning("No valid JSON found in Gemini tag extraction response")
                return []
                
        except Exception as e:
            logger.error(f"Failed to extract implicit tags with Gemini: {e}")
            return []
    
    async def generate_summary(self, text: str, context: Dict[str, Any] = None) -> str:
        """Generate comprehensive meeting summary"""
        if not self.initialized:
            return "Summary generation unavailable - Gemini not initialized"
        
        context = context or {}
        
        prompt = f"""
        Generate a comprehensive summary of this business meeting transcript.
        
        Meeting Context:
        - Client: {context.get('client_name', 'Unknown')}
        - Industry: {context.get('client_industry', 'Unknown')}
        - Meeting Type: {context.get('meeting_type', 'business')}
        - Duration: {context.get('duration', 'Unknown')} minutes
        
        Please provide:
        1. Executive Summary (2-3 sentences)
        2. Key Discussion Points (bullet points)
        3. Decisions Made
        4. Action Items
        5. Next Steps
        6. Important Mentions (people, companies, amounts)
        
        Keep the summary professional, concise, and actionable.
        
        Meeting Transcript:
        {text}
        """
        
        try:
            summary = await self.generate_content(prompt)
            return summary
        except Exception as e:
            logger.error(f"Failed to generate summary with Gemini: {e}")
            return "Summary generation failed. Please try again."
    
    async def analyze_sentiment(self, text: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze sentiment of the text"""
        if not self.initialized:
            return self._get_fallback_sentiment()
        
        context = context or {}
        
        prompt = f"""
        Analyze the sentiment of this business meeting text. Consider the context and provide a detailed sentiment analysis.
        
        Meeting Context:
        - Client: {context.get('client_name', 'Unknown')}
        - Industry: {context.get('client_industry', 'Unknown')}
        
        Return ONLY a valid JSON object with this exact structure:
        {{
          "overall": "positive|negative|neutral",
          "confidence": 0.85,
          "sentiment_score": 0.3,
          "scores": {{
            "positive": 0.6,
            "negative": 0.1,
            "neutral": 0.3
          }},
          "reasoning": "Brief explanation of the sentiment analysis",
          "key_indicators": ["positive phrase 1", "concern mentioned"]
        }}
        
        Text: {text}
        """
        
        try:
            response = await self.generate_content(prompt)
            
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = response[json_start:json_end]
                sentiment = json.loads(json_str)
                
                # Validate and normalize
                return {
                    'overall': sentiment.get('overall', 'neutral'),
                    'confidence': min(max(sentiment.get('confidence', 0.5), 0), 1),
                    'sentiment_score': min(max(sentiment.get('sentiment_score', 0), -1), 1),
                    'scores': {
                        'positive': min(max(sentiment.get('scores', {}).get('positive', 0.33), 0), 1),
                        'negative': min(max(sentiment.get('scores', {}).get('negative', 0.33), 0), 1),
                        'neutral': min(max(sentiment.get('scores', {}).get('neutral', 0.34), 0), 1)
                    },
                    'reasoning': sentiment.get('reasoning', 'Sentiment analysis completed'),
                    'key_indicators': sentiment.get('key_indicators', [])
                }
            else:
                logger.warning("No valid JSON found in Gemini sentiment response")
                return self._get_fallback_sentiment()
                
        except Exception as e:
            logger.error(f"Failed to analyze sentiment with Gemini: {e}")
            return self._get_fallback_sentiment()
    
    async def extract_insights(self, text: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract business insights and recommendations"""
        if not self.initialized:
            return self._get_fallback_insights()
        
        context = context or {}
        
        prompt = f"""
        Analyze this business meeting and extract key insights, opportunities, and recommendations.
        
        Meeting Context:
        - Client: {context.get('client_name', 'Unknown')}
        - Industry: {context.get('client_industry', 'Unknown')}
        - Meeting Type: {context.get('meeting_type', 'business')}
        
        Provide insights on:
        1. Business Opportunities
        2. Risk Factors
        3. Client Needs and Pain Points
        4. Competitive Landscape
        5. Strategic Recommendations
        6. Follow-up Actions
        
        Return ONLY a valid JSON object:
        {{
          "opportunities": ["opportunity 1", "opportunity 2"],
          "risks": ["risk 1", "risk 2"],
          "client_needs": ["need 1", "need 2"],
          "recommendations": ["recommendation 1", "recommendation 2"],
          "follow_ups": ["action 1", "action 2"],
          "key_metrics": {{"metric1": "value1", "metric2": "value2"}}
        }}
        
        Text: {text}
        """
        
        try:
            response = await self.generate_content(prompt)
            
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                logger.warning("No valid JSON found in Gemini insights response")
                return self._get_fallback_insights()
                
        except Exception as e:
            logger.error(f"Failed to extract insights with Gemini: {e}")
            return self._get_fallback_insights()
    
    async def extract_action_items(self, text: str, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Extract action items and tasks from meeting"""
        if not self.initialized:
            return []
        
        context = context or {}
        
        prompt = f"""
        Extract action items and tasks from this business meeting transcript.
        
        Meeting Context:
        - Client: {context.get('client_name', 'Unknown')}
        - Participants: {', '.join(context.get('participants', ['Unknown']))}
        
        Return ONLY a valid JSON array:
        [
          {{
            "task": "Description of the task",
            "assignee": "Person responsible",
            "deadline": "timeframe or date mentioned",
            "priority": "high|medium|low",
            "category": "follow_up|research|preparation|decision"
          }}
        ]
        
        Text: {text}
        """
        
        try:
            response = await self.generate_content(prompt)
            
            # Extract JSON from response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                logger.warning("No valid JSON found in Gemini action items response")
                return []
                
        except Exception as e:
            logger.error(f"Failed to extract action items with Gemini: {e}")
            return []
    
    async def test_connection(self) -> bool:
        """Test Gemini AI connection"""
        if not self.initialized:
            return False
        
        try:
            response = await self.generate_content("Hello, this is a test. Please respond with 'Connection successful'.")
            return 'successful' in response.lower()
        except Exception as e:
            logger.error(f"Gemini connection test failed: {e}")
            return False
    
    def _get_fallback_sentiment(self) -> Dict[str, Any]:
        """Fallback sentiment analysis"""
        return {
            'overall': 'neutral',
            'confidence': 0.5,
            'sentiment_score': 0,
            'scores': {
                'positive': 0.33,
                'negative': 0.33,
                'neutral': 0.34
            },
            'reasoning': 'Fallback sentiment analysis',
            'key_indicators': []
        }
    
    def _get_fallback_insights(self) -> Dict[str, Any]:
        """Fallback insights"""
        return {
            'opportunities': ['Follow up on discussed topics'],
            'risks': ['Monitor for any concerns raised'],
            'client_needs': ['Address questions from meeting'],
            'recommendations': ['Schedule follow-up meeting'],
            'follow_ups': ['Send meeting summary'],
            'key_metrics': {}
        }

# Global instance
gemini_service = GeminiService()
