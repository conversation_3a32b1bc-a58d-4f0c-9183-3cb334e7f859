const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Client = sequelize.define('Client', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      len: [1, 200],
    },
  },
  industry: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 100],
    },
  },
  contact_email: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true,
    },
  },
  contact_phone: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 20],
    },
  },
  contact_person: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 100],
    },
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  billing_rate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 150.00,
    validate: {
      min: 0,
    },
  },
  billing_currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'USD',
    validate: {
      len: [3, 3],
    },
  },
  payment_terms: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: 'Net 30',
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'prospect', 'churned'),
    allowNull: false,
    defaultValue: 'active',
  },
  tier: {
    type: DataTypes.ENUM('premium', 'standard', 'basic'),
    allowNull: false,
    defaultValue: 'standard',
  },
  onboarding_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  last_meeting_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  total_meetings: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0,
    },
  },
  total_billed_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    validate: {
      min: 0,
    },
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  preferences: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
  },
  assigned_analyst: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
}, {
  tableName: 'clients',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['name'],
    },
    {
      fields: ['industry'],
    },
    {
      fields: ['status'],
    },
    {
      fields: ['tier'],
    },
    {
      fields: ['assigned_analyst'],
    },
    {
      fields: ['last_meeting_date'],
    },
  ],
});

// Instance methods
Client.prototype.updateMeetingStats = async function() {
  const Meeting = require('./Meeting');
  
  const stats = await Meeting.findAndCountAll({
    where: {
      client_name: this.name,
    },
    attributes: [
      [sequelize.fn('MAX', sequelize.col('meeting_date')), 'last_meeting'],
      [sequelize.fn('SUM', sequelize.col('billing_amount')), 'total_billed'],
    ],
  });
  
  if (stats.count > 0) {
    this.total_meetings = stats.count;
    this.last_meeting_date = stats.rows[0].dataValues.last_meeting;
    this.total_billed_amount = stats.rows[0].dataValues.total_billed || 0;
    await this.save();
  }
  
  return this;
};

Client.prototype.getRecentMeetings = function(limit = 10) {
  const Meeting = require('./Meeting');
  
  return Meeting.findAll({
    where: {
      client_name: this.name,
    },
    order: [['meeting_date', 'DESC']],
    limit: limit,
  });
};

// Class methods
Client.findByIndustry = function(industry) {
  return this.findAll({
    where: {
      industry: {
        [sequelize.Sequelize.Op.iLike]: `%${industry}%`,
      },
      status: 'active',
    },
    order: [['name', 'ASC']],
  });
};

Client.findActiveClients = function() {
  return this.findAll({
    where: {
      status: 'active',
    },
    order: [['name', 'ASC']],
  });
};

Client.findByTier = function(tier) {
  return this.findAll({
    where: {
      tier: tier,
      status: 'active',
    },
    order: [['name', 'ASC']],
  });
};

Client.getTopClientsByRevenue = function(limit = 10) {
  return this.findAll({
    where: {
      status: 'active',
    },
    order: [['total_billed_amount', 'DESC']],
    limit: limit,
  });
};

module.exports = Client;
