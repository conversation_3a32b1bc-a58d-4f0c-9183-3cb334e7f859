{"name": "smartcoverage-backend", "version": "1.0.0", "description": "SmartConverge Backend API - Intelligent Meeting Analysis System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "sequelize-cli db:migrate", "db:seed": "sequelize-cli db:seed:all", "db:reset": "sequelize-cli db:drop && sequelize-cli db:create && npm run db:migrate && npm run db:seed"}, "keywords": ["meeting-analysis", "nlp", "ai", "business-intelligence", "express", "nodejs"], "author": "SmartConverge Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sequelize": "^6.35.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "mongoose": "^8.0.3", "@google/generative-ai": "^0.2.1", "axios": "^1.6.2", "node-cron": "^3.0.3", "winston": "^3.11.0", "compression": "^1.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "csv-parser": "^3.0.0", "natural": "^6.7.0", "compromise": "^14.10.0", "sentiment": "^5.0.2", "@pinecone-database/pinecone": "^1.1.2", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "sequelize-cli": "^6.6.2", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}