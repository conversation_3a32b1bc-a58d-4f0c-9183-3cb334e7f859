# NLP Service Configuration
NLP_API_HOST=localhost
NLP_API_PORT=8000
NLP_API_RELOAD=true
NLP_API_WORKERS=1

# Database Configuration
POSTGRES_URL=postgresql://smartcoverage_user:smartcoverage_password@localhost:5432/smartcoverage
MONGODB_URL=mongodb://localhost:27017/smartcoverage
REDIS_URL=redis://localhost:6379

# Model Configuration
MODELS_DIR=models
DATA_DIR=data
CACHE_DIR=cache

# Training Configuration
TRAINING_BATCH_SIZE=16
TRAINING_LEARNING_RATE=2e-5
TRAINING_NUM_EPOCHS=3
TRAINING_MAX_LENGTH=512

# Processing Configuration
MAX_TEXT_LENGTH=10000
MIN_CONFIDENCE_THRESHOLD=0.7
MAX_TAGS_PER_TEXT=20
CHUNK_SIZE=1000
OVERLAP_SIZE=100

# GPU Configuration
CUDA_VISIBLE_DEVICES=0
TORCH_HOME=./cache/torch

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/nlp_service.log

# External API Keys (Optional)
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
