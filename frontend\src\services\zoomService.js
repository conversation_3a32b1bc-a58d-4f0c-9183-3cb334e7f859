import api from './api';

class ZoomService {
  // Create a new Zoom meeting with NLP integration
  async createMeetingWithNLP(meetingData) {
    try {
      const response = await api.post('/zoom/meetings/create-with-nlp', meetingData);
      return response.data;
    } catch (error) {
      console.error('Failed to create Zoom meeting with NLP:', error);
      throw error;
    }
  }

  // Get meeting details
  async getMeeting(meetingId) {
    try {
      const response = await api.get(`/zoom/meetings/${meetingId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting details:', error);
      throw error;
    }
  }

  // Join a meeting
  async joinMeeting(meetingId) {
    try {
      const response = await api.post(`/zoom/meetings/${meetingId}/join`);
      return response.data;
    } catch (error) {
      console.error('Failed to get join meeting info:', error);
      throw error;
    }
  }

  // Start a meeting (for hosts)
  async startMeeting(meetingId) {
    try {
      const response = await api.post(`/zoom/meetings/${meetingId}/start`);
      return response.data;
    } catch (error) {
      console.error('Failed to start meeting:', error);
      throw error;
    }
  }

  // Update meeting
  async updateMeeting(meetingId, updateData) {
    try {
      const response = await api.patch(`/zoom/meetings/${meetingId}`, updateData);
      return response.data;
    } catch (error) {
      console.error('Failed to update meeting:', error);
      throw error;
    }
  }

  // Delete meeting
  async deleteMeeting(meetingId) {
    try {
      const response = await api.delete(`/zoom/meetings/${meetingId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to delete meeting:', error);
      throw error;
    }
  }

  // List meetings for a user
  async listMeetings(userEmail, type = 'scheduled') {
    try {
      const response = await api.get(`/zoom/meetings/list`, {
        params: { userEmail, type }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to list meetings:', error);
      throw error;
    }
  }

  // Get meeting participants
  async getMeetingParticipants(meetingId) {
    try {
      const response = await api.get(`/zoom/meetings/${meetingId}/participants`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting participants:', error);
      throw error;
    }
  }

  // Get meeting recordings
  async getMeetingRecordings(meetingId) {
    try {
      const response = await api.get(`/zoom/meetings/${meetingId}/recordings`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting recordings:', error);
      throw error;
    }
  }

  // Get real-time meeting insights
  async getRealTimeInsights(meetingId) {
    try {
      const response = await api.get(`/zoom/meetings/${meetingId}/insights`);
      return response.data;
    } catch (error) {
      console.error('Failed to get real-time insights:', error);
      throw error;
    }
  }

  // Subscribe to real-time meeting updates
  subscribeToMeetingUpdates(meetingId, callback) {
    // This would typically use WebSocket connection
    // For now, we'll use polling as a fallback
    const pollInterval = setInterval(async () => {
      try {
        const insights = await this.getRealTimeInsights(meetingId);
        callback(insights);
      } catch (error) {
        console.error('Failed to poll meeting updates:', error);
      }
    }, 10000); // Poll every 10 seconds

    return () => clearInterval(pollInterval);
  }

  // Generate meeting invitation text
  generateInvitation(meetingData) {
    const {
      topic,
      start_time,
      duration,
      join_url,
      password,
      meeting_id,
      host_name
    } = meetingData;

    const startDate = new Date(start_time);
    
    return `
Subject: Invitation to ${topic}

You are invited to a Zoom meeting.

Topic: ${topic}
Time: ${startDate.toLocaleString()}
Duration: ${duration} minutes

Join Zoom Meeting
${join_url}

Meeting ID: ${meeting_id}
Password: ${password}

One tap mobile
+1234567890,,${meeting_id}#,,,,*${password}# US

Dial by your location
****** 567 890 US

Meeting ID: ${meeting_id}
Password: ${password}

Find your local number: https://zoom.us/u/abc123

Hosted by: ${host_name}

---
This meeting includes AI-powered analysis for enhanced insights and automatic transcription.
    `.trim();
  }

  // Download meeting summary
  async downloadMeetingSummary(meetingId, format = 'pdf') {
    try {
      const response = await api.get(`/zoom/meetings/${meetingId}/summary/download`, {
        params: { format },
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `meeting-summary-${meetingId}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return true;
    } catch (error) {
      console.error('Failed to download meeting summary:', error);
      throw error;
    }
  }

  // Get meeting analytics
  async getMeetingAnalytics(meetingId) {
    try {
      const response = await api.get(`/zoom/meetings/${meetingId}/analytics`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting analytics:', error);
      throw error;
    }
  }

  // Send meeting feedback
  async sendMeetingFeedback(meetingId, feedback) {
    try {
      const response = await api.post(`/zoom/meetings/${meetingId}/feedback`, feedback);
      return response.data;
    } catch (error) {
      console.error('Failed to send meeting feedback:', error);
      throw error;
    }
  }

  // Get meeting status
  async getMeetingStatus(meetingId) {
    try {
      const response = await api.get(`/zoom/meetings/${meetingId}/status`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting status:', error);
      throw error;
    }
  }

  // Check if meeting is ready to join
  async isMeetingReady(meetingId) {
    try {
      const status = await this.getMeetingStatus(meetingId);
      return status.status === 'in_progress' || status.can_join;
    } catch (error) {
      console.error('Failed to check meeting readiness:', error);
      return false;
    }
  }

  // Get meeting join button configuration
  getMeetingJoinButtonConfig(meeting) {
    const now = new Date();
    const meetingTime = new Date(meeting.meeting_date);
    const diffMinutes = (meetingTime - now) / (1000 * 60);

    if (diffMinutes < -60) {
      // Meeting ended more than 1 hour ago
      return {
        disabled: true,
        text: 'Meeting Ended',
        color: 'default',
        variant: 'outlined'
      };
    } else if (diffMinutes < 0) {
      // Meeting should be in progress
      return {
        disabled: false,
        text: 'Join Meeting',
        color: 'success',
        variant: 'contained'
      };
    } else if (diffMinutes <= 15) {
      // Meeting starts within 15 minutes
      return {
        disabled: false,
        text: 'Join Now',
        color: 'success',
        variant: 'contained'
      };
    } else if (diffMinutes <= 60) {
      // Meeting starts within 1 hour
      return {
        disabled: false,
        text: 'Join Meeting',
        color: 'primary',
        variant: 'outlined'
      };
    } else {
      // Meeting is more than 1 hour away
      return {
        disabled: true,
        text: `Starts in ${Math.round(diffMinutes / 60)}h`,
        color: 'default',
        variant: 'outlined'
      };
    }
  }

  // Format meeting time for display
  formatMeetingTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((date - now) / (1000 * 60 * 60 * 24));

    let dateDisplay;
    if (diffDays === 0) {
      dateDisplay = 'Today';
    } else if (diffDays === 1) {
      dateDisplay = 'Tomorrow';
    } else if (diffDays === -1) {
      dateDisplay = 'Yesterday';
    } else if (diffDays > 1 && diffDays <= 7) {
      dateDisplay = date.toLocaleDateString('en-US', { weekday: 'long' });
    } else {
      dateDisplay = date.toLocaleDateString();
    }

    const timeDisplay = date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });

    return {
      date: dateDisplay,
      time: timeDisplay,
      full: `${dateDisplay} at ${timeDisplay}`
    };
  }

  // Get meeting platform icon
  getMeetingPlatformIcon(platform) {
    switch (platform) {
      case 'zoom':
        return '📹';
      case 'teams':
        return '💼';
      case 'meet':
        return '🎥';
      case 'in_person':
        return '🏢';
      default:
        return '📞';
    }
  }
}

export const zoomService = new ZoomService();
export default zoomService;
