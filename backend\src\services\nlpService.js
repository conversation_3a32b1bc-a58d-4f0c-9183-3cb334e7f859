const OpenAI = require('openai');
const natural = require('natural');
const compromise = require('compromise');
const Sentiment = require('sentiment');
const logger = require('../utils/logger');

class NLPService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.sentiment = new Sentiment();
    this.stemmer = natural.PorterStemmer;
    this.tokenizer = new natural.WordTokenizer();
    
    // Initialize financial and business keywords
    this.financialKeywords = [
      'investment', 'portfolio', 'roi', 'return', 'profit', 'revenue', 'blockchain',
      'cryptocurrency', 'bitcoin', 'ethereum', 'defi', 'nft', 'stocks', 'bonds',
      'equity', 'debt', 'capital', 'funding', 'valuation', 'ipo', 'merger',
      'acquisition', 'dividend', 'yield', 'risk', 'hedge', 'derivative'
    ];
    
    this.industryKeywords = {
      'technology': ['ai', 'artificial intelligence', 'machine learning', 'software', 'saas', 'cloud', 'data'],
      'healthcare': ['medical', 'pharmaceutical', 'biotech', 'clinical', 'drug', 'therapy'],
      'finance': ['banking', 'fintech', 'insurance', 'lending', 'payment', 'trading'],
      'energy': ['renewable', 'solar', 'wind', 'oil', 'gas', 'nuclear', 'battery'],
      'real estate': ['property', 'commercial', 'residential', 'reit', 'development'],
      'retail': ['ecommerce', 'consumer', 'brand', 'marketplace', 'supply chain']
    };
  }

  // Extract explicit tags using rule-based approach
  async extractExplicitTags(text) {
    const tags = [];
    const lowerText = text.toLowerCase();
    
    // Financial keywords
    this.financialKeywords.forEach(keyword => {
      if (lowerText.includes(keyword)) {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const matches = text.match(regex);
        if (matches) {
          tags.push({
            name: keyword,
            type: 'explicit',
            confidence: 1.0,
            source: 'rule_based',
            frequency: matches.length,
            category: 'financial'
          });
        }
      }
    });
    
    // Industry keywords
    Object.entries(this.industryKeywords).forEach(([industry, keywords]) => {
      keywords.forEach(keyword => {
        if (lowerText.includes(keyword)) {
          const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
          const matches = text.match(regex);
          if (matches) {
            tags.push({
              name: keyword,
              type: 'explicit',
              confidence: 1.0,
              source: 'rule_based',
              frequency: matches.length,
              category: industry
            });
          }
        }
      });
    });
    
    // Extract named entities using compromise
    const doc = compromise(text);
    
    // Organizations
    const orgs = doc.organizations().out('array');
    orgs.forEach(org => {
      tags.push({
        name: org.toLowerCase(),
        type: 'explicit',
        confidence: 0.9,
        source: 'nlp_model',
        frequency: 1,
        category: 'organization'
      });
    });
    
    // People
    const people = doc.people().out('array');
    people.forEach(person => {
      tags.push({
        name: person.toLowerCase(),
        type: 'explicit',
        confidence: 0.9,
        source: 'nlp_model',
        frequency: 1,
        category: 'person'
      });
    });
    
    // Money amounts
    const money = doc.money().out('array');
    money.forEach(amount => {
      tags.push({
        name: amount.toLowerCase(),
        type: 'explicit',
        confidence: 0.95,
        source: 'nlp_model',
        frequency: 1,
        category: 'financial'
      });
    });
    
    return this.deduplicateTags(tags);
  }

  // Extract implicit tags using OpenAI
  async extractImplicitTags(text) {
    try {
      const prompt = `
        Analyze the following business meeting text and extract implicit topics, themes, and concepts that are discussed but not explicitly mentioned. 
        Focus on investment themes, business strategies, market trends, and industry insights.
        
        Text: "${text}"
        
        Return a JSON array of objects with the following structure:
        [
          {
            "name": "tag_name",
            "confidence": 0.8,
            "category": "category_name",
            "reasoning": "why this tag was inferred"
          }
        ]
        
        Limit to maximum 10 most relevant implicit tags.
      `;

      const response = await this.openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert financial analyst who can identify implicit themes and topics in business discussions.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000,
      });

      const content = response.choices[0].message.content;
      const implicitTags = JSON.parse(content);
      
      return implicitTags.map(tag => ({
        name: tag.name.toLowerCase(),
        type: 'implicit',
        confidence: tag.confidence,
        source: 'nlp_model',
        frequency: 1,
        category: tag.category,
        context: tag.reasoning
      }));
      
    } catch (error) {
      logger.error('Error extracting implicit tags:', error);
      return [];
    }
  }

  // Generate meeting summary
  async generateSummary(text, meetingData = {}) {
    try {
      const prompt = `
        Generate a comprehensive summary of this business meeting. Include:
        1. Key discussion points
        2. Decisions made
        3. Action items
        4. Investment amounts and ROI expectations
        5. Client sentiment and satisfaction
        6. Next steps
        
        Meeting Details:
        - Subject: ${meetingData.subject || 'N/A'}
        - Client: ${meetingData.client_name || 'N/A'}
        - Organizer: ${meetingData.organizer_name || 'N/A'}
        
        Meeting Content: "${text}"
        
        Provide a structured summary in 3-4 paragraphs.
      `;

      const response = await this.openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a professional business analyst who creates concise, informative meeting summaries for executive review.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 800,
      });

      return response.choices[0].message.content;
      
    } catch (error) {
      logger.error('Error generating summary:', error);
      return 'Summary generation failed. Please try again.';
    }
  }

  // Analyze sentiment
  analyzeSentiment(text) {
    const result = this.sentiment.analyze(text);
    
    // Normalize score to -1 to 1 range
    const normalizedScore = Math.max(-1, Math.min(1, result.score / 10));
    
    let sentiment = 'neutral';
    if (normalizedScore > 0.1) sentiment = 'positive';
    else if (normalizedScore < -0.1) sentiment = 'negative';
    
    return {
      score: normalizedScore,
      sentiment: sentiment,
      positive: result.positive,
      negative: result.negative,
      tokens: result.tokens
    };
  }

  // Extract key phrases using TF-IDF
  extractKeyPhrases(text, maxPhrases = 10) {
    const tokens = this.tokenizer.tokenize(text.toLowerCase());
    const stopWords = natural.stopwords;
    
    // Remove stop words and short words
    const filteredTokens = tokens.filter(token => 
      !stopWords.includes(token) && 
      token.length > 2 && 
      /^[a-zA-Z]+$/.test(token)
    );
    
    // Calculate term frequency
    const termFreq = {};
    filteredTokens.forEach(token => {
      termFreq[token] = (termFreq[token] || 0) + 1;
    });
    
    // Sort by frequency and return top phrases
    const sortedTerms = Object.entries(termFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, maxPhrases)
      .map(([term, freq]) => ({
        phrase: term,
        frequency: freq,
        relevance_score: freq / filteredTokens.length
      }));
    
    return sortedTerms;
  }

  // Deduplicate and merge similar tags
  deduplicateTags(tags) {
    const tagMap = new Map();
    
    tags.forEach(tag => {
      const key = tag.name.toLowerCase().trim();
      if (tagMap.has(key)) {
        const existing = tagMap.get(key);
        existing.frequency += tag.frequency;
        existing.confidence = Math.max(existing.confidence, tag.confidence);
      } else {
        tagMap.set(key, { ...tag });
      }
    });
    
    return Array.from(tagMap.values())
      .filter(tag => tag.confidence >= (process.env.CONFIDENCE_THRESHOLD || 0.7))
      .sort((a, b) => b.confidence - a.confidence);
  }

  // Process complete meeting text
  async processText(text, meetingData = {}) {
    try {
      const startTime = Date.now();
      
      // Run all analyses in parallel
      const [explicitTags, implicitTags, summary, sentiment, keyPhrases] = await Promise.all([
        this.extractExplicitTags(text),
        this.extractImplicitTags(text),
        this.generateSummary(text, meetingData),
        Promise.resolve(this.analyzeSentiment(text)),
        Promise.resolve(this.extractKeyPhrases(text))
      ]);
      
      const processingTime = Date.now() - startTime;
      
      return {
        explicit_tags: explicitTags,
        implicit_tags: implicitTags,
        all_tags: this.deduplicateTags([...explicitTags, ...implicitTags]),
        summary: summary,
        sentiment: sentiment,
        key_phrases: keyPhrases,
        processing_time_ms: processingTime,
        word_count: text.split(' ').length
      };
      
    } catch (error) {
      logger.error('Error processing text:', error);
      throw error;
    }
  }
}

module.exports = new NLPService();
