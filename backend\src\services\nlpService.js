const axios = require('axios');
const WebSocket = require('ws');
const EventEmitter = require('events');
const logger = require('../utils/logger');

class NLPService extends EventEmitter {
  constructor() {
    super();
    this.nlpServiceUrl = process.env.NLP_SERVICE_URL || 'http://localhost:8000';
    this.wsUrl = process.env.NLP_WS_URL || 'ws://localhost:8000/ws';
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;

    // Initialize WebSocket connection
    this.initializeWebSocket();
  }

  initializeWebSocket() {
    try {
      this.ws = new WebSocket(this.wsUrl);

      this.ws.on('open', () => {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        logger.info('✅ Connected to NLP Service WebSocket');
        this.emit('connected');
      });

      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleWebSocketMessage(message);
        } catch (error) {
          logger.error('Error parsing WebSocket message:', error);
        }
      });

      this.ws.on('close', () => {
        this.isConnected = false;
        logger.warn('❌ NLP Service WebSocket connection closed');
        this.emit('disconnected');
        this.attemptReconnect();
      });

      this.ws.on('error', (error) => {
        logger.error('NLP Service WebSocket error:', error);
        this.emit('error', error);
      });

    } catch (error) {
      logger.error('Failed to initialize WebSocket:', error);
      this.attemptReconnect();
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      logger.info(`Attempting to reconnect to NLP Service (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

      setTimeout(() => {
        this.initializeWebSocket();
      }, this.reconnectDelay);
    } else {
      logger.error('Max reconnection attempts reached. NLP Service unavailable.');
    }
  }

  handleWebSocketMessage(message) {
    switch (message.type) {
      case 'processing_result':
        this.emit('processingResult', message.task_id, message.result);
        break;
      case 'task_started':
        this.emit('taskStarted', message.task_id);
        break;
      default:
        logger.debug('Unknown WebSocket message type:', message.type);
    }
  }

  // Real-time text processing using deep learning NLP service
  async processTextRealTime(text, options = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/process/sync`, {
        text: text,
        options: options
      });

      return response.data.result;
    } catch (error) {
      logger.error('Real-time NLP processing failed:', error);
      // Fallback to basic processing
      return this.processTextFallback(text);
    }
  }

  // Asynchronous processing for large texts
  async processTextAsync(text, options = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/process/async`, {
        text: text,
        options: options
      });

      return {
        taskId: response.data.task_id,
        status: response.data.status
      };
    } catch (error) {
      logger.error('Async NLP processing failed:', error);
      throw error;
    }
  }

  // Get result from async processing
  async getProcessingResult(taskId) {
    try {
      const response = await axios.get(`${this.nlpServiceUrl}/process/result/${taskId}`);
      return response.data.result;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // Still processing
      }
      logger.error('Failed to get processing result:', error);
      throw error;
    }
  }

  // Semantic search functionality
  async searchDocuments(query, options = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/search`, {
        query: query,
        top_k: options.topK || 10,
        filters: options.filters
      });

      return response.data.results;
    } catch (error) {
      logger.error('Semantic search failed:', error);
      throw error;
    }
  }

  // Advanced semantic query
  async semanticQuery(query, context = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/search/semantic`, {
        query: query,
        filters: context
      });

      return response.data;
    } catch (error) {
      logger.error('Semantic query failed:', error);
      throw error;
    }
  }

  // Add documents to search index
  async addDocuments(documents) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/documents/batch`, documents);
      return response.data.document_ids;
    } catch (error) {
      logger.error('Failed to add documents to search index:', error);
      throw error;
    }
  }

  // Specialized model endpoints
  async analyzeSentiment(text) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/models/sentiment/analyze`, {
        text: text
      });

      return response.data.sentiment;
    } catch (error) {
      logger.error('Sentiment analysis failed:', error);
      return this.fallbackSentimentAnalysis(text);
    }
  }

  async generateSummary(text, context = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/models/summarize`, {
        text: text,
        options: context
      });

      return response.data.summary;
    } catch (error) {
      logger.error('Summarization failed:', error);
      return this.fallbackSummarization(text);
    }
  }

  async extractTags(text) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/models/extract-tags`, {
        text: text
      });

      return response.data.tags;
    } catch (error) {
      logger.error('Tag extraction failed:', error);
      return this.fallbackTagExtraction(text);
    }
  }

  // Extract implicit tags using OpenAI
  async extractImplicitTags(text) {
    try {
      const prompt = `
        Analyze the following business meeting text and extract implicit topics, themes, and concepts that are discussed but not explicitly mentioned.
        Focus on investment themes, business strategies, market trends, and industry insights.

        Text: "${text}"

        Return a JSON array of objects with the following structure:
        [
          {
            "name": "tag_name",
            "confidence": 0.8,
            "category": "category_name",
            "reasoning": "why this tag was inferred"
          }
        ]

        Limit to maximum 10 most relevant implicit tags.
      `;

      const response = await this.openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert financial analyst who can identify implicit themes and topics in business discussions.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000,
      });

      const content = response.choices[0].message.content;
      const implicitTags = JSON.parse(content);

      return implicitTags.map(tag => ({
        name: tag.name.toLowerCase(),
        type: 'implicit',
        confidence: tag.confidence,
        source: 'nlp_model',
        frequency: 1,
        category: tag.category,
        context: tag.reasoning
      }));

    } catch (error) {
      logger.error('Error extracting implicit tags:', error);
      return [];
    }
  }

  // Generate meeting summary
  async generateSummary(text, meetingData = {}) {
    try {
      const prompt = `
        Generate a comprehensive summary of this business meeting. Include:
        1. Key discussion points
        2. Decisions made
        3. Action items
        4. Investment amounts and ROI expectations
        5. Client sentiment and satisfaction
        6. Next steps

        Meeting Details:
        - Subject: ${meetingData.subject || 'N/A'}
        - Client: ${meetingData.client_name || 'N/A'}
        - Organizer: ${meetingData.organizer_name || 'N/A'}

        Meeting Content: "${text}"

        Provide a structured summary in 3-4 paragraphs.
      `;

      const response = await this.openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a professional business analyst who creates concise, informative meeting summaries for executive review.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 800,
      });

      return response.choices[0].message.content;

    } catch (error) {
      logger.error('Error generating summary:', error);
      return 'Summary generation failed. Please try again.';
    }
  }

  // Analyze sentiment
  analyzeSentiment(text) {
    const result = this.sentiment.analyze(text);

    // Normalize score to -1 to 1 range
    const normalizedScore = Math.max(-1, Math.min(1, result.score / 10));

    let sentiment = 'neutral';
    if (normalizedScore > 0.1) sentiment = 'positive';
    else if (normalizedScore < -0.1) sentiment = 'negative';

    return {
      score: normalizedScore,
      sentiment: sentiment,
      positive: result.positive,
      negative: result.negative,
      tokens: result.tokens
    };
  }

  // Extract key phrases using TF-IDF
  extractKeyPhrases(text, maxPhrases = 10) {
    const tokens = this.tokenizer.tokenize(text.toLowerCase());
    const stopWords = natural.stopwords;

    // Remove stop words and short words
    const filteredTokens = tokens.filter(token =>
      !stopWords.includes(token) &&
      token.length > 2 &&
      /^[a-zA-Z]+$/.test(token)
    );

    // Calculate term frequency
    const termFreq = {};
    filteredTokens.forEach(token => {
      termFreq[token] = (termFreq[token] || 0) + 1;
    });

    // Sort by frequency and return top phrases
    const sortedTerms = Object.entries(termFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, maxPhrases)
      .map(([term, freq]) => ({
        phrase: term,
        frequency: freq,
        relevance_score: freq / filteredTokens.length
      }));

    return sortedTerms;
  }

  // Deduplicate and merge similar tags
  deduplicateTags(tags) {
    const tagMap = new Map();

    tags.forEach(tag => {
      const key = tag.name.toLowerCase().trim();
      if (tagMap.has(key)) {
        const existing = tagMap.get(key);
        existing.frequency += tag.frequency;
        existing.confidence = Math.max(existing.confidence, tag.confidence);
      } else {
        tagMap.set(key, { ...tag });
      }
    });

    return Array.from(tagMap.values())
      .filter(tag => tag.confidence >= (process.env.CONFIDENCE_THRESHOLD || 0.7))
      .sort((a, b) => b.confidence - a.confidence);
  }

  // Main processing method that integrates with deep learning service
  async processText(text, meetingData = {}) {
    try {
      const startTime = Date.now();

      // Use the advanced NLP service for processing
      const result = await this.processTextRealTime(text, {
        meeting_context: meetingData,
        include_embeddings: true,
        detailed_analysis: true
      });

      // If real-time processing succeeded, return enhanced result
      if (result && !result.error) {
        return {
          explicit_tags: result.tags?.filter(tag => tag.type === 'explicit') || [],
          implicit_tags: result.tags?.filter(tag => tag.type === 'implicit') || [],
          all_tags: result.tags || [],
          summary: result.summary || '',
          sentiment: result.sentiment || { overall: 'neutral', confidence: 0.5 },
          entities: result.entities || [],
          classification: result.classification || {},
          processing_time_ms: Date.now() - startTime,
          word_count: text.split(' ').length,
          confidence_score: result.confidence_score || 0.5
        };
      }

      // Fallback to basic processing if advanced service fails
      return await this.processTextFallback(text, meetingData);

    } catch (error) {
      logger.error('Error in processText:', error);
      return await this.processTextFallback(text, meetingData);
    }
  }

  // Fallback processing methods for when the advanced service is unavailable
  async processTextFallback(text, meetingData = {}) {
    try {
      const startTime = Date.now();

      // Basic sentiment analysis
      const sentiment = this.fallbackSentimentAnalysis(text);

      // Basic tag extraction
      const tags = this.fallbackTagExtraction(text);

      // Basic summarization
      const summary = this.fallbackSummarization(text);

      return {
        explicit_tags: tags.filter(tag => tag.type === 'explicit'),
        implicit_tags: tags.filter(tag => tag.type === 'implicit'),
        all_tags: tags,
        summary: summary,
        sentiment: sentiment,
        entities: [],
        processing_time_ms: Date.now() - startTime,
        word_count: text.split(' ').length,
        confidence_score: 0.6
      };

    } catch (error) {
      logger.error('Error in fallback processing:', error);
      throw error;
    }
  }

  // Fallback methods for basic processing
  fallbackSentimentAnalysis(text) {
    // Simple sentiment analysis using basic rules
    const positiveWords = ['good', 'great', 'excellent', 'positive', 'successful', 'profitable', 'growth'];
    const negativeWords = ['bad', 'poor', 'negative', 'loss', 'decline', 'risk', 'concern'];

    const words = text.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    });

    const score = (positiveCount - negativeCount) / words.length;
    let overall = 'neutral';
    if (score > 0.01) overall = 'positive';
    else if (score < -0.01) overall = 'negative';

    return {
      overall,
      confidence: Math.min(0.8, Math.abs(score) * 10),
      scores: {
        positive: positiveCount / words.length,
        negative: negativeCount / words.length,
        neutral: 1 - (positiveCount + negativeCount) / words.length
      },
      sentiment_score: score
    };
  }

  fallbackTagExtraction(text) {
    const tags = [];
    const lowerText = text.toLowerCase();

    // Financial keywords
    const financialKeywords = [
      'investment', 'portfolio', 'roi', 'return', 'profit', 'revenue', 'blockchain',
      'cryptocurrency', 'bitcoin', 'ethereum', 'defi', 'funding', 'valuation'
    ];

    financialKeywords.forEach(keyword => {
      if (lowerText.includes(keyword)) {
        tags.push({
          name: keyword,
          type: 'explicit',
          confidence: 0.9,
          source: 'keyword_matching',
          category: 'financial',
          frequency: (lowerText.match(new RegExp(keyword, 'g')) || []).length
        });
      }
    });

    return tags;
  }

  fallbackSummarization(text) {
    // Simple extractive summarization - take first and last sentences
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);

    if (sentences.length <= 2) {
      return text;
    }

    const summary = [sentences[0], sentences[sentences.length - 1]].join('. ') + '.';
    return summary.length > 200 ? summary.substring(0, 200) + '...' : summary;
  }

  // Legacy methods for backward compatibility
  async extractExplicitTags(text) {
    const result = await this.extractTags(text);
    return result.explicit_tags || [];
  }

  async extractImplicitTags(text) {
    const result = await this.extractTags(text);
    return result.implicit_tags || [];
  }
}

module.exports = new NLPService();
