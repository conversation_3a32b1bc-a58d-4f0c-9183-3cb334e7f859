const axios = require('axios');
const WebSocket = require('ws');
const EventEmitter = require('events');
const geminiService = require('./geminiService');
const logger = require('../utils/logger');

class NLPService extends EventEmitter {
  constructor() {
    super();
    this.nlpServiceUrl = process.env.NLP_SERVICE_URL || 'http://localhost:8000';
    this.wsUrl = process.env.NLP_WS_URL || 'ws://localhost:8000/ws';
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 3; // Reduced attempts
    this.reconnectDelay = 5000;
    this.enableWebSocket = process.env.ENABLE_NLP_WEBSOCKET !== 'false';

    // Initialize WebSocket connection only if enabled
    if (this.enableWebSocket) {
      this.initializeWebSocket();
    } else {
      logger.info('NLP WebSocket connection disabled');
    }
  }

  initializeWebSocket() {
    try {
      this.ws = new WebSocket(this.wsUrl);

      this.ws.on('open', () => {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        logger.info('✅ Connected to NLP Service WebSocket');
        this.emit('connected');
      });

      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleWebSocketMessage(message);
        } catch (error) {
          logger.error('Error parsing WebSocket message:', error);
        }
      });

      this.ws.on('close', () => {
        this.isConnected = false;
        logger.warn('❌ NLP Service WebSocket connection closed');
        this.emit('disconnected');
        this.attemptReconnect();
      });

      this.ws.on('error', (error) => {
        logger.warn('NLP Service WebSocket error (service may not be running):', error.code || error.message);
        this.isConnected = false;
        // Don't emit error to prevent uncaught exceptions
        // this.emit('error', error);
      });

    } catch (error) {
      logger.error('Failed to initialize WebSocket:', error);
      this.attemptReconnect();
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      logger.info(`Attempting to reconnect to NLP Service (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

      setTimeout(() => {
        this.initializeWebSocket();
      }, this.reconnectDelay);
    } else {
      logger.error('Max reconnection attempts reached. NLP Service unavailable.');
    }
  }

  handleWebSocketMessage(message) {
    switch (message.type) {
      case 'processing_result':
        this.emit('processingResult', message.task_id, message.result);
        break;
      case 'task_started':
        this.emit('taskStarted', message.task_id);
        break;
      default:
        logger.debug('Unknown WebSocket message type:', message.type);
    }
  }

  // Real-time text processing using deep learning NLP service
  async processTextRealTime(text, options = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/process/sync`, {
        text: text,
        options: options
      });

      return response.data.result;
    } catch (error) {
      logger.error('Real-time NLP processing failed:', error);
      // Fallback to basic processing
      return this.processTextFallback(text);
    }
  }

  // Asynchronous processing for large texts
  async processTextAsync(text, options = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/process/async`, {
        text: text,
        options: options
      });

      return {
        taskId: response.data.task_id,
        status: response.data.status
      };
    } catch (error) {
      logger.error('Async NLP processing failed:', error);
      throw error;
    }
  }

  // Get result from async processing
  async getProcessingResult(taskId) {
    try {
      const response = await axios.get(`${this.nlpServiceUrl}/process/result/${taskId}`);
      return response.data.result;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // Still processing
      }
      logger.error('Failed to get processing result:', error);
      throw error;
    }
  }

  // Semantic search functionality
  async searchDocuments(query, options = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/search`, {
        query: query,
        top_k: options.topK || 10,
        filters: options.filters
      });

      return response.data.results;
    } catch (error) {
      logger.error('Semantic search failed:', error);
      throw error;
    }
  }

  // Advanced semantic query
  async semanticQuery(query, context = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/search/semantic`, {
        query: query,
        filters: context
      });

      return response.data;
    } catch (error) {
      logger.error('Semantic query failed:', error);
      throw error;
    }
  }

  // Add documents to search index
  async addDocuments(documents) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/documents/batch`, documents);
      return response.data.document_ids;
    } catch (error) {
      logger.error('Failed to add documents to search index:', error);
      throw error;
    }
  }

  // Specialized model endpoints
  async analyzeSentiment(text) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/models/sentiment/analyze`, {
        text: text
      });

      return response.data.sentiment;
    } catch (error) {
      logger.error('Sentiment analysis failed:', error);
      return this.fallbackSentimentAnalysis(text);
    }
  }

  async generateSummary(text, context = {}) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/models/summarize`, {
        text: text,
        options: context
      });

      return response.data.summary;
    } catch (error) {
      logger.error('Summarization failed:', error);
      return this.fallbackSummarization(text);
    }
  }

  async extractTags(text) {
    try {
      const response = await axios.post(`${this.nlpServiceUrl}/models/extract-tags`, {
        text: text
      });

      return response.data.tags;
    } catch (error) {
      logger.error('Tag extraction failed:', error);
      return this.fallbackTagExtraction(text);
    }
  }

  // Extract implicit tags using Gemini
  async extractImplicitTags(text, meetingContext = {}) {
    try {
      return await geminiService.extractImplicitTags(text, meetingContext);
    } catch (error) {
      logger.error('Error extracting implicit tags with Gemini:', error);
      return [];
    }
  }

  // Generate meeting summary using Gemini
  async generateMeetingSummary(text, meetingData = {}) {
    try {
      return await geminiService.generateSummary(text, meetingData);
    } catch (error) {
      logger.error('Error generating summary with Gemini:', error);
      return 'Summary generation failed. Please try again.';
    }
  }

  // Analyze sentiment using Gemini
  async analyzeSentimentWithGemini(text, meetingContext = {}) {
    try {
      return await geminiService.analyzeSentiment(text, meetingContext);
    } catch (error) {
      logger.error('Error analyzing sentiment with Gemini:', error);
      return this.fallbackSentimentAnalysis(text);
    }
  }

  // Extract insights using Gemini
  async extractInsights(text, meetingContext = {}) {
    try {
      return await geminiService.extractInsights(text, meetingContext);
    } catch (error) {
      logger.error('Error extracting insights with Gemini:', error);
      return geminiService.getFallbackInsights();
    }
  }

  // Extract action items using Gemini
  async extractActionItems(text, meetingContext = {}) {
    try {
      return await geminiService.extractActionItems(text, meetingContext);
    } catch (error) {
      logger.error('Error extracting action items with Gemini:', error);
      return [];
    }
  }

  // Analyze client relationship using Gemini
  async analyzeClientRelationship(text, clientHistory = {}) {
    try {
      return await geminiService.analyzeClientRelationship(text, clientHistory);
    } catch (error) {
      logger.error('Error analyzing client relationship with Gemini:', error);
      return geminiService.getFallbackRelationshipAnalysis();
    }
  }

  // Extract key phrases using TF-IDF
  extractKeyPhrases(text, maxPhrases = 10) {
    const tokens = this.tokenizer.tokenize(text.toLowerCase());
    const stopWords = natural.stopwords;

    // Remove stop words and short words
    const filteredTokens = tokens.filter(token =>
      !stopWords.includes(token) &&
      token.length > 2 &&
      /^[a-zA-Z]+$/.test(token)
    );

    // Calculate term frequency
    const termFreq = {};
    filteredTokens.forEach(token => {
      termFreq[token] = (termFreq[token] || 0) + 1;
    });

    // Sort by frequency and return top phrases
    const sortedTerms = Object.entries(termFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, maxPhrases)
      .map(([term, freq]) => ({
        phrase: term,
        frequency: freq,
        relevance_score: freq / filteredTokens.length
      }));

    return sortedTerms;
  }

  // Deduplicate and merge similar tags
  deduplicateTags(tags) {
    const tagMap = new Map();

    tags.forEach(tag => {
      const key = tag.name.toLowerCase().trim();
      if (tagMap.has(key)) {
        const existing = tagMap.get(key);
        existing.frequency += tag.frequency;
        existing.confidence = Math.max(existing.confidence, tag.confidence);
      } else {
        tagMap.set(key, { ...tag });
      }
    });

    return Array.from(tagMap.values())
      .filter(tag => tag.confidence >= (process.env.CONFIDENCE_THRESHOLD || 0.7))
      .sort((a, b) => b.confidence - a.confidence);
  }

  // Main processing method - now uses Gemini AI by default
  async processText(text, meetingData = {}) {
    try {
      // First try Gemini AI processing
      const geminiResult = await this.processTextWithGemini(text, meetingData);
      if (geminiResult && geminiResult.ai_provider === 'gemini') {
        logger.info('✅ Text processed successfully with Gemini AI');
        return geminiResult;
      }

      // If Gemini fails, try the advanced NLP service
      const result = await this.processTextRealTime(text, {
        meeting_context: meetingData,
        include_embeddings: true,
        detailed_analysis: true
      });

      // If real-time processing succeeded, return enhanced result
      if (result && !result.error) {
        logger.info('✅ Text processed with advanced NLP service');
        return {
          explicit_tags: result.tags?.filter(tag => tag.type === 'explicit') || [],
          implicit_tags: result.tags?.filter(tag => tag.type === 'implicit') || [],
          all_tags: result.tags || [],
          summary: result.summary || '',
          sentiment: result.sentiment || { overall: 'neutral', confidence: 0.5 },
          entities: result.entities || [],
          classification: result.classification || {},
          insights: result.insights || {},
          action_items: result.action_items || [],
          processing_time_ms: Date.now() - Date.now(),
          word_count: text.split(' ').length,
          confidence_score: result.confidence_score || 0.5,
          ai_provider: 'nlp_service'
        };
      }

      // Final fallback to basic processing
      logger.warn('⚠️ Falling back to basic text processing');
      return await this.processTextFallback(text, meetingData);

    } catch (error) {
      logger.error('Error in processText:', error);
      return await this.processTextFallback(text, meetingData);
    }
  }

  // Enhanced processing with Gemini AI
  async processTextWithGemini(text, meetingData = {}) {
    try {
      const startTime = Date.now();

      // Run Gemini-powered analysis in parallel
      const [
        implicitTags,
        summary,
        sentiment,
        insights,
        actionItems
      ] = await Promise.all([
        this.extractImplicitTags(text, meetingData),
        this.generateMeetingSummary(text, meetingData),
        this.analyzeSentimentWithGemini(text, meetingData),
        this.extractInsights(text, meetingData),
        this.extractActionItems(text, meetingData)
      ]);

      // Basic explicit tag extraction (fallback method)
      const explicitTags = this.fallbackTagExtraction(text);

      return {
        explicit_tags: explicitTags.filter(tag => tag.type === 'explicit'),
        implicit_tags: implicitTags,
        all_tags: [...explicitTags, ...implicitTags],
        summary: summary,
        sentiment: sentiment,
        insights: insights,
        action_items: actionItems,
        entities: [], // Could be enhanced with Gemini entity extraction
        processing_time_ms: Date.now() - startTime,
        word_count: text.split(' ').length,
        confidence_score: 0.85, // Higher confidence with Gemini
        ai_provider: 'gemini'
      };

    } catch (error) {
      logger.error('Error in Gemini processing:', error);
      // Fallback to basic processing
      return this.processTextFallback(text, meetingData);
    }
  }

  // Fallback processing methods for when Gemini is unavailable
  async processTextFallback(text, meetingData = {}) {
    try {
      const startTime = Date.now();

      // Basic sentiment analysis
      const sentiment = this.fallbackSentimentAnalysis(text);

      // Basic tag extraction
      const tags = this.fallbackTagExtraction(text);

      // Basic summarization
      const summary = this.fallbackSummarization(text);

      return {
        explicit_tags: tags.filter(tag => tag.type === 'explicit'),
        implicit_tags: tags.filter(tag => tag.type === 'implicit'),
        all_tags: tags,
        summary: summary,
        sentiment: sentiment,
        insights: geminiService.getFallbackInsights(),
        action_items: [],
        entities: [],
        processing_time_ms: Date.now() - startTime,
        word_count: text.split(' ').length,
        confidence_score: 0.6,
        ai_provider: 'fallback'
      };

    } catch (error) {
      logger.error('Error in fallback processing:', error);
      throw error;
    }
  }

  // Fallback methods for basic processing
  fallbackSentimentAnalysis(text) {
    // Simple sentiment analysis using basic rules
    const positiveWords = ['good', 'great', 'excellent', 'positive', 'successful', 'profitable', 'growth'];
    const negativeWords = ['bad', 'poor', 'negative', 'loss', 'decline', 'risk', 'concern'];

    const words = text.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    });

    const score = (positiveCount - negativeCount) / words.length;
    let overall = 'neutral';
    if (score > 0.01) overall = 'positive';
    else if (score < -0.01) overall = 'negative';

    return {
      overall,
      confidence: Math.min(0.8, Math.abs(score) * 10),
      scores: {
        positive: positiveCount / words.length,
        negative: negativeCount / words.length,
        neutral: 1 - (positiveCount + negativeCount) / words.length
      },
      sentiment_score: score
    };
  }

  fallbackTagExtraction(text) {
    const tags = [];
    const lowerText = text.toLowerCase();

    // Financial keywords
    const financialKeywords = [
      'investment', 'portfolio', 'roi', 'return', 'profit', 'revenue', 'blockchain',
      'cryptocurrency', 'bitcoin', 'ethereum', 'defi', 'funding', 'valuation'
    ];

    financialKeywords.forEach(keyword => {
      if (lowerText.includes(keyword)) {
        tags.push({
          name: keyword,
          type: 'explicit',
          confidence: 0.9,
          source: 'keyword_matching',
          category: 'financial',
          frequency: (lowerText.match(new RegExp(keyword, 'g')) || []).length
        });
      }
    });

    return tags;
  }

  fallbackSummarization(text) {
    // Simple extractive summarization - take first and last sentences
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);

    if (sentences.length <= 2) {
      return text;
    }

    const summary = [sentences[0], sentences[sentences.length - 1]].join('. ') + '.';
    return summary.length > 200 ? summary.substring(0, 200) + '...' : summary;
  }

  // Legacy methods for backward compatibility
  async extractExplicitTags(text) {
    const result = await this.extractTags(text);
    return result.explicit_tags || [];
  }

  async extractImplicitTags(text) {
    const result = await this.extractTags(text);
    return result.implicit_tags || [];
  }
}

module.exports = new NLPService();
