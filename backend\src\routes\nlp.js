const express = require('express');
const { body, validationResult } = require('express-validator');
const nlpService = require('../services/nlpService');
const { Meeting, Tag } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @swagger
 * /nlp/analyze:
 *   post:
 *     summary: Analyze text with NLP processing
 *     tags: [NLP]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *             properties:
 *               text:
 *                 type: string
 *                 description: Text to analyze
 *               context:
 *                 type: object
 *                 description: Additional context for analysis
 *     responses:
 *       200:
 *         description: Analysis results
 *       400:
 *         description: Validation error
 */
router.post('/analyze', [
  body('text').notEmpty().trim(),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }

  const { text, context } = req.body;

  try {
    const results = await nlpService.processText(text, context);
    
    res.json({
      success: true,
      data: results,
    });
  } catch (error) {
    logger.error('NLP analysis failed:', error);
    return next(new AppError('NLP analysis failed', 500));
  }
}));

/**
 * @swagger
 * /nlp/extract-tags:
 *   post:
 *     summary: Extract tags from text
 *     tags: [NLP]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *             properties:
 *               text:
 *                 type: string
 *               include_implicit:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Extracted tags
 */
router.post('/extract-tags', [
  body('text').notEmpty().trim(),
  body('include_implicit').optional().isBoolean(),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }

  const { text, include_implicit = true } = req.body;

  try {
    const explicitTags = await nlpService.extractExplicitTags(text);
    let implicitTags = [];
    
    if (include_implicit) {
      implicitTags = await nlpService.extractImplicitTags(text);
    }

    const allTags = nlpService.deduplicateTags([...explicitTags, ...implicitTags]);

    res.json({
      success: true,
      data: {
        explicit_tags: explicitTags,
        implicit_tags: implicitTags,
        all_tags: allTags,
        total_count: allTags.length,
      },
    });
  } catch (error) {
    logger.error('Tag extraction failed:', error);
    return next(new AppError('Tag extraction failed', 500));
  }
}));

/**
 * @swagger
 * /nlp/summarize:
 *   post:
 *     summary: Generate summary of text
 *     tags: [NLP]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *             properties:
 *               text:
 *                 type: string
 *               context:
 *                 type: object
 *     responses:
 *       200:
 *         description: Generated summary
 */
router.post('/summarize', [
  body('text').notEmpty().trim(),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }

  const { text, context } = req.body;

  try {
    const summary = await nlpService.generateSummary(text, context);
    
    res.json({
      success: true,
      data: {
        summary,
        original_length: text.length,
        summary_length: summary.length,
        compression_ratio: (summary.length / text.length * 100).toFixed(2) + '%',
      },
    });
  } catch (error) {
    logger.error('Summarization failed:', error);
    return next(new AppError('Summarization failed', 500));
  }
}));

/**
 * @swagger
 * /nlp/sentiment:
 *   post:
 *     summary: Analyze sentiment of text
 *     tags: [NLP]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *             properties:
 *               text:
 *                 type: string
 *     responses:
 *       200:
 *         description: Sentiment analysis results
 */
router.post('/sentiment', [
  body('text').notEmpty().trim(),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }

  const { text } = req.body;

  try {
    const sentiment = nlpService.analyzeSentiment(text);
    
    res.json({
      success: true,
      data: sentiment,
    });
  } catch (error) {
    logger.error('Sentiment analysis failed:', error);
    return next(new AppError('Sentiment analysis failed', 500));
  }
}));

/**
 * @swagger
 * /nlp/reprocess-meeting/{id}:
 *   post:
 *     summary: Reprocess a meeting with updated NLP models
 *     tags: [NLP]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Meeting reprocessed successfully
 *       404:
 *         description: Meeting not found
 */
router.post('/reprocess-meeting/:id', catchAsync(async (req, res, next) => {
  const meeting = await Meeting.findByPk(req.params.id);
  if (!meeting) {
    return next(new AppError('Meeting not found', 404));
  }

  try {
    // Process meeting notes with NLP
    const nlpResults = await nlpService.processText(meeting.meeting_notes, {
      subject: meeting.meeting_subject,
      client_name: meeting.client_name,
      organizer_name: meeting.organizer_name,
    });

    // Update meeting with new summary and sentiment
    meeting.summary = nlpResults.summary;
    meeting.sentiment_score = nlpResults.sentiment.score;
    await meeting.save();

    // Delete existing auto-generated tags
    await Tag.destroy({
      where: {
        meeting_id: meeting.id,
        source: ['nlp_model', 'rule_based', 'hybrid'],
      },
    });

    // Create new tags
    const tagPromises = nlpResults.all_tags.map(tag => 
      Tag.create({
        meeting_id: meeting.id,
        tag_name: tag.name,
        tag_type: tag.type,
        confidence_score: tag.confidence,
        category: tag.category,
        source: tag.source,
        context: tag.context,
        frequency: tag.frequency,
      })
    );

    await Promise.all(tagPromises);

    // Fetch updated meeting with new tags
    const updatedMeeting = await Meeting.findByPk(meeting.id, {
      include: [
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'tag_name', 'tag_type', 'confidence_score', 'category', 'source'],
        },
      ],
    });

    logger.info(`Meeting ${meeting.id} reprocessed successfully`);

    res.json({
      success: true,
      message: 'Meeting reprocessed successfully',
      data: {
        meeting: updatedMeeting,
        processing_stats: {
          tags_created: nlpResults.all_tags.length,
          processing_time_ms: nlpResults.processing_time_ms,
          word_count: nlpResults.word_count,
        },
      },
    });
  } catch (error) {
    logger.error(`Error reprocessing meeting ${meeting.id}:`, error);
    return next(new AppError('Failed to reprocess meeting', 500));
  }
}));

/**
 * @swagger
 * /nlp/batch-reprocess:
 *   post:
 *     summary: Reprocess multiple meetings in batch
 *     tags: [NLP]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               meeting_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *               filters:
 *                 type: object
 *                 properties:
 *                   start_date:
 *                     type: string
 *                     format: date
 *                   end_date:
 *                     type: string
 *                     format: date
 *                   client_name:
 *                     type: string
 *     responses:
 *       200:
 *         description: Batch processing initiated
 */
router.post('/batch-reprocess', [
  body('meeting_ids').optional().isArray(),
  body('filters').optional().isObject(),
], catchAsync(async (req, res, next) => {
  const { meeting_ids, filters } = req.body;

  let meetings;
  
  if (meeting_ids && meeting_ids.length > 0) {
    // Process specific meetings
    meetings = await Meeting.findAll({
      where: {
        id: meeting_ids,
      },
    });
  } else if (filters) {
    // Process meetings based on filters
    const where = {};
    if (filters.start_date && filters.end_date) {
      where.meeting_date = {
        [require('sequelize').Op.between]: [filters.start_date, filters.end_date],
      };
    }
    if (filters.client_name) {
      where.client_name = {
        [require('sequelize').Op.iLike]: `%${filters.client_name}%`,
      };
    }
    
    meetings = await Meeting.findAll({ where });
  } else {
    return next(new AppError('Either meeting_ids or filters must be provided', 400));
  }

  if (meetings.length === 0) {
    return next(new AppError('No meetings found to process', 404));
  }

  // Process meetings asynchronously (in background)
  const processingPromises = meetings.map(async (meeting) => {
    try {
      const nlpResults = await nlpService.processText(meeting.meeting_notes, {
        subject: meeting.meeting_subject,
        client_name: meeting.client_name,
        organizer_name: meeting.organizer_name,
      });

      // Update meeting
      meeting.summary = nlpResults.summary;
      meeting.sentiment_score = nlpResults.sentiment.score;
      await meeting.save();

      // Update tags
      await Tag.destroy({
        where: {
          meeting_id: meeting.id,
          source: ['nlp_model', 'rule_based', 'hybrid'],
        },
      });

      const tagPromises = nlpResults.all_tags.map(tag => 
        Tag.create({
          meeting_id: meeting.id,
          tag_name: tag.name,
          tag_type: tag.type,
          confidence_score: tag.confidence,
          category: tag.category,
          source: tag.source,
          context: tag.context,
          frequency: tag.frequency,
        })
      );

      await Promise.all(tagPromises);
      
      return { meeting_id: meeting.id, status: 'success' };
    } catch (error) {
      logger.error(`Error processing meeting ${meeting.id}:`, error);
      return { meeting_id: meeting.id, status: 'failed', error: error.message };
    }
  });

  // Don't wait for all to complete, return immediately
  Promise.all(processingPromises).then(results => {
    logger.info(`Batch processing completed for ${meetings.length} meetings`);
  });

  res.json({
    success: true,
    message: 'Batch processing initiated',
    data: {
      meetings_queued: meetings.length,
      meeting_ids: meetings.map(m => m.id),
    },
  });
}));

module.exports = router;
