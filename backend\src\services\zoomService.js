const axios = require('axios');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

class ZoomService {
  constructor() {
    this.apiKey = process.env.ZOOM_API_KEY;
    this.apiSecret = process.env.ZOOM_API_SECRET;
    this.baseURL = 'https://api.zoom.us/v2';
    this.jwtToken = null;
    this.tokenExpiry = null;
  }

  // Generate JWT token for Zoom API
  generateJWT() {
    const payload = {
      iss: this.apiKey,
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour expiry
    };

    this.jwtToken = jwt.sign(payload, this.apiSecret);
    this.tokenExpiry = Date.now() + (55 * 60 * 1000); // 55 minutes
    
    return this.jwtToken;
  }

  // Get valid JWT token
  getValidToken() {
    if (!this.jwtToken || Date.now() >= this.tokenExpiry) {
      return this.generateJWT();
    }
    return this.jwtToken;
  }

  // Make API request to Zoom
  async makeRequest(method, endpoint, data = null) {
    try {
      const token = this.getValidToken();
      
      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      logger.error('Zoom API request failed:', error.response?.data || error.message);
      throw new Error(`Zoom API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Create a Zoom meeting
  async createMeeting(meetingData) {
    try {
      const {
        topic,
        start_time,
        duration = 60,
        timezone = 'UTC',
        agenda = '',
        host_email,
        password,
        settings = {}
      } = meetingData;

      const meetingPayload = {
        topic,
        type: 2, // Scheduled meeting
        start_time,
        duration,
        timezone,
        agenda,
        password,
        settings: {
          host_video: true,
          participant_video: true,
          cn_meeting: false,
          in_meeting: false,
          join_before_host: false,
          mute_upon_entry: true,
          watermark: false,
          use_pmi: false,
          approval_type: 2,
          audio: 'both',
          auto_recording: 'none',
          enforce_login: false,
          enforce_login_domains: '',
          alternative_hosts: '',
          close_registration: false,
          show_share_button: true,
          allow_multiple_devices: true,
          registrants_confirmation_email: true,
          waiting_room: true,
          request_permission_to_unmute_participants: false,
          global_dial_in_countries: ['US'],
          global_dial_in_numbers: [],
          contact_name: '',
          contact_email: '',
          registrants_email_notification: true,
          meeting_authentication: false,
          authentication_option: '',
          authentication_domains: '',
          additional_data_center_regions: [],
          breakout_room: {
            enable: false,
            rooms: []
          },
          ...settings
        }
      };

      const result = await this.makeRequest('POST', `/users/${host_email}/meetings`, meetingPayload);
      
      logger.info(`Zoom meeting created: ${result.id}`);
      
      return {
        meeting_id: result.id,
        join_url: result.join_url,
        start_url: result.start_url,
        password: result.password,
        host_email: result.host_email,
        topic: result.topic,
        start_time: result.start_time,
        duration: result.duration,
        timezone: result.timezone
      };

    } catch (error) {
      logger.error('Failed to create Zoom meeting:', error);
      throw error;
    }
  }

  // Get meeting details
  async getMeeting(meetingId) {
    try {
      const result = await this.makeRequest('GET', `/meetings/${meetingId}`);
      
      return {
        meeting_id: result.id,
        topic: result.topic,
        start_time: result.start_time,
        duration: result.duration,
        timezone: result.timezone,
        join_url: result.join_url,
        password: result.password,
        status: result.status,
        host_email: result.host_email
      };
    } catch (error) {
      logger.error(`Failed to get Zoom meeting ${meetingId}:`, error);
      throw error;
    }
  }

  // Update meeting
  async updateMeeting(meetingId, updateData) {
    try {
      const result = await this.makeRequest('PATCH', `/meetings/${meetingId}`, updateData);
      logger.info(`Zoom meeting updated: ${meetingId}`);
      return result;
    } catch (error) {
      logger.error(`Failed to update Zoom meeting ${meetingId}:`, error);
      throw error;
    }
  }

  // Delete meeting
  async deleteMeeting(meetingId) {
    try {
      await this.makeRequest('DELETE', `/meetings/${meetingId}`);
      logger.info(`Zoom meeting deleted: ${meetingId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to delete Zoom meeting ${meetingId}:`, error);
      throw error;
    }
  }

  // List meetings for a user
  async listMeetings(userEmail, type = 'scheduled') {
    try {
      const result = await this.makeRequest('GET', `/users/${userEmail}/meetings?type=${type}`);
      return result.meetings || [];
    } catch (error) {
      logger.error(`Failed to list meetings for ${userEmail}:`, error);
      throw error;
    }
  }

  // Get meeting participants
  async getMeetingParticipants(meetingId) {
    try {
      const result = await this.makeRequest('GET', `/meetings/${meetingId}/participants`);
      return result.participants || [];
    } catch (error) {
      logger.error(`Failed to get participants for meeting ${meetingId}:`, error);
      throw error;
    }
  }

  // Get meeting recordings
  async getMeetingRecordings(meetingId) {
    try {
      const result = await this.makeRequest('GET', `/meetings/${meetingId}/recordings`);
      return result.recording_files || [];
    } catch (error) {
      logger.error(`Failed to get recordings for meeting ${meetingId}:`, error);
      throw error;
    }
  }

  // Start meeting (for hosts)
  async startMeeting(meetingId) {
    try {
      const meeting = await this.getMeeting(meetingId);
      return {
        start_url: meeting.start_url,
        meeting_id: meetingId
      };
    } catch (error) {
      logger.error(`Failed to start meeting ${meetingId}:`, error);
      throw error;
    }
  }

  // Join meeting (for participants)
  async joinMeeting(meetingId) {
    try {
      const meeting = await this.getMeeting(meetingId);
      return {
        join_url: meeting.join_url,
        password: meeting.password,
        meeting_id: meetingId
      };
    } catch (error) {
      logger.error(`Failed to get join info for meeting ${meetingId}:`, error);
      throw error;
    }
  }

  // Generate meeting invitation
  generateInvitation(meetingData) {
    const {
      topic,
      start_time,
      duration,
      join_url,
      password,
      host_name
    } = meetingData;

    const startDate = new Date(start_time);
    const endDate = new Date(startDate.getTime() + (duration * 60000));

    return `
Subject: Invitation to ${topic}

You are invited to a Zoom meeting.

Topic: ${topic}
Time: ${startDate.toLocaleString()}
Duration: ${duration} minutes

Join Zoom Meeting
${join_url}

Meeting ID: ${meetingData.meeting_id}
Password: ${password}

One tap mobile
+1234567890,,${meetingData.meeting_id}#,,,,*${password}# US

Dial by your location
****** 567 890 US

Meeting ID: ${meetingData.meeting_id}
Password: ${password}

Find your local number: https://zoom.us/u/abc123

Hosted by: ${host_name}
    `.trim();
  }

  // Validate Zoom webhook signature
  validateWebhookSignature(payload, signature, timestamp) {
    const message = `v0:${timestamp}:${payload}`;
    const hashForVerify = crypto
      .createHmac('sha256', process.env.ZOOM_WEBHOOK_SECRET)
      .update(message, 'utf8')
      .digest('hex');
    
    const hashToCompare = `v0=${hashForVerify}`;
    return hashToCompare === signature;
  }

  // Process Zoom webhook events
  async processWebhookEvent(event) {
    try {
      const { event: eventType, payload } = event;
      
      logger.info(`Processing Zoom webhook event: ${eventType}`);
      
      switch (eventType) {
        case 'meeting.started':
          await this.handleMeetingStarted(payload);
          break;
        case 'meeting.ended':
          await this.handleMeetingEnded(payload);
          break;
        case 'meeting.participant_joined':
          await this.handleParticipantJoined(payload);
          break;
        case 'meeting.participant_left':
          await this.handleParticipantLeft(payload);
          break;
        case 'recording.completed':
          await this.handleRecordingCompleted(payload);
          break;
        default:
          logger.info(`Unhandled Zoom event type: ${eventType}`);
      }
    } catch (error) {
      logger.error('Failed to process Zoom webhook event:', error);
      throw error;
    }
  }

  // Handle meeting started event
  async handleMeetingStarted(payload) {
    const { object: meeting } = payload;
    logger.info(`Meeting started: ${meeting.id}`);
    
    // Update meeting status in database
    // This would be implemented based on your database structure
  }

  // Handle meeting ended event
  async handleMeetingEnded(payload) {
    const { object: meeting } = payload;
    logger.info(`Meeting ended: ${meeting.id}`);
    
    // Update meeting status and duration in database
  }

  // Handle participant joined event
  async handleParticipantJoined(payload) {
    const { object: participant } = payload;
    logger.info(`Participant joined: ${participant.user_name} in meeting ${participant.id}`);
  }

  // Handle participant left event
  async handleParticipantLeft(payload) {
    const { object: participant } = payload;
    logger.info(`Participant left: ${participant.user_name} from meeting ${participant.id}`);
  }

  // Handle recording completed event
  async handleRecordingCompleted(payload) {
    const { object: recording } = payload;
    logger.info(`Recording completed for meeting: ${recording.id}`);
    
    // Process and store recording information
  }
}

module.exports = new ZoomService();
