# SmartConverge: Intelligent Meeting Analysis System

A Deep Learning-Based System for Intelligent Meeting Analysis, Automated Contextual Tagging, and Semantic Query Processing for Enhanced Business Decision-Making.

## Overview

SmartConverge is an end-to-end intelligent meeting analysis system that processes analyst conversations, extracts contextual information, generates summaries, and delivers semantic insights for enterprise decision-making.

## Features

- **Automated Contextual Tagging**: Extract explicit and implicit entities using hybrid NLP approaches
- **Intelligent Summarization**: Generate concise summaries using fine-tuned models
- **Semantic Query Processing**: Natural language queries with context-aware responses
- **Analyst Participation Tracking**: Map speakers to topics and roles
- **Client-Oriented Reporting**: Generate personalized reports with billing capabilities
- **Interactive Dashboard**: React.js-based visualization interface

## Architecture

### Backend
- Node.js with Express.js for REST API
- PostgreSQL with Sequelize ORM for structured data
- MongoDB with Mongoose for unstructured data
- NLP Processing: OpenAI GPT, spaCy integration
- Vector Database: Pinecone for semantic search

### Frontend
- React.js with TypeScript
- Material-UI components
- Chart.js for visualizations

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd smartcoverage
   ```

2. **Backend Setup**
   ```bash
   cd backend
   npm install
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ```

4. **Environment Setup**
   ```bash
   # All configuration is now in a single .env file at the root!
   # Edit the .env file with your API keys and database credentials
   code .env
   ```

5. **Database Setup**
   ```bash
   # PostgreSQL and MongoDB setup
   npm run db:migrate
   npm run db:seed
   ```

6. **Run the Application**
   ```bash
   # Backend (from backend directory)
   npm run dev

   # Frontend (from frontend directory)
   npm start
   ```

## Project Structure

```
smartcoverage/
├── backend/
│   ├── src/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   ├── middleware/
│   │   └── utils/
│   ├── package.json
│   └── server.js
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   └── utils/
│   ├── package.json
│   └── public/
├── data/
├── docs/
└── docker-compose.yml
```

## API Documentation

Once running, visit `http://localhost:3001/api/docs` for interactive API documentation.

## Environment Variables

🎉 **NEW: Single .env Configuration!**

All environment variables are now consolidated into **one `.env` file** at the root directory. No more confusion with multiple .env files!

### Key Variables to Configure
```env
# AI & NLP Services
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
DB_USER=your_username
DB_PASS=your_password
MONGODB_URI=mongodb://localhost:27017/smartcoverage

# Security
JWT_SECRET=your_unique_secret_key_here

# Application URLs
REACT_APP_API_URL=http://localhost:3001/api
```

📖 **For complete configuration details, see [ENV_SETUP_GUIDE.md](./ENV_SETUP_GUIDE.md)**

## Contributing

Please read CONTRIBUTING.md for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
