#!/bin/bash

# SmartConverge Setup Script
# This script sets up the complete SmartConverge system with deep learning NLP capabilities

set -e

echo "🚀 Setting up SmartConverge - Intelligent Meeting Analysis System"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if running on supported OS
check_os() {
    print_header "Checking Operating System..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_status "Linux detected"
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_status "macOS detected"
        OS="macos"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        print_status "Windows detected"
        OS="windows"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites..."
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    # Check Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_status "Python found: $PYTHON_VERSION"
    else
        print_error "Python 3 is not installed. Please install Python 3.8+ from https://python.org/"
        exit 1
    fi
    
    # Check pip
    if command -v pip3 &> /dev/null; then
        print_status "pip3 found"
    else
        print_error "pip3 is not installed. Please install pip3"
        exit 1
    fi
    
    # Check PostgreSQL
    if command -v psql &> /dev/null; then
        print_status "PostgreSQL found"
    else
        print_warning "PostgreSQL not found. Please install PostgreSQL 13+"
    fi
    
    # Check MongoDB
    if command -v mongod &> /dev/null; then
        print_status "MongoDB found"
    else
        print_warning "MongoDB not found. Please install MongoDB 5.0+"
    fi
    
    # Check Redis
    if command -v redis-server &> /dev/null; then
        print_status "Redis found"
    else
        print_warning "Redis not found. Please install Redis 6.0+"
    fi
}

# Setup environment files
setup_environment() {
    print_header "Setting up Environment Files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_status "Created backend/.env from template"
    else
        print_warning "backend/.env already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        cp frontend/.env.example frontend/.env
        print_status "Created frontend/.env from template"
    else
        print_warning "frontend/.env already exists"
    fi
    
    # NLP Service environment
    if [ ! -f "nlp-service/.env" ]; then
        cp nlp-service/.env.example nlp-service/.env
        print_status "Created nlp-service/.env from template"
    else
        print_warning "nlp-service/.env already exists"
    fi
    
    # Root environment
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_status "Created .env from template"
    else
        print_warning ".env already exists"
    fi
}

# Install backend dependencies
install_backend() {
    print_header "Installing Backend Dependencies..."
    
    cd backend
    
    if [ -f "package.json" ]; then
        print_status "Installing Node.js dependencies..."
        npm install
        print_status "Backend dependencies installed successfully"
    else
        print_error "package.json not found in backend directory"
        exit 1
    fi
    
    cd ..
}

# Install frontend dependencies
install_frontend() {
    print_header "Installing Frontend Dependencies..."
    
    cd frontend
    
    if [ -f "package.json" ]; then
        print_status "Installing React dependencies..."
        npm install
        print_status "Frontend dependencies installed successfully"
    else
        print_error "package.json not found in frontend directory"
        exit 1
    fi
    
    cd ..
}

# Install NLP service dependencies
install_nlp_service() {
    print_header "Installing NLP Service Dependencies..."
    
    cd nlp-service
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install dependencies
    if [ -f "requirements.txt" ]; then
        print_status "Installing Python dependencies..."
        pip install -r requirements.txt
        print_status "NLP service dependencies installed successfully"
    else
        print_error "requirements.txt not found in nlp-service directory"
        exit 1
    fi
    
    # Download spaCy model
    print_status "Downloading spaCy English model..."
    python -m spacy download en_core_web_sm
    
    deactivate
    cd ..
}

# Setup databases
setup_databases() {
    print_header "Setting up Databases..."
    
    # Create PostgreSQL database
    print_status "Setting up PostgreSQL database..."
    
    # Check if PostgreSQL is running
    if pgrep -x "postgres" > /dev/null; then
        print_status "PostgreSQL is running"
        
        # Create database and user
        psql -c "CREATE DATABASE smartcoverage;" 2>/dev/null || print_warning "Database smartcoverage may already exist"
        psql -c "CREATE USER smartcoverage_user WITH PASSWORD 'smartcoverage_password';" 2>/dev/null || print_warning "User smartcoverage_user may already exist"
        psql -c "GRANT ALL PRIVILEGES ON DATABASE smartcoverage TO smartcoverage_user;" 2>/dev/null
        
        print_status "PostgreSQL database setup completed"
    else
        print_warning "PostgreSQL is not running. Please start PostgreSQL and run database migrations manually"
    fi
    
    # MongoDB setup
    print_status "MongoDB will be configured automatically when the application starts"
    
    # Redis setup
    print_status "Redis will be configured automatically when the application starts"
}

# Create necessary directories
create_directories() {
    print_header "Creating Directories..."
    
    # Backend directories
    mkdir -p backend/logs
    mkdir -p backend/uploads
    
    # NLP service directories
    mkdir -p nlp-service/data
    mkdir -p nlp-service/models
    mkdir -p nlp-service/logs
    mkdir -p nlp-service/cache
    
    print_status "Directories created successfully"
}

# Make scripts executable
make_scripts_executable() {
    print_header "Making Scripts Executable..."
    
    chmod +x scripts/*.sh
    chmod +x nlp-service/start.py
    chmod +x backend/scripts/seed.js
    
    print_status "Scripts made executable"
}

# Main setup function
main() {
    print_header "SmartConverge Setup Starting..."
    
    check_os
    check_prerequisites
    setup_environment
    create_directories
    install_backend
    install_frontend
    install_nlp_service
    setup_databases
    make_scripts_executable
    
    print_header "Setup Complete!"
    echo ""
    print_status "SmartConverge has been set up successfully!"
    echo ""
    print_status "Next steps:"
    echo "1. Review and update environment files (.env) with your specific configuration"
    echo "2. Start the databases (PostgreSQL, MongoDB, Redis)"
    echo "3. Run database migrations: cd backend && npm run migrate"
    echo "4. Seed the database: cd backend && npm run seed"
    echo "5. Start the services:"
    echo "   - NLP Service: cd nlp-service && python start.py"
    echo "   - Backend API: cd backend && npm run dev"
    echo "   - Frontend: cd frontend && npm start"
    echo ""
    print_status "Access the application at: http://localhost:3000"
    print_status "API documentation at: http://localhost:3001/api-docs"
    print_status "NLP Service API at: http://localhost:8000/docs"
    echo ""
    print_warning "Note: The first startup may take longer as deep learning models are downloaded"
}

# Run main function
main "$@"
