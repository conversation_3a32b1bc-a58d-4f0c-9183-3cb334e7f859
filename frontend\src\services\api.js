import axios from 'axios';
import toast from 'react-hot-toast';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      return Promise.reject(error);
    }

    // Show error toast for other errors
    const message = error.response?.data?.message || error.message || 'An error occurred';
    toast.error(message);

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getProfile: () => api.get('/auth/profile'),
  logout: () => api.post('/auth/logout'),
};

// Meetings API
export const meetingsAPI = {
  getAll: (params) => api.get('/meetings', { params }),
  getById: (id) => api.get(`/meetings/${id}`),
  create: (data) => api.post('/meetings', data),
  update: (id, data) => api.put(`/meetings/${id}`, data),
  delete: (id) => api.delete(`/meetings/${id}`),
};

// Clients API
export const clientsAPI = {
  getAll: (params) => api.get('/clients', { params }),
  getById: (id) => api.get(`/clients/${id}`),
  create: (data) => api.post('/clients', data),
  update: (id, data) => api.put(`/clients/${id}`, data),
  getMeetings: (id, params) => api.get(`/clients/${id}/meetings`, { params }),
  getAnalytics: (id, params) => api.get(`/clients/${id}/analytics`, { params }),
};

// Analytics API
export const analyticsAPI = {
  getDashboard: (params) => api.get('/analytics/dashboard', { params }),
  getTrends: (params) => api.get('/analytics/trends', { params }),
  getTagAnalysis: (params) => api.get('/analytics/tag-analysis', { params }),
};

// Reports API
export const reportsAPI = {
  getClientSummary: (params) => api.get('/reports/client-summary', { params }),
  getTagInsights: (params) => api.get('/reports/tag-insights', { params }),
};

// NLP API
export const nlpAPI = {
  analyze: (data) => api.post('/nlp/analyze', data),
  extractTags: (data) => api.post('/nlp/extract-tags', data),
  summarize: (data) => api.post('/nlp/summarize', data),
  analyzeSentiment: (data) => api.post('/nlp/sentiment', data),
  reprocessMeeting: (id) => api.post(`/nlp/reprocess-meeting/${id}`),
  batchReprocess: (data) => api.post('/nlp/batch-reprocess', data),
};

export default api;
