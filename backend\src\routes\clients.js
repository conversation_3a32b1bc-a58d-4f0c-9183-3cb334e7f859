const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { Client, Meeting, Tag } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

const router = express.Router();

/**
 * @swagger
 * /clients:
 *   get:
 *     summary: Get all clients with optional filtering
 *     tags: [Clients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: industry
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, prospect, churned]
 *       - in: query
 *         name: tier
 *         schema:
 *           type: string
 *           enum: [premium, standard, basic]
 *     responses:
 *       200:
 *         description: List of clients
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;

  // Build where clause
  const where = {};
  if (req.query.industry) {
    where.industry = { [Op.iLike]: `%${req.query.industry}%` };
  }
  if (req.query.status) {
    where.status = req.query.status;
  }
  if (req.query.tier) {
    where.tier = req.query.tier;
  }

  const { count, rows: clients } = await Client.findAndCountAll({
    where,
    order: [['name', 'ASC']],
    limit,
    offset,
  });

  res.json({
    success: true,
    data: {
      clients,
      pagination: {
        current_page: page,
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: limit,
      },
    },
  });
}));

/**
 * @swagger
 * /clients/{id}:
 *   get:
 *     summary: Get a specific client by ID
 *     tags: [Clients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Client details
 *       404:
 *         description: Client not found
 */
router.get('/:id', catchAsync(async (req, res, next) => {
  const client = await Client.findByPk(req.params.id);

  if (!client) {
    return next(new AppError('Client not found', 404));
  }

  // Get client's recent meetings
  const recentMeetings = await Meeting.findAll({
    where: {
      client_name: client.name,
    },
    order: [['meeting_date', 'DESC']],
    limit: 10,
    include: [
      {
        model: Tag,
        as: 'tags',
        attributes: ['tag_name', 'confidence_score', 'category'],
      },
    ],
  });

  // Get client's top tags
  const topTags = await Tag.findAll({
    attributes: [
      'tag_name',
      'category',
      [require('sequelize').fn('COUNT', require('sequelize').col('tag_name')), 'frequency'],
      [require('sequelize').fn('AVG', require('sequelize').col('confidence_score')), 'avg_confidence'],
    ],
    include: [{
      model: Meeting,
      as: 'meeting',
      where: {
        client_name: client.name,
      },
      attributes: [],
    }],
    group: ['tag_name', 'category'],
    order: [[require('sequelize').literal('frequency'), 'DESC']],
    limit: 20,
  });

  res.json({
    success: true,
    data: {
      client,
      recent_meetings: recentMeetings,
      top_tags: topTags.map(tag => ({
        name: tag.tag_name,
        category: tag.category,
        frequency: parseInt(tag.dataValues.frequency),
        avg_confidence: parseFloat(tag.dataValues.avg_confidence),
      })),
    },
  });
}));

/**
 * @swagger
 * /clients:
 *   post:
 *     summary: Create a new client
 *     tags: [Clients]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Client'
 *     responses:
 *       201:
 *         description: Client created successfully
 *       400:
 *         description: Validation error
 */
router.post('/', [
  body('name').notEmpty().trim(),
  body('industry').optional().trim(),
  body('contact_email').optional().isEmail(),
  body('billing_rate').optional().isFloat({ min: 0 }),
  body('tier').optional().isIn(['premium', 'standard', 'basic']),
  body('status').optional().isIn(['active', 'inactive', 'prospect', 'churned']),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }

  const client = await Client.create(req.body);

  res.status(201).json({
    success: true,
    message: 'Client created successfully',
    data: client,
  });
}));

/**
 * @swagger
 * /clients/{id}:
 *   put:
 *     summary: Update a client
 *     tags: [Clients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Client'
 *     responses:
 *       200:
 *         description: Client updated successfully
 *       404:
 *         description: Client not found
 */
router.put('/:id', [
  body('name').optional().notEmpty().trim(),
  body('industry').optional().trim(),
  body('contact_email').optional().isEmail(),
  body('billing_rate').optional().isFloat({ min: 0 }),
  body('tier').optional().isIn(['premium', 'standard', 'basic']),
  body('status').optional().isIn(['active', 'inactive', 'prospect', 'churned']),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }

  const client = await Client.findByPk(req.params.id);
  if (!client) {
    return next(new AppError('Client not found', 404));
  }

  await client.update(req.body);

  res.json({
    success: true,
    message: 'Client updated successfully',
    data: client,
  });
}));

/**
 * @swagger
 * /clients/{id}/meetings:
 *   get:
 *     summary: Get all meetings for a specific client
 *     tags: [Clients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: Client meetings
 *       404:
 *         description: Client not found
 */
router.get('/:id/meetings', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
], catchAsync(async (req, res, next) => {
  const client = await Client.findByPk(req.params.id);
  if (!client) {
    return next(new AppError('Client not found', 404));
  }

  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;

  const { count, rows: meetings } = await Meeting.findAndCountAll({
    where: {
      client_name: client.name,
    },
    include: [
      {
        model: Tag,
        as: 'tags',
        attributes: ['tag_name', 'confidence_score', 'category'],
      },
    ],
    order: [['meeting_date', 'DESC']],
    limit,
    offset,
  });

  res.json({
    success: true,
    data: {
      client: {
        id: client.id,
        name: client.name,
        industry: client.industry,
      },
      meetings,
      pagination: {
        current_page: page,
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: limit,
      },
    },
  });
}));

/**
 * @swagger
 * /clients/{id}/analytics:
 *   get:
 *     summary: Get analytics for a specific client
 *     tags: [Clients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [month, quarter, year, all]
 *           default: all
 *     responses:
 *       200:
 *         description: Client analytics
 *       404:
 *         description: Client not found
 */
router.get('/:id/analytics', [
  query('period').optional().isIn(['month', 'quarter', 'year', 'all']),
], catchAsync(async (req, res, next) => {
  const client = await Client.findByPk(req.params.id);
  if (!client) {
    return next(new AppError('Client not found', 404));
  }

  const period = req.query.period || 'all';
  
  // Calculate date range
  let startDate = null;
  if (period !== 'all') {
    const now = new Date();
    switch (period) {
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'quarter':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
    }
  }

  const whereClause = { client_name: client.name };
  if (startDate) {
    whereClause.meeting_date = { [Op.gte]: startDate };
  }

  const [
    meetingStats,
    sentimentStats,
    investmentStats,
    tagStats,
    meetingTrends
  ] = await Promise.all([
    // Basic meeting statistics
    Meeting.findAll({
      attributes: [
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'total_meetings'],
        [require('sequelize').fn('AVG', require('sequelize').col('sentiment_score')), 'avg_sentiment'],
        [require('sequelize').fn('AVG', require('sequelize').col('client_satisfaction')), 'avg_satisfaction'],
        [require('sequelize').fn('SUM', require('sequelize').col('billing_amount')), 'total_billed'],
      ],
      where: whereClause,
    }),
    
    // Sentiment distribution
    Meeting.findAll({
      attributes: [
        [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN sentiment_score > 0.1 THEN 1 END')), 'positive'],
        [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN sentiment_score < -0.1 THEN 1 END')), 'negative'],
        [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN sentiment_score BETWEEN -0.1 AND 0.1 THEN 1 END')), 'neutral'],
      ],
      where: {
        ...whereClause,
        sentiment_score: { [Op.ne]: null },
      },
    }),
    
    // Investment statistics
    Meeting.findAll({
      attributes: [
        [require('sequelize').fn('SUM', require('sequelize').col('investment_amount_discussed')), 'total_investment'],
        [require('sequelize').fn('AVG', require('sequelize').col('investment_amount_discussed')), 'avg_investment'],
        [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN investment_amount_discussed > 0 THEN 1 END')), 'investment_meetings'],
      ],
      where: whereClause,
    }),
    
    // Top tags for this client
    Tag.findAll({
      attributes: [
        'tag_name',
        'category',
        [require('sequelize').fn('COUNT', require('sequelize').col('tag_name')), 'frequency'],
        [require('sequelize').fn('AVG', require('sequelize').col('confidence_score')), 'avg_confidence'],
      ],
      include: [{
        model: Meeting,
        as: 'meeting',
        where: whereClause,
        attributes: [],
      }],
      group: ['tag_name', 'category'],
      order: [[require('sequelize').literal('frequency'), 'DESC']],
      limit: 15,
    }),
    
    // Meeting trends over time
    Meeting.findAll({
      attributes: [
        [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('meeting_date'), '%Y-%m'), 'month'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
        [require('sequelize').fn('AVG', require('sequelize').col('sentiment_score')), 'avg_sentiment'],
      ],
      where: whereClause,
      group: [require('sequelize').literal('month')],
      order: [require('sequelize').literal('month')],
    }),
  ]);

  res.json({
    success: true,
    data: {
      client: {
        id: client.id,
        name: client.name,
        industry: client.industry,
        tier: client.tier,
      },
      period,
      analytics: {
        meeting_stats: {
          total_meetings: parseInt(meetingStats[0]?.dataValues?.total_meetings || 0),
          avg_sentiment: parseFloat(meetingStats[0]?.dataValues?.avg_sentiment || 0),
          avg_satisfaction: parseFloat(meetingStats[0]?.dataValues?.avg_satisfaction || 0),
          total_billed: parseFloat(meetingStats[0]?.dataValues?.total_billed || 0),
        },
        sentiment_distribution: {
          positive: parseInt(sentimentStats[0]?.dataValues?.positive || 0),
          negative: parseInt(sentimentStats[0]?.dataValues?.negative || 0),
          neutral: parseInt(sentimentStats[0]?.dataValues?.neutral || 0),
        },
        investment_stats: {
          total_investment: parseFloat(investmentStats[0]?.dataValues?.total_investment || 0),
          avg_investment: parseFloat(investmentStats[0]?.dataValues?.avg_investment || 0),
          investment_meetings: parseInt(investmentStats[0]?.dataValues?.investment_meetings || 0),
        },
        top_tags: tagStats.map(tag => ({
          name: tag.tag_name,
          category: tag.category,
          frequency: parseInt(tag.dataValues.frequency),
          avg_confidence: parseFloat(tag.dataValues.avg_confidence),
        })),
        meeting_trends: meetingTrends.map(trend => ({
          month: trend.dataValues.month,
          count: parseInt(trend.dataValues.count),
          avg_sentiment: parseFloat(trend.dataValues.avg_sentiment || 0),
        })),
      },
    },
  });
}));

module.exports = router;
