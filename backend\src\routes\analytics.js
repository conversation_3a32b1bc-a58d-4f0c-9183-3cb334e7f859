const express = require('express');
const { query, validationResult } = require('express-validator');
const { Meeting, Tag, Client, User } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

const router = express.Router();

/**
 * @swagger
 * /analytics/dashboard:
 *   get:
 *     summary: Get dashboard analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *           default: month
 *     responses:
 *       200:
 *         description: Dashboard analytics data
 */
router.get('/dashboard', [
  query('period').optional().isIn(['week', 'month', 'quarter', 'year']),
], catchAsync(async (req, res) => {
  const period = req.query.period || 'month';
  
  // Calculate date range
  const now = new Date();
  let startDate;
  
  switch (period) {
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'quarter':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case 'year':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default: // month
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  // Get basic metrics
  const [
    totalMeetings,
    totalClients,
    totalTags,
    recentMeetings,
    topTags,
    clientDistribution,
    sentimentAnalysis,
    investmentMetrics
  ] = await Promise.all([
    // Total meetings in period
    Meeting.count({
      where: {
        meeting_date: { [Op.gte]: startDate },
      },
    }),
    
    // Total unique clients
    Meeting.count({
      distinct: true,
      col: 'client_name',
      where: {
        meeting_date: { [Op.gte]: startDate },
      },
    }),
    
    // Total tags generated
    Tag.count({
      include: [{
        model: Meeting,
        as: 'meeting',
        where: {
          meeting_date: { [Op.gte]: startDate },
        },
      }],
    }),
    
    // Recent meetings
    Meeting.findAll({
      where: {
        meeting_date: { [Op.gte]: startDate },
      },
      order: [['meeting_date', 'DESC']],
      limit: 5,
      attributes: ['id', 'meeting_subject', 'client_name', 'meeting_date', 'sentiment_score'],
    }),
    
    // Top tags
    Tag.findAll({
      attributes: [
        'tag_name',
        [require('sequelize').fn('COUNT', require('sequelize').col('tag_name')), 'count'],
        [require('sequelize').fn('AVG', require('sequelize').col('confidence_score')), 'avg_confidence'],
      ],
      include: [{
        model: Meeting,
        as: 'meeting',
        where: {
          meeting_date: { [Op.gte]: startDate },
        },
        attributes: [],
      }],
      group: ['tag_name'],
      order: [[require('sequelize').literal('count'), 'DESC']],
      limit: 10,
    }),
    
    // Client distribution by industry
    Meeting.findAll({
      attributes: [
        'client_industry',
        [require('sequelize').fn('COUNT', require('sequelize').col('client_industry')), 'count'],
      ],
      where: {
        meeting_date: { [Op.gte]: startDate },
        client_industry: { [Op.ne]: null },
      },
      group: ['client_industry'],
      order: [[require('sequelize').literal('count'), 'DESC']],
    }),
    
    // Sentiment analysis
    Meeting.findAll({
      attributes: [
        [require('sequelize').fn('AVG', require('sequelize').col('sentiment_score')), 'avg_sentiment'],
        [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN sentiment_score > 0.1 THEN 1 END')), 'positive_count'],
        [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN sentiment_score < -0.1 THEN 1 END')), 'negative_count'],
        [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN sentiment_score BETWEEN -0.1 AND 0.1 THEN 1 END')), 'neutral_count'],
      ],
      where: {
        meeting_date: { [Op.gte]: startDate },
        sentiment_score: { [Op.ne]: null },
      },
    }),
    
    // Investment metrics
    Meeting.findAll({
      attributes: [
        [require('sequelize').fn('SUM', require('sequelize').col('investment_amount_discussed')), 'total_investment'],
        [require('sequelize').fn('AVG', require('sequelize').col('investment_amount_discussed')), 'avg_investment'],
        [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN investment_amount_discussed > 0 THEN 1 END')), 'investment_meetings'],
      ],
      where: {
        meeting_date: { [Op.gte]: startDate },
      },
    }),
  ]);

  res.json({
    success: true,
    data: {
      period,
      date_range: {
        start: startDate.toISOString(),
        end: now.toISOString(),
      },
      metrics: {
        total_meetings: totalMeetings,
        total_clients: totalClients,
        total_tags: totalTags,
        avg_sentiment: sentimentAnalysis[0]?.dataValues?.avg_sentiment || 0,
        total_investment_discussed: investmentMetrics[0]?.dataValues?.total_investment || 0,
      },
      recent_meetings: recentMeetings,
      top_tags: topTags.map(tag => ({
        name: tag.tag_name,
        count: parseInt(tag.dataValues.count),
        avg_confidence: parseFloat(tag.dataValues.avg_confidence),
      })),
      client_distribution: clientDistribution.map(item => ({
        industry: item.client_industry,
        count: parseInt(item.dataValues.count),
      })),
      sentiment_distribution: {
        positive: parseInt(sentimentAnalysis[0]?.dataValues?.positive_count || 0),
        negative: parseInt(sentimentAnalysis[0]?.dataValues?.negative_count || 0),
        neutral: parseInt(sentimentAnalysis[0]?.dataValues?.neutral_count || 0),
      },
      investment_metrics: {
        total_amount: parseFloat(investmentMetrics[0]?.dataValues?.total_investment || 0),
        average_amount: parseFloat(investmentMetrics[0]?.dataValues?.avg_investment || 0),
        meetings_with_investment: parseInt(investmentMetrics[0]?.dataValues?.investment_meetings || 0),
      },
    },
  });
}));

/**
 * @swagger
 * /analytics/trends:
 *   get:
 *     summary: Get trend analysis over time
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: metric
 *         schema:
 *           type: string
 *           enum: [meetings, sentiment, investment, tags]
 *           default: meetings
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [daily, weekly, monthly]
 *           default: weekly
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *     responses:
 *       200:
 *         description: Trend analysis data
 */
router.get('/trends', [
  query('metric').optional().isIn(['meetings', 'sentiment', 'investment', 'tags']),
  query('period').optional().isIn(['daily', 'weekly', 'monthly']),
  query('days').optional().isInt({ min: 7, max: 365 }),
], catchAsync(async (req, res) => {
  const metric = req.query.metric || 'meetings';
  const period = req.query.period || 'weekly';
  const days = parseInt(req.query.days) || 30;
  
  const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
  
  let dateFormat;
  switch (period) {
    case 'daily':
      dateFormat = '%Y-%m-%d';
      break;
    case 'monthly':
      dateFormat = '%Y-%m';
      break;
    default: // weekly
      dateFormat = '%Y-%u'; // Year-Week
  }

  let query;
  switch (metric) {
    case 'sentiment':
      query = Meeting.findAll({
        attributes: [
          [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('meeting_date'), dateFormat), 'period'],
          [require('sequelize').fn('AVG', require('sequelize').col('sentiment_score')), 'value'],
          [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
        ],
        where: {
          meeting_date: { [Op.gte]: startDate },
          sentiment_score: { [Op.ne]: null },
        },
        group: [require('sequelize').literal('period')],
        order: [require('sequelize').literal('period')],
      });
      break;
      
    case 'investment':
      query = Meeting.findAll({
        attributes: [
          [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('meeting_date'), dateFormat), 'period'],
          [require('sequelize').fn('SUM', require('sequelize').col('investment_amount_discussed')), 'value'],
          [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN investment_amount_discussed > 0 THEN 1 END')), 'count'],
        ],
        where: {
          meeting_date: { [Op.gte]: startDate },
        },
        group: [require('sequelize').literal('period')],
        order: [require('sequelize').literal('period')],
      });
      break;
      
    case 'tags':
      query = Tag.findAll({
        attributes: [
          [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('meeting.meeting_date'), dateFormat), 'period'],
          [require('sequelize').fn('COUNT', require('sequelize').col('Tag.id')), 'value'],
          [require('sequelize').fn('AVG', require('sequelize').col('confidence_score')), 'avg_confidence'],
        ],
        include: [{
          model: Meeting,
          as: 'meeting',
          where: {
            meeting_date: { [Op.gte]: startDate },
          },
          attributes: [],
        }],
        group: [require('sequelize').literal('period')],
        order: [require('sequelize').literal('period')],
      });
      break;
      
    default: // meetings
      query = Meeting.findAll({
        attributes: [
          [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('meeting_date'), dateFormat), 'period'],
          [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'value'],
        ],
        where: {
          meeting_date: { [Op.gte]: startDate },
        },
        group: [require('sequelize').literal('period')],
        order: [require('sequelize').literal('period')],
      });
  }

  const results = await query;
  
  res.json({
    success: true,
    data: {
      metric,
      period,
      date_range: {
        start: startDate.toISOString(),
        end: new Date().toISOString(),
      },
      trends: results.map(item => ({
        period: item.dataValues.period,
        value: parseFloat(item.dataValues.value || 0),
        count: parseInt(item.dataValues.count || 0),
        avg_confidence: parseFloat(item.dataValues.avg_confidence || 0),
      })),
    },
  });
}));

/**
 * @swagger
 * /analytics/tag-analysis:
 *   get:
 *     summary: Get detailed tag analysis
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: min_confidence
 *         schema:
 *           type: number
 *           minimum: 0
 *           maximum: 1
 *           default: 0.7
 *     responses:
 *       200:
 *         description: Tag analysis data
 */
router.get('/tag-analysis', [
  query('min_confidence').optional().isFloat({ min: 0, max: 1 }),
], catchAsync(async (req, res) => {
  const minConfidence = parseFloat(req.query.min_confidence) || 0.7;
  const category = req.query.category;

  const whereClause = {
    confidence_score: { [Op.gte]: minConfidence },
  };
  
  if (category) {
    whereClause.category = category;
  }

  const [tagStats, categoryStats, confidenceDistribution, tagTypes] = await Promise.all([
    // Tag frequency and confidence
    Tag.findAll({
      attributes: [
        'tag_name',
        'category',
        [require('sequelize').fn('COUNT', require('sequelize').col('tag_name')), 'frequency'],
        [require('sequelize').fn('AVG', require('sequelize').col('confidence_score')), 'avg_confidence'],
        [require('sequelize').fn('MIN', require('sequelize').col('confidence_score')), 'min_confidence'],
        [require('sequelize').fn('MAX', require('sequelize').col('confidence_score')), 'max_confidence'],
      ],
      where: whereClause,
      group: ['tag_name', 'category'],
      order: [[require('sequelize').literal('frequency'), 'DESC']],
      limit: 50,
    }),
    
    // Category distribution
    Tag.findAll({
      attributes: [
        'category',
        [require('sequelize').fn('COUNT', require('sequelize').col('category')), 'count'],
        [require('sequelize').fn('AVG', require('sequelize').col('confidence_score')), 'avg_confidence'],
      ],
      where: whereClause,
      group: ['category'],
      order: [[require('sequelize').literal('count'), 'DESC']],
    }),
    
    // Confidence score distribution
    Tag.findAll({
      attributes: [
        [require('sequelize').fn('ROUND', require('sequelize').col('confidence_score'), 1), 'confidence_range'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
      ],
      where: whereClause,
      group: [require('sequelize').literal('confidence_range')],
      order: [require('sequelize').literal('confidence_range')],
    }),
    
    // Tag type distribution
    Tag.findAll({
      attributes: [
        'tag_type',
        'source',
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
        [require('sequelize').fn('AVG', require('sequelize').col('confidence_score')), 'avg_confidence'],
      ],
      where: whereClause,
      group: ['tag_type', 'source'],
      order: [[require('sequelize').literal('count'), 'DESC']],
    }),
  ]);

  res.json({
    success: true,
    data: {
      filters: {
        min_confidence: minConfidence,
        category: category || 'all',
      },
      tag_stats: tagStats.map(tag => ({
        name: tag.tag_name,
        category: tag.category,
        frequency: parseInt(tag.dataValues.frequency),
        avg_confidence: parseFloat(tag.dataValues.avg_confidence),
        confidence_range: {
          min: parseFloat(tag.dataValues.min_confidence),
          max: parseFloat(tag.dataValues.max_confidence),
        },
      })),
      category_distribution: categoryStats.map(cat => ({
        category: cat.category,
        count: parseInt(cat.dataValues.count),
        avg_confidence: parseFloat(cat.dataValues.avg_confidence),
      })),
      confidence_distribution: confidenceDistribution.map(conf => ({
        range: parseFloat(conf.dataValues.confidence_range),
        count: parseInt(conf.dataValues.count),
      })),
      tag_type_distribution: tagTypes.map(type => ({
        type: type.tag_type,
        source: type.source,
        count: parseInt(type.dataValues.count),
        avg_confidence: parseFloat(type.dataValues.avg_confidence),
      })),
    },
  });
}));

module.exports = router;
