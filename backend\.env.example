# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Database Configuration - PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_NAME=smartcoverage
DB_USER=your_username
DB_PASS=your_password
DB_DIALECT=postgres

# Database Configuration - MongoDB
MONGODB_URI=mongodb://localhost:27017/smartcoverage

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# Google Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-1.5-pro
GEMINI_ENDPOINT=https://generativelanguage.googleapis.com/v1beta

# NLP Service Configuration
NLP_SERVICE_URL=http://localhost:8000

# Zoom Integration Configuration
ZOOM_API_KEY=your_zoom_api_key_here
ZOOM_API_SECRET=your_zoom_api_secret_here
ZOOM_WEBHOOK_SECRET=your_zoom_webhook_secret_here

# Pinecone Configuration (for vector database)
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=smartcoverage-embeddings

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Audio Processing
WHISPER_API_URL=https://api.openai.com/v1/audio/transcriptions

# NLP Processing
SPACY_MODEL=en_core_web_sm
CONFIDENCE_THRESHOLD=0.7

# Client Billing
DEFAULT_HOURLY_RATE=150
BILLING_CURRENCY=USD
