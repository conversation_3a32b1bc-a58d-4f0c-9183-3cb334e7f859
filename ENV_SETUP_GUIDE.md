# 🔧 SmartConverge Environment Configuration Guide

## ✅ **SIMPLIFIED CONFIGURATION**

We've consolidated all environment variables into a **single `.env` file** at the root of the project!

### **Before (❌ Confusing)**
```
smartcoverage/
├── .env                    # Root config
├── backend/.env           # Backend config
├── frontend/.env          # Frontend config
└── nlp-service/.env       # NLP service config
```

### **After (✅ Clean & Simple)**
```
smartcoverage/
└── .env                   # ALL configuration in ONE file
```

## 🚀 **Quick Setup**

1. **Edit the root `.env` file** with your actual values:
   ```bash
   # Open the .env file in your editor
   code .env
   ```

2. **Update these key values**:
   ```env
   # Required for AI features
   GEMINI_API_KEY=your_actual_gemini_api_key_here
   OPENAI_API_KEY=your_actual_openai_api_key_here
   
   # Database credentials (if using external databases)
   DB_USER=your_db_username
   DB_PASS=your_db_password
   
   # JWT Secret (change for production)
   JWT_SECRET=your_unique_secret_key_here
   ```

3. **Start all services**:
   ```bash
   # Start NLP service
   cd nlp-service && python simple_server.py
   
   # Start backend (in new terminal)
   cd backend && npm run dev
   
   # Start frontend (in new terminal)
   cd frontend && npm start
   ```

## 📋 **Configuration Sections**

The `.env` file is organized into clear sections:

- **🏗️ APPLICATION CONFIGURATION** - Basic app settings
- **🌐 SERVER CONFIGURATION** - Ports and URLs
- **🗄️ DATABASE CONFIGURATION** - PostgreSQL, MongoDB, Redis
- **🔐 AUTHENTICATION & SECURITY** - JWT, CORS, rate limiting
- **🤖 AI & NLP CONFIGURATION** - Gemini, OpenAI, Hugging Face
- **📊 VECTOR DATABASE CONFIGURATION** - Pinecone settings
- **📹 ZOOM INTEGRATION** - Zoom API credentials
- **📁 FILE HANDLING** - Upload paths and limits
- **🎯 TRAINING CONFIGURATION** - ML model training settings
- **💻 GPU CONFIGURATION** - CUDA settings
- **📝 LOGGING CONFIGURATION** - Log levels and files
- **📧 EMAIL CONFIGURATION** - SMTP settings
- **💰 BILLING CONFIGURATION** - Pricing settings
- **🐛 DEVELOPMENT & DEBUGGING** - Debug flags

## 🔄 **Migration from Old Setup**

If you had custom values in the old `.env` files, they've been automatically consolidated. The old files have been removed:

- ~~`backend/.env`~~ ❌ Removed
- ~~`frontend/.env`~~ ❌ Removed  
- ~~`nlp-service/.env`~~ ❌ Removed

All services now read from the root `.env` file automatically.

## 🛠️ **How It Works**

Each service has been updated to load the root `.env` file:

- **Backend**: `require('dotenv').config({ path: '../.env' })`
- **NLP Service**: `load_dotenv(dotenv_path='../.env')`
- **Frontend**: Uses `REACT_APP_*` variables from root `.env`

## 🚨 **Important Notes**

1. **Frontend Variables**: Only variables starting with `REACT_APP_` are available in the frontend
2. **Security**: Never commit real API keys to version control
3. **Production**: Use environment-specific values for production deployment
4. **Backup**: Keep a backup of your API keys in a secure location

## 🎯 **Benefits**

✅ **Single source of truth** for all configuration  
✅ **No more confusion** about which .env file to edit  
✅ **Easier deployment** - just one file to manage  
✅ **Better organization** with clear sections  
✅ **Reduced errors** from inconsistent configurations  

## 🆘 **Troubleshooting**

**Problem**: Service can't find environment variables  
**Solution**: Make sure the `.env` file is in the root directory

**Problem**: Frontend can't access variables  
**Solution**: Ensure frontend variables start with `REACT_APP_`

**Problem**: Database connection fails  
**Solution**: Check database credentials in the `.env` file

---

**Need help?** Check the main README.md or create an issue in the repository.
