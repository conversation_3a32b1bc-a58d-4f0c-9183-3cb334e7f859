const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const zoomNlpService = require('../services/zoomNlpService');
const Meeting = require('../models/Meeting');
const logger = require('../utils/logger');

// Create meeting with NLP integration
router.post('/meetings/create-with-nlp', authenticateToken, async (req, res) => {
  try {
    const {
      topic,
      start_time,
      duration = 60,
      timezone = 'UTC',
      agenda = '',
      client_name,
      client_industry,
      meeting_type = 'client_call',
      enable_nlp_analysis = true,
      settings = {}
    } = req.body;

    // Validate required fields
    if (!topic || !start_time || !client_name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: topic, start_time, client_name'
      });
    }

    const meetingData = {
      topic,
      start_time,
      duration,
      timezone,
      agenda,
      host_email: req.user.email,
      password: Math.random().toString(36).substring(2, 10), // Generate random password
      client_name,
      client_industry,
      meeting_type,
      enable_nlp_analysis,
      settings: {
        auto_recording: enable_nlp_analysis ? 'cloud' : 'none',
        waiting_room: true,
        mute_upon_entry: true,
        participant_video: true,
        ...settings
      }
    };

    const result = await zoomNlpService.createMeetingWithNLP(meetingData);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error('Failed to create meeting with NLP:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create meeting'
    });
  }
});

// Get meeting details
router.get('/meetings/:meetingId', authenticateToken, async (req, res) => {
  try {
    const { meetingId } = req.params;

    // Get from database first
    const dbMeeting = await Meeting.findOne({
      where: { zoom_meeting_id: meetingId }
    });

    if (!dbMeeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Get live data from Zoom if needed
    let zoomData = null;
    if (dbMeeting.status === 'scheduled' || dbMeeting.status === 'in_progress') {
      try {
        zoomData = await zoomNlpService.makeZoomRequest('GET', `/meetings/${meetingId}`);
      } catch (error) {
        logger.warning(`Failed to get live Zoom data for meeting ${meetingId}:`, error);
      }
    }

    res.json({
      success: true,
      data: {
        database: dbMeeting,
        zoom: zoomData
      }
    });

  } catch (error) {
    logger.error('Failed to get meeting details:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get meeting details'
    });
  }
});

// Join meeting
router.post('/meetings/:meetingId/join', authenticateToken, async (req, res) => {
  try {
    const { meetingId } = req.params;

    const dbMeeting = await Meeting.findOne({
      where: { zoom_meeting_id: meetingId }
    });

    if (!dbMeeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Check if user has permission to join
    const canJoin = req.user.role === 'admin' ||
                   req.user.email === dbMeeting.organizer_name ||
                   dbMeeting.attendees.includes(req.user.email) ||
                   req.user.email === dbMeeting.client_name;

    if (!canJoin) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to join this meeting'
      });
    }

    const joinInfo = {
      join_url: dbMeeting.zoom_join_url,
      password: dbMeeting.zoom_password,
      meeting_id: meetingId
    };

    res.json({
      success: true,
      data: joinInfo
    });

  } catch (error) {
    logger.error('Failed to get join meeting info:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get join meeting info'
    });
  }
});

// Start meeting (for hosts)
router.post('/meetings/:meetingId/start', authenticateToken, async (req, res) => {
  try {
    const { meetingId } = req.params;

    const dbMeeting = await Meeting.findOne({
      where: { zoom_meeting_id: meetingId }
    });

    if (!dbMeeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Check if user is the host or admin
    const canStart = req.user.role === 'admin' ||
                    req.user.email === dbMeeting.organizer_name;

    if (!canStart) {
      return res.status(403).json({
        success: false,
        message: 'Only the meeting host or admin can start the meeting'
      });
    }

    const startInfo = {
      start_url: dbMeeting.zoom_start_url,
      meeting_id: meetingId
    };

    res.json({
      success: true,
      data: startInfo
    });

  } catch (error) {
    logger.error('Failed to start meeting:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to start meeting'
    });
  }
});

// Get real-time meeting insights
router.get('/meetings/:meetingId/insights', authenticateToken, async (req, res) => {
  try {
    const { meetingId } = req.params;

    const insights = await zoomNlpService.generateRealTimeInsights(meetingId);

    if (!insights) {
      return res.status(404).json({
        success: false,
        message: 'No active session found for this meeting'
      });
    }

    res.json({
      success: true,
      data: insights
    });

  } catch (error) {
    logger.error('Failed to get real-time insights:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get real-time insights'
    });
  }
});

// Get meeting analytics
router.get('/meetings/:meetingId/analytics', authenticateToken, async (req, res) => {
  try {
    const { meetingId } = req.params;

    const dbMeeting = await Meeting.findOne({
      where: { zoom_meeting_id: meetingId }
    });

    if (!dbMeeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Get meeting session data
    const meetingSession = zoomNlpService.activeMeetings.get(meetingId);
    let analytics = {};

    if (meetingSession) {
      analytics = zoomNlpService.getMeetingStats(meetingSession);
    } else {
      // Get historical analytics from database
      analytics = {
        duration_ms: dbMeeting.meeting_duration_minutes * 60000,
        sentiment_score: dbMeeting.sentiment_score,
        tags: dbMeeting.manual_tags || [],
        summary: dbMeeting.summary
      };
    }

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    logger.error('Failed to get meeting analytics:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get meeting analytics'
    });
  }
});

// Zoom webhook endpoint
router.post('/webhook', async (req, res) => {
  try {
    const event = req.body;

    // Validate webhook signature if configured
    if (process.env.ZOOM_WEBHOOK_SECRET) {
      const signature = req.headers['x-zm-signature'];
      const timestamp = req.headers['x-zm-request-timestamp'];

      const isValid = zoomNlpService.validateWebhookSignature(
        JSON.stringify(req.body),
        signature,
        timestamp
      );

      if (!isValid) {
        return res.status(401).json({
          success: false,
          message: 'Invalid webhook signature'
        });
      }
    }

    // Process the webhook event
    await zoomNlpService.processZoomWebhookWithNLP(event);

    res.json({
      success: true,
      message: 'Webhook processed successfully'
    });

  } catch (error) {
    logger.error('Failed to process Zoom webhook:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to process webhook'
    });
  }
});

// List meetings for a user
router.get('/meetings/list', authenticateToken, async (req, res) => {
  try {
    const { userEmail, type = 'all' } = req.query;
    const email = userEmail || req.user.email;

    let whereClause = {};

    // Role-based filtering
    if (req.user.role === 'client') {
      // Clients can only see their own meetings
      whereClause = {
        $or: [
          { organizer_name: req.user.email },
          { client_name: req.user.email },
          { attendees: { $contains: [req.user.email] } }
        ]
      };
    } else if (req.user.role === 'manager') {
      // Managers can see meetings they organize or are assigned to
      whereClause = {
        $or: [
          { organizer_name: req.user.email },
          { created_by: req.user.id },
          { attendees: { $contains: [req.user.email] } }
        ]
      };
    }
    // Admins can see all meetings (no additional filtering)

    if (type !== 'all') {
      whereClause.status = type;
    }

    const meetings = await Meeting.findAll({
      where: whereClause,
      order: [['meeting_date', 'DESC']],
      limit: 50
    });

    res.json({
      success: true,
      data: meetings
    });

  } catch (error) {
    logger.error('Failed to list meetings:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to list meetings'
    });
  }
});

// Update meeting
router.patch('/meetings/:meetingId', authenticateToken, async (req, res) => {
  try {
    // Check if user has permission to update meetings
    if (!['admin', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to update meetings'
      });
    }

    const { meetingId } = req.params;
    const updateData = req.body;

    const dbMeeting = await Meeting.findOne({
      where: { zoom_meeting_id: meetingId }
    });

    if (!dbMeeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Update in database
    await dbMeeting.update(updateData);

    // Update in Zoom if meeting is still scheduled
    if (dbMeeting.status === 'scheduled' && updateData.zoom_update) {
      try {
        await zoomNlpService.makeZoomRequest('PATCH', `/meetings/${meetingId}`, updateData.zoom_update);
      } catch (error) {
        logger.warning(`Failed to update Zoom meeting ${meetingId}:`, error);
      }
    }

    res.json({
      success: true,
      data: dbMeeting
    });

  } catch (error) {
    logger.error('Failed to update meeting:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update meeting'
    });
  }
});

// Delete meeting
router.delete('/meetings/:meetingId', authenticateToken, async (req, res) => {
  try {
    // Check if user has permission to delete meetings
    if (!['admin', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to delete meetings'
      });
    }

    const { meetingId } = req.params;

    const dbMeeting = await Meeting.findOne({
      where: { zoom_meeting_id: meetingId }
    });

    if (!dbMeeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Delete from Zoom if meeting is still scheduled
    if (dbMeeting.status === 'scheduled') {
      try {
        await zoomNlpService.makeZoomRequest('DELETE', `/meetings/${meetingId}`);
      } catch (error) {
        logger.warning(`Failed to delete Zoom meeting ${meetingId}:`, error);
      }
    }

    // Update status in database instead of deleting
    await dbMeeting.update({ status: 'cancelled' });

    res.json({
      success: true,
      message: 'Meeting cancelled successfully'
    });

  } catch (error) {
    logger.error('Failed to delete meeting:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete meeting'
    });
  }
});

// Download meeting summary
router.get('/meetings/:meetingId/summary/download', authenticateToken, async (req, res) => {
  try {
    const { meetingId } = req.params;
    const { format = 'pdf' } = req.query;

    const dbMeeting = await Meeting.findOne({
      where: { zoom_meeting_id: meetingId }
    });

    if (!dbMeeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Generate summary document
    const summaryData = {
      meeting: dbMeeting,
      format: format
    };

    // This would integrate with a document generation service
    // For now, return the summary as JSON
    res.json({
      success: true,
      data: summaryData
    });

  } catch (error) {
    logger.error('Failed to download meeting summary:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to download meeting summary'
    });
  }
});

module.exports = router;
