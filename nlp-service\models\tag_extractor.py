import torch
import torch.nn as nn
from transformers import AutoTokenizer, AutoModelForTokenClassification, AutoModel
from sentence_transformers import SentenceTransformer
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from pathlib import Path
import re
from collections import Counter
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from .base_model import BaseNLPModel
from config import FINAN<PERSON>AL_KEYWORDS, INDUSTRY_CATEGORIES

logger = logging.getLogger(__name__)

class TagExtractor(BaseNLPModel):
    """Advanced tag extraction using multiple NLP techniques"""
    
    def __init__(self, model_name: str = "dbmdz/bert-large-cased-finetuned-conll03-english", model_path: Optional[Path] = None):
        super().__init__(model_name, model_path)
        self.ner_model = None
        self.ner_tokenizer = None
        self.embedding_model = None
        self.tfidf_vectorizer = None
        self.keyword_patterns = self._compile_keyword_patterns()
        
    def load_model(self):
        """Load NER and embedding models"""
        try:
            # Load NER model
            if self.model_path and (self.model_path / "ner").exists():
                self.ner_tokenizer = AutoTokenizer.from_pretrained(self.model_path / "ner")
                self.ner_model = AutoModelForTokenClassification.from_pretrained(self.model_path / "ner")
            else:
                self.ner_tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.ner_model = AutoModelForTokenClassification.from_pretrained(self.model_name)
            
            self.ner_model.to(self.device)
            self.ner_model.eval()
            
            # Load sentence transformer for semantic similarity
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Initialize TF-IDF vectorizer
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 3),
                min_df=2
            )
            
            logger.info("Tag extraction models loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading tag extraction models: {e}")
            raise
    
    def predict(self, text: str) -> Dict[str, Any]:
        """Extract tags using multiple methods"""
        if not self.ner_model or not self.embedding_model:
            raise ValueError("Models not loaded")
        
        # Extract different types of tags
        explicit_tags = self._extract_explicit_tags(text)
        implicit_tags = self._extract_implicit_tags(text)
        entity_tags = self._extract_named_entities(text)
        keyword_tags = self._extract_keyword_tags(text)
        semantic_tags = self._extract_semantic_tags(text)
        
        # Combine and rank all tags
        all_tags = self._combine_and_rank_tags(
            explicit_tags, implicit_tags, entity_tags, keyword_tags, semantic_tags
        )
        
        return {
            "explicit_tags": explicit_tags,
            "implicit_tags": implicit_tags,
            "entity_tags": entity_tags,
            "keyword_tags": keyword_tags,
            "semantic_tags": semantic_tags,
            "all_tags": all_tags,
            "tag_count": len(all_tags),
            "confidence_stats": self._calculate_confidence_stats(all_tags)
        }
    
    def _extract_explicit_tags(self, text: str) -> List[Dict[str, Any]]:
        """Extract explicit tags using rule-based matching"""
        tags = []
        text_lower = text.lower()
        
        # Financial keywords
        for category, keywords in FINANCIAL_KEYWORDS.items():
            for keyword in keywords:
                pattern = r'\b' + re.escape(keyword.lower()) + r'\b'
                matches = re.finditer(pattern, text_lower)
                for match in matches:
                    tags.append({
                        "name": keyword,
                        "type": "explicit",
                        "category": category,
                        "confidence": 1.0,
                        "source": "rule_based",
                        "position": (match.start(), match.end()),
                        "context": self._extract_context(text, match.start(), match.end())
                    })
        
        # Industry keywords
        for industry, keywords in INDUSTRY_CATEGORIES.items():
            for keyword in keywords:
                pattern = r'\b' + re.escape(keyword.lower()) + r'\b'
                matches = re.finditer(pattern, text_lower)
                for match in matches:
                    tags.append({
                        "name": keyword,
                        "type": "explicit",
                        "category": industry,
                        "confidence": 1.0,
                        "source": "rule_based",
                        "position": (match.start(), match.end()),
                        "context": self._extract_context(text, match.start(), match.end())
                    })
        
        return self._deduplicate_tags(tags)
    
    def _extract_implicit_tags(self, text: str) -> List[Dict[str, Any]]:
        """Extract implicit tags using semantic analysis"""
        tags = []
        
        # Use TF-IDF to find important terms
        try:
            # Fit TF-IDF on the text (split into sentences)
            sentences = self._split_into_sentences(text)
            if len(sentences) > 1:
                tfidf_matrix = self.tfidf_vectorizer.fit_transform(sentences)
                feature_names = self.tfidf_vectorizer.get_feature_names_out()
                
                # Get top TF-IDF terms
                mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)
                top_indices = np.argsort(mean_scores)[-20:]  # Top 20 terms
                
                for idx in top_indices:
                    if mean_scores[idx] > 0.1:  # Threshold for significance
                        term = feature_names[idx]
                        if len(term) > 2 and not term.isdigit():
                            tags.append({
                                "name": term,
                                "type": "implicit",
                                "category": self._categorize_term(term),
                                "confidence": float(mean_scores[idx]),
                                "source": "tfidf",
                                "relevance_score": float(mean_scores[idx])
                            })
        
        except Exception as e:
            logger.warning(f"TF-IDF extraction failed: {e}")
        
        return sorted(tags, key=lambda x: x["confidence"], reverse=True)[:10]
    
    def _extract_named_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract named entities using NER model"""
        tags = []
        
        # Tokenize text
        inputs = self.ner_tokenizer(
            text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512,
            return_offsets_mapping=True
        ).to(self.device)
        
        # Get predictions
        with torch.no_grad():
            outputs = self.ner_model(**{k: v for k, v in inputs.items() if k != 'offset_mapping'})
            predictions = torch.argmax(outputs.logits, dim=-1)
            probabilities = torch.softmax(outputs.logits, dim=-1)
        
        # Convert predictions to tags
        tokens = self.ner_tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
        offset_mapping = inputs['offset_mapping'][0] if 'offset_mapping' in inputs else None
        
        current_entity = None
        current_tokens = []
        current_confidence = []
        
        for i, (token, pred_id, probs) in enumerate(zip(tokens, predictions[0], probabilities[0])):
            if token in ['[CLS]', '[SEP]', '[PAD]']:
                continue
            
            label = self.ner_model.config.id2label[pred_id.item()]
            confidence = probs.max().item()
            
            if label.startswith('B-'):  # Beginning of entity
                if current_entity:
                    # Save previous entity
                    entity_text = self.ner_tokenizer.convert_tokens_to_string(current_tokens)
                    tags.append({
                        "name": entity_text.strip(),
                        "type": "explicit",
                        "category": current_entity.lower(),
                        "confidence": np.mean(current_confidence),
                        "source": "ner_model",
                        "entity_type": current_entity
                    })
                
                current_entity = label[2:]  # Remove 'B-' prefix
                current_tokens = [token]
                current_confidence = [confidence]
                
            elif label.startswith('I-') and current_entity == label[2:]:  # Inside entity
                current_tokens.append(token)
                current_confidence.append(confidence)
                
            else:  # Outside entity
                if current_entity:
                    entity_text = self.ner_tokenizer.convert_tokens_to_string(current_tokens)
                    tags.append({
                        "name": entity_text.strip(),
                        "type": "explicit",
                        "category": current_entity.lower(),
                        "confidence": np.mean(current_confidence),
                        "source": "ner_model",
                        "entity_type": current_entity
                    })
                current_entity = None
                current_tokens = []
                current_confidence = []
        
        # Handle last entity
        if current_entity:
            entity_text = self.ner_tokenizer.convert_tokens_to_string(current_tokens)
            tags.append({
                "name": entity_text.strip(),
                "type": "explicit",
                "category": current_entity.lower(),
                "confidence": np.mean(current_confidence),
                "source": "ner_model",
                "entity_type": current_entity
            })
        
        return [tag for tag in tags if tag["confidence"] > 0.7 and len(tag["name"]) > 1]
    
    def _extract_keyword_tags(self, text: str) -> List[Dict[str, Any]]:
        """Extract keywords using pattern matching"""
        tags = []
        
        for pattern_name, pattern in self.keyword_patterns.items():
            matches = pattern.finditer(text.lower())
            for match in matches:
                keyword = match.group()
                tags.append({
                    "name": keyword,
                    "type": "explicit",
                    "category": self._categorize_term(keyword),
                    "confidence": 0.9,
                    "source": "pattern_matching",
                    "pattern": pattern_name,
                    "position": (match.start(), match.end())
                })
        
        return self._deduplicate_tags(tags)
    
    def _extract_semantic_tags(self, text: str) -> List[Dict[str, Any]]:
        """Extract tags using semantic similarity"""
        tags = []
        
        # Create embeddings for text chunks
        sentences = self._split_into_sentences(text)
        if len(sentences) < 2:
            return tags
        
        try:
            sentence_embeddings = self.embedding_model.encode(sentences)
            
            # Cluster sentences to find topics
            n_clusters = min(5, len(sentences) // 2)
            if n_clusters > 1:
                kmeans = KMeans(n_clusters=n_clusters, random_state=42)
                clusters = kmeans.fit_predict(sentence_embeddings)
                
                # Extract representative terms from each cluster
                for cluster_id in range(n_clusters):
                    cluster_sentences = [sentences[i] for i in range(len(sentences)) if clusters[i] == cluster_id]
                    cluster_text = " ".join(cluster_sentences)
                    
                    # Extract key terms from cluster
                    cluster_tags = self._extract_cluster_keywords(cluster_text, cluster_id)
                    tags.extend(cluster_tags)
        
        except Exception as e:
            logger.warning(f"Semantic tag extraction failed: {e}")
        
        return tags[:10]  # Limit to top 10 semantic tags
    
    def _extract_cluster_keywords(self, text: str, cluster_id: int) -> List[Dict[str, Any]]:
        """Extract keywords from a text cluster"""
        # Simple keyword extraction using word frequency
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        word_freq = Counter(words)
        
        # Filter out common words and get top terms
        stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will', 'with'}
        
        keywords = []
        for word, freq in word_freq.most_common(5):
            if word not in stop_words and len(word) > 3:
                keywords.append({
                    "name": word,
                    "type": "implicit",
                    "category": "semantic_cluster",
                    "confidence": min(0.8, freq / len(words) * 10),  # Normalize frequency
                    "source": "semantic_clustering",
                    "cluster_id": cluster_id,
                    "frequency": freq
                })
        
        return keywords
    
    def train(self, train_data: List[Dict], validation_data: Optional[List[Dict]] = None):
        """Train custom tag extraction model"""
        # This would implement training for a custom NER model
        # For now, we'll focus on fine-tuning the existing models
        logger.info("Training functionality to be implemented for custom tag extraction")
        pass
    
    def _compile_keyword_patterns(self) -> Dict[str, re.Pattern]:
        """Compile regex patterns for keyword matching"""
        patterns = {}
        
        # Financial amounts
        patterns['financial_amounts'] = re.compile(r'\$[\d,]+(?:\.\d{2})?[kmb]?|\d+(?:\.\d+)?\s*(?:million|billion|thousand|dollars?|usd)', re.IGNORECASE)
        
        # Percentages
        patterns['percentages'] = re.compile(r'\d+(?:\.\d+)?%|\d+(?:\.\d+)?\s*percent', re.IGNORECASE)
        
        # Company indicators
        patterns['companies'] = re.compile(r'\b\w+\s+(?:inc|corp|llc|ltd|company|technologies|systems|solutions|group|holdings)\b', re.IGNORECASE)
        
        # Investment terms
        patterns['investment_terms'] = re.compile(r'\b(?:roi|return on investment|portfolio|asset|equity|debt|valuation|funding|capital)\b', re.IGNORECASE)
        
        return patterns
    
    def _combine_and_rank_tags(self, *tag_lists) -> List[Dict[str, Any]]:
        """Combine tags from different sources and rank by confidence"""
        all_tags = []
        for tag_list in tag_lists:
            all_tags.extend(tag_list)
        
        # Deduplicate and merge similar tags
        merged_tags = self._merge_similar_tags(all_tags)
        
        # Sort by confidence
        return sorted(merged_tags, key=lambda x: x["confidence"], reverse=True)[:20]
    
    def _merge_similar_tags(self, tags: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Merge similar tags and combine their confidence scores"""
        tag_groups = {}
        
        for tag in tags:
            name = tag["name"].lower().strip()
            if name in tag_groups:
                # Merge with existing tag
                existing = tag_groups[name]
                existing["confidence"] = max(existing["confidence"], tag["confidence"])
                existing["sources"] = existing.get("sources", [existing.get("source", "unknown")])
                if tag.get("source") not in existing["sources"]:
                    existing["sources"].append(tag.get("source", "unknown"))
            else:
                tag_groups[name] = tag.copy()
                tag_groups[name]["sources"] = [tag.get("source", "unknown")]
        
        return list(tag_groups.values())
    
    def _deduplicate_tags(self, tags: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate tags"""
        seen = set()
        unique_tags = []
        
        for tag in tags:
            key = (tag["name"].lower(), tag.get("category", ""))
            if key not in seen:
                seen.add(key)
                unique_tags.append(tag)
        
        return unique_tags
    
    def _categorize_term(self, term: str) -> str:
        """Categorize a term into industry/domain"""
        term_lower = term.lower()
        
        for category, keywords in INDUSTRY_CATEGORIES.items():
            if any(keyword in term_lower for keyword in keywords):
                return category
        
        for category, keywords in FINANCIAL_KEYWORDS.items():
            if any(keyword in term_lower for keyword in keywords):
                return category
        
        return "general"
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        import re
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if len(s.strip()) > 10]
    
    def _extract_context(self, text: str, start: int, end: int, window: int = 50) -> str:
        """Extract context around a matched term"""
        context_start = max(0, start - window)
        context_end = min(len(text), end + window)
        return text[context_start:context_end].strip()
    
    def _calculate_confidence_stats(self, tags: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate confidence statistics for tags"""
        if not tags:
            return {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}
        
        confidences = [tag["confidence"] for tag in tags]
        return {
            "mean": np.mean(confidences),
            "std": np.std(confidences),
            "min": np.min(confidences),
            "max": np.max(confidences)
        }
