const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
      notEmpty: true,
    },
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [6, 100],
    },
  },
  first_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 50],
    },
  },
  last_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 50],
    },
  },
  role: {
    type: DataTypes.ENUM('admin', 'analyst', 'manager', 'viewer'),
    allowNull: false,
    defaultValue: 'analyst',
  },
  department: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 100],
    },
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 20],
    },
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  last_login: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  password_reset_token: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  password_reset_expires: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  email_verified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  email_verification_token: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  preferences: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
  },
}, {
  tableName: 'users',
  timestamps: true,
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        user.password = await bcrypt.hash(user.password, 12);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        user.password = await bcrypt.hash(user.password, 12);
      }
    },
  },
  indexes: [
    {
      unique: true,
      fields: ['email'],
    },
    {
      fields: ['role'],
    },
    {
      fields: ['department'],
    },
    {
      fields: ['is_active'],
    },
  ],
});

// Instance methods
User.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  
  // Remove sensitive fields
  delete values.password;
  delete values.password_reset_token;
  delete values.email_verification_token;
  
  return values;
};

User.prototype.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

User.prototype.updateLastLogin = function() {
  this.last_login = new Date();
  return this.save();
};

User.prototype.getFullName = function() {
  return `${this.first_name} ${this.last_name}`;
};

// Class methods
User.findByEmail = function(email) {
  return this.findOne({
    where: {
      email: email.toLowerCase(),
      is_active: true,
    },
  });
};

User.findActiveUsers = function() {
  return this.findAll({
    where: {
      is_active: true,
    },
    order: [['last_name', 'ASC'], ['first_name', 'ASC']],
  });
};

User.findByRole = function(role) {
  return this.findAll({
    where: {
      role: role,
      is_active: true,
    },
    order: [['last_name', 'ASC'], ['first_name', 'ASC']],
  });
};

User.findByDepartment = function(department) {
  return this.findAll({
    where: {
      department: department,
      is_active: true,
    },
    order: [['last_name', 'ASC'], ['first_name', 'ASC']],
  });
};

module.exports = User;
