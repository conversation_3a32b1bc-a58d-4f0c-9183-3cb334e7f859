#!/usr/bin/env node

require('dotenv').config();
const { connectPostgreSQL } = require('../src/config/database');
const { connectMongoDB } = require('../src/config/mongodb');
const { seedDatabase } = require('../src/utils/seedData');
const logger = require('../src/utils/logger');

async function runSeeding() {
  try {
    logger.info('🌱 Starting SmartConverge database seeding...');
    
    // Connect to databases
    await connectPostgreSQL();
    await connectMongoDB();
    
    // Run seeding
    const results = await seedDatabase();
    
    logger.info('📊 Seeding Results:');
    logger.info(`   Users created: ${results.users}`);
    logger.info(`   Clients created: ${results.clients}`);
    logger.info(`   Meetings created: ${results.meetings}`);
    
    logger.info('✅ Database seeding completed successfully!');
    logger.info('');
    logger.info('🔑 Default Admin Credentials:');
    logger.info('   Email: <EMAIL>');
    logger.info('   Password: admin123');
    logger.info('');
    logger.info('👥 Sample Analyst Accounts:');
    logger.info('   <EMAIL> / password123');
    logger.info('   <EMAIL> / password123');
    logger.info('   <EMAIL> / password123');
    
    process.exit(0);
  } catch (error) {
    logger.error('❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
SmartConverge Database Seeding Script

Usage: node scripts/seed.js [options]

Options:
  --help, -h     Show this help message
  
This script will:
1. Clear existing data from the database
2. Create sample users, clients, and meetings
3. Process meetings with NLP to generate tags
4. Update client statistics

Make sure you have:
- PostgreSQL running and configured
- MongoDB running and configured
- Environment variables set in .env file
- OpenAI API key configured (optional, for NLP processing)

Default admin credentials after seeding:
  Email: <EMAIL>
  Password: admin123
`);
  process.exit(0);
}

// Run the seeding
runSeeding();
