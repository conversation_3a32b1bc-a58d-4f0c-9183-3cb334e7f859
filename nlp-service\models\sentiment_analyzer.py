import torch
import torch.nn as nn
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from typing import Dict, List, Any, Optional
import numpy as np
import logging
from pathlib import Path
from .base_model import BaseNLPModel

logger = logging.getLogger(__name__)

class SentimentAnalyzer(BaseNLPModel):
    """Advanced sentiment analysis model for meeting content"""
    
    def __init__(self, model_name: str = "cardiffnlp/twitter-roberta-base-sentiment-latest", model_path: Optional[Path] = None):
        super().__init__(model_name, model_path)
        self.label_mapping = {
            0: "negative",
            1: "neutral", 
            2: "positive"
        }
        
    def load_model(self):
        """Load sentiment analysis model"""
        try:
            if self.model_path and self.model_path.exists():
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
                self.model = AutoModelForSequenceClassification.from_pretrained(self.model_path)
            else:
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
            
            self.model.to(self.device)
            self.model.eval()
            logger.info(f"Sentiment model loaded: {self.model_name}")
            
        except Exception as e:
            logger.error(f"Error loading sentiment model: {e}")
            raise
    
    def predict(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of input text"""
        if not self.model or not self.tokenizer:
            raise ValueError("Model not loaded")
        
        # Preprocess text
        text = self._preprocess_text(text)
        
        # Tokenize
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        ).to(self.device)
        
        # Get predictions
        with torch.no_grad():
            outputs = self.model(**inputs)
            probabilities = torch.softmax(outputs.logits, dim=-1)
            predicted_class = torch.argmax(probabilities, dim=-1).item()
        
        # Convert probabilities to numpy for easier handling
        probs = probabilities.cpu().numpy()[0]
        
        # Calculate confidence and sentiment score
        confidence = float(probs.max())
        sentiment_score = self._calculate_sentiment_score(probs)
        
        return {
            "sentiment": self.label_mapping[predicted_class],
            "confidence": confidence,
            "sentiment_score": sentiment_score,  # -1 to 1 scale
            "probabilities": {
                "negative": float(probs[0]),
                "neutral": float(probs[1]),
                "positive": float(probs[2])
            },
            "raw_scores": probs.tolist()
        }
    
    def analyze_segments(self, text_segments: List[str]) -> Dict[str, Any]:
        """Analyze sentiment for multiple text segments"""
        segment_results = []
        overall_scores = []
        
        for i, segment in enumerate(text_segments):
            if len(segment.strip()) < 10:  # Skip very short segments
                continue
                
            result = self.predict(segment)
            result["segment_id"] = i
            result["text"] = segment[:100] + "..." if len(segment) > 100 else segment
            
            segment_results.append(result)
            overall_scores.append(result["sentiment_score"])
        
        # Calculate overall sentiment
        if overall_scores:
            overall_sentiment_score = np.mean(overall_scores)
            overall_sentiment = self._score_to_label(overall_sentiment_score)
            sentiment_variance = np.var(overall_scores)
        else:
            overall_sentiment_score = 0.0
            overall_sentiment = "neutral"
            sentiment_variance = 0.0
        
        return {
            "overall_sentiment": overall_sentiment,
            "overall_score": overall_sentiment_score,
            "sentiment_variance": sentiment_variance,
            "segment_count": len(segment_results),
            "segments": segment_results,
            "sentiment_distribution": self._calculate_distribution(segment_results)
        }
    
    def train(self, train_data: List[Dict], validation_data: Optional[List[Dict]] = None):
        """Fine-tune the sentiment model on domain-specific data"""
        from torch.utils.data import DataLoader
        from transformers import AdamW, get_linear_schedule_with_warmup
        from sklearn.metrics import accuracy_score, classification_report
        
        # Prepare datasets
        train_dataset = SentimentDataset(train_data, self.tokenizer)
        train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
        
        val_loader = None
        if validation_data:
            val_dataset = SentimentDataset(validation_data, self.tokenizer)
            val_loader = DataLoader(val_dataset, batch_size=16)
        
        # Setup training
        optimizer = AdamW(self.model.parameters(), lr=2e-5, weight_decay=0.01)
        total_steps = len(train_loader) * 3
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=100,
            num_training_steps=total_steps
        )
        
        # Training loop
        self.model.train()
        best_val_accuracy = 0.0
        
        for epoch in range(3):
            total_loss = 0
            all_predictions = []
            all_labels = []
            
            for batch in train_loader:
                optimizer.zero_grad()
                
                inputs = {k: v.to(self.device) for k, v in batch.items() if k != 'labels'}
                labels = batch['labels'].to(self.device)
                
                outputs = self.model(**inputs, labels=labels)
                loss = outputs.loss
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                optimizer.step()
                scheduler.step()
                
                total_loss += loss.item()
                
                # Collect predictions for metrics
                predictions = torch.argmax(outputs.logits, dim=-1)
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
            
            # Calculate training metrics
            avg_loss = total_loss / len(train_loader)
            train_accuracy = accuracy_score(all_labels, all_predictions)
            
            logger.info(f"Epoch {epoch + 1}")
            logger.info(f"  Training Loss: {avg_loss:.4f}")
            logger.info(f"  Training Accuracy: {train_accuracy:.4f}")
            
            # Validation
            if val_loader:
                val_accuracy, val_report = self._evaluate(val_loader)
                logger.info(f"  Validation Accuracy: {val_accuracy:.4f}")
                
                if val_accuracy > best_val_accuracy:
                    best_val_accuracy = val_accuracy
                    if self.model_path:
                        self.save_model(self.model_path)
                        logger.info(f"  New best model saved with accuracy: {val_accuracy:.4f}")
    
    def _evaluate(self, data_loader):
        """Evaluate model performance"""
        from sklearn.metrics import accuracy_score, classification_report
        
        self.model.eval()
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for batch in data_loader:
                inputs = {k: v.to(self.device) for k, v in batch.items() if k != 'labels'}
                labels = batch['labels'].to(self.device)
                
                outputs = self.model(**inputs)
                predictions = torch.argmax(outputs.logits, dim=-1)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        accuracy = accuracy_score(all_labels, all_predictions)
        report = classification_report(all_labels, all_predictions, target_names=list(self.label_mapping.values()))
        
        return accuracy, report
    
    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for sentiment analysis"""
        # Remove excessive whitespace
        text = " ".join(text.split())
        
        # Handle special cases for meeting content
        text = text.replace("@", " at ")
        text = text.replace("#", " hashtag ")
        
        return text
    
    def _calculate_sentiment_score(self, probabilities: np.ndarray) -> float:
        """Convert probabilities to sentiment score (-1 to 1)"""
        # probabilities: [negative, neutral, positive]
        negative_weight = -1.0
        neutral_weight = 0.0
        positive_weight = 1.0
        
        score = (probabilities[0] * negative_weight + 
                probabilities[1] * neutral_weight + 
                probabilities[2] * positive_weight)
        
        return float(score)
    
    def _score_to_label(self, score: float) -> str:
        """Convert sentiment score to label"""
        if score > 0.1:
            return "positive"
        elif score < -0.1:
            return "negative"
        else:
            return "neutral"
    
    def _calculate_distribution(self, segment_results: List[Dict]) -> Dict[str, float]:
        """Calculate sentiment distribution across segments"""
        if not segment_results:
            return {"positive": 0.0, "neutral": 0.0, "negative": 0.0}
        
        sentiments = [result["sentiment"] for result in segment_results]
        total = len(sentiments)
        
        return {
            "positive": sentiments.count("positive") / total,
            "neutral": sentiments.count("neutral") / total,
            "negative": sentiments.count("negative") / total
        }

class SentimentDataset(torch.utils.data.Dataset):
    """Dataset for sentiment analysis training"""
    
    def __init__(self, data: List[Dict], tokenizer, max_length: int = 512):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        # Map string labels to integers
        self.label_map = {"negative": 0, "neutral": 1, "positive": 2}
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text = item['text']
        label = item['label']
        
        # Convert string label to integer if needed
        if isinstance(label, str):
            label = self.label_map.get(label.lower(), 1)  # Default to neutral
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }
