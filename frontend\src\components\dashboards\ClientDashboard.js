import React, { useState, useEffect } from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typo<PERSON>,
  Box,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Avatar,
  Paper,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  VideoCall as VideoCallIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  AccessTime as AccessTimeIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  PlayArrow as PlayIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Feedback as FeedbackIcon,
  Star as StarIcon,
  CalendarToday as CalendarIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { meetingService } from '../../services/meetingService';
import { zoomService } from '../../services/zoomService';

const ClientDashboard = () => {
  const { user } = useAuth();
  const [meetings, setMeetings] = useState([]);
  const [upcomingMeetings, setUpcomingMeetings] = useState([]);
  const [recentMeetings, setRecentMeetings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedMeeting, setSelectedMeeting] = useState(null);
  const [meetingDetailsOpen, setMeetingDetailsOpen] = useState(false);

  useEffect(() => {
    loadClientData();
  }, []);

  const loadClientData = async () => {
    try {
      setLoading(true);
      const [allMeetings] = await Promise.all([
        meetingService.getClientMeetings(user.email)
      ]);

      const now = new Date();
      const upcoming = allMeetings.data?.filter(m => new Date(m.meeting_date) > now) || [];
      const recent = allMeetings.data?.filter(m => new Date(m.meeting_date) <= now) || [];

      setMeetings(allMeetings.data || []);
      setUpcomingMeetings(upcoming.slice(0, 5));
      setRecentMeetings(recent.slice(0, 5));
    } catch (error) {
      console.error('Failed to load client data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleJoinMeeting = async (meeting) => {
    try {
      if (meeting.zoom_meeting_id) {
        const joinInfo = await zoomService.joinMeeting(meeting.zoom_meeting_id);
        window.open(joinInfo.join_url, '_blank');
      } else {
        // Fallback for non-Zoom meetings
        alert('Meeting link will be provided by your advisor');
      }
    } catch (error) {
      console.error('Failed to join meeting:', error);
      alert('Unable to join meeting. Please contact your advisor.');
    }
  };

  const handleViewMeetingDetails = (meeting) => {
    setSelectedMeeting(meeting);
    setMeetingDetailsOpen(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled': return 'primary';
      case 'in_progress': return 'success';
      case 'completed': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getMeetingTimeStatus = (meetingDate) => {
    const now = new Date();
    const meeting = new Date(meetingDate);
    const diffMinutes = (meeting - now) / (1000 * 60);

    if (diffMinutes < 0) return { status: 'past', color: 'default', text: 'Completed' };
    if (diffMinutes <= 15) return { status: 'starting', color: 'success', text: 'Starting Soon' };
    if (diffMinutes <= 60) return { status: 'soon', color: 'warning', text: 'Starting in 1 hour' };
    return { status: 'scheduled', color: 'primary', text: 'Scheduled' };
  };

  const formatMeetingTime = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Box display="flex" alignItems="center" mb={3}>
        <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
          <PersonIcon />
        </Avatar>
        <Box>
          <Typography variant="h4">
            Welcome, {user?.first_name}
          </Typography>
          <Typography variant="subtitle1" color="textSecondary">
            Client Portal - Your Meeting Hub
          </Typography>
        </Box>
      </Box>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <ScheduleIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Upcoming
                  </Typography>
                  <Typography variant="h5">
                    {upcomingMeetings.length}
                  </Typography>
                  <Typography variant="caption" color="primary">
                    Meetings scheduled
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <AssignmentIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Completed
                  </Typography>
                  <Typography variant="h5">
                    {recentMeetings.length}
                  </Typography>
                  <Typography variant="caption" color="success.main">
                    This month
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <AccessTimeIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Hours
                  </Typography>
                  <Typography variant="h5">
                    {meetings.reduce((sum, m) => sum + (m.meeting_duration_minutes || 0), 0) / 60}h
                  </Typography>
                  <Typography variant="caption" color="info.main">
                    Meeting time
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Satisfaction
                  </Typography>
                  <Box display="flex" alignItems="center">
                    <Typography variant="h5" sx={{ mr: 1 }}>
                      4.8
                    </Typography>
                    <StarIcon color="warning" fontSize="small" />
                  </Box>
                  <Typography variant="caption" color="warning.main">
                    Average rating
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Upcoming Meetings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom display="flex" alignItems="center">
                <ScheduleIcon sx={{ mr: 1 }} />
                Upcoming Meetings
              </Typography>
              
              {upcomingMeetings.length === 0 ? (
                <Alert severity="info">
                  No upcoming meetings scheduled. Your advisor will contact you to schedule your next session.
                </Alert>
              ) : (
                <List>
                  {upcomingMeetings.map((meeting) => {
                    const timeStatus = getMeetingTimeStatus(meeting.meeting_date);
                    const { date, time } = formatMeetingTime(meeting.meeting_date);
                    
                    return (
                      <ListItem 
                        key={meeting.id}
                        sx={{ 
                          border: 1, 
                          borderColor: 'divider', 
                          borderRadius: 1, 
                          mb: 1,
                          bgcolor: timeStatus.status === 'starting' ? 'success.light' : 'background.paper'
                        }}
                      >
                        <ListItemIcon>
                          <VideoCallIcon color={timeStatus.color} />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                              <Typography variant="subtitle1" fontWeight="bold">
                                {meeting.subject}
                              </Typography>
                              <Chip 
                                label={timeStatus.text} 
                                color={timeStatus.color}
                                size="small"
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="textSecondary">
                                <CalendarIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                                {date} at {time}
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                <BusinessIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                                with {meeting.organizer_name}
                              </Typography>
                              {meeting.meeting_duration_minutes && (
                                <Typography variant="body2" color="textSecondary">
                                  <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                                  Duration: {meeting.meeting_duration_minutes} minutes
                                </Typography>
                              )}
                            </Box>
                          }
                        />
                        <Box display="flex" flexDirection="column" gap={1}>
                          {timeStatus.status === 'starting' && (
                            <Button
                              variant="contained"
                              color="success"
                              size="small"
                              startIcon={<PlayIcon />}
                              onClick={() => handleJoinMeeting(meeting)}
                            >
                              Join Now
                            </Button>
                          )}
                          {timeStatus.status === 'soon' && (
                            <Button
                              variant="outlined"
                              color="primary"
                              size="small"
                              startIcon={<VideoCallIcon />}
                              onClick={() => handleJoinMeeting(meeting)}
                            >
                              Join Meeting
                            </Button>
                          )}
                          <IconButton 
                            size="small" 
                            onClick={() => handleViewMeetingDetails(meeting)}
                            title="View Details"
                          >
                            <ViewIcon />
                          </IconButton>
                        </Box>
                      </ListItem>
                    );
                  })}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Meetings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom display="flex" alignItems="center">
                <AssignmentIcon sx={{ mr: 1 }} />
                Recent Meetings
              </Typography>
              
              {recentMeetings.length === 0 ? (
                <Alert severity="info">
                  No recent meetings found.
                </Alert>
              ) : (
                <List>
                  {recentMeetings.map((meeting) => {
                    const { date, time } = formatMeetingTime(meeting.meeting_date);
                    
                    return (
                      <ListItem 
                        key={meeting.id}
                        sx={{ 
                          border: 1, 
                          borderColor: 'divider', 
                          borderRadius: 1, 
                          mb: 1 
                        }}
                      >
                        <ListItemIcon>
                          <AssignmentIcon color="default" />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                              <Typography variant="subtitle1" fontWeight="bold">
                                {meeting.subject}
                              </Typography>
                              <Chip 
                                label={meeting.status} 
                                color={getStatusColor(meeting.status)}
                                size="small"
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="textSecondary">
                                <CalendarIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                                {date} at {time}
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                <BusinessIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                                with {meeting.organizer_name}
                              </Typography>
                              {meeting.summary && (
                                <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                                  {meeting.summary.substring(0, 100)}...
                                </Typography>
                              )}
                            </Box>
                          }
                        />
                        <Box display="flex" flexDirection="column" gap={1}>
                          <Tooltip title="View Meeting Summary">
                            <IconButton 
                              size="small" 
                              onClick={() => handleViewMeetingDetails(meeting)}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Download Summary">
                            <IconButton size="small">
                              <DownloadIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </ListItem>
                    );
                  })}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<FeedbackIcon />}
                    sx={{ py: 2 }}
                  >
                    Provide Feedback
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    sx={{ py: 2 }}
                  >
                    Download Reports
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<ScheduleIcon />}
                    sx={{ py: 2 }}
                  >
                    Request Meeting
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<PersonIcon />}
                    sx={{ py: 2 }}
                  >
                    Contact Advisor
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Meeting Details Dialog */}
      <Dialog 
        open={meetingDetailsOpen} 
        onClose={() => setMeetingDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Meeting Details
        </DialogTitle>
        <DialogContent>
          {selectedMeeting && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedMeeting.subject}
              </Typography>
              
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">
                    Date & Time
                  </Typography>
                  <Typography variant="body1">
                    {new Date(selectedMeeting.meeting_date).toLocaleString()}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">
                    Duration
                  </Typography>
                  <Typography variant="body1">
                    {selectedMeeting.meeting_duration_minutes || 'N/A'} minutes
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">
                    Advisor
                  </Typography>
                  <Typography variant="body1">
                    {selectedMeeting.organizer_name}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">
                    Status
                  </Typography>
                  <Chip 
                    label={selectedMeeting.status} 
                    color={getStatusColor(selectedMeeting.status)}
                    size="small"
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              {selectedMeeting.summary && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Meeting Summary
                  </Typography>
                  <Typography variant="body1" paragraph>
                    {selectedMeeting.summary}
                  </Typography>
                </Box>
              )}

              {selectedMeeting.manual_tags && selectedMeeting.manual_tags.length > 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Discussion Topics
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={1}>
                    {selectedMeeting.manual_tags.map((tag, index) => (
                      <Chip key={index} label={tag} size="small" variant="outlined" />
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMeetingDetailsOpen(false)}>
            Close
          </Button>
          {selectedMeeting?.status === 'completed' && (
            <Button variant="contained" startIcon={<DownloadIcon />}>
              Download Summary
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ClientDashboard;
