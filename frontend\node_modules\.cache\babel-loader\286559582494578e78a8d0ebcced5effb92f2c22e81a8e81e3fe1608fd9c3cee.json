{"ast": null, "code": "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' ||\n    // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "map": {"version": 3, "names": ["getBoundingClientRect", "getNodeScroll", "getNodeName", "isHTMLElement", "getWindowScrollBarX", "getDocumentElement", "isScrollParent", "round", "isElementScaled", "element", "rect", "scaleX", "width", "offsetWidth", "scaleY", "height", "offsetHeight", "getCompositeRect", "elementOrVirtualElement", "offsetParent", "isFixed", "isOffsetParentAnElement", "offsetParentIsScaled", "documentElement", "scroll", "scrollLeft", "scrollTop", "offsets", "x", "y", "clientLeft", "clientTop", "left", "top"], "sources": ["C:/Users/<USER>/Documents/augment-projects/smartcoverage/node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js"], "sourcesContent": ["import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,4BAA4B;AAC9D,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,SAASC,KAAK,QAAQ,kBAAkB;AAExC,SAASC,eAAeA,CAACC,OAAO,EAAE;EAChC,IAAIC,IAAI,GAAGD,OAAO,CAACT,qBAAqB,CAAC,CAAC;EAC1C,IAAIW,MAAM,GAAGJ,KAAK,CAACG,IAAI,CAACE,KAAK,CAAC,GAAGH,OAAO,CAACI,WAAW,IAAI,CAAC;EACzD,IAAIC,MAAM,GAAGP,KAAK,CAACG,IAAI,CAACK,MAAM,CAAC,GAAGN,OAAO,CAACO,YAAY,IAAI,CAAC;EAC3D,OAAOL,MAAM,KAAK,CAAC,IAAIG,MAAM,KAAK,CAAC;AACrC,CAAC,CAAC;AACF;;AAGA,eAAe,SAASG,gBAAgBA,CAACC,uBAAuB,EAAEC,YAAY,EAAEC,OAAO,EAAE;EACvF,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,KAAK;EACjB;EAEA,IAAIC,uBAAuB,GAAGlB,aAAa,CAACgB,YAAY,CAAC;EACzD,IAAIG,oBAAoB,GAAGnB,aAAa,CAACgB,YAAY,CAAC,IAAIX,eAAe,CAACW,YAAY,CAAC;EACvF,IAAII,eAAe,GAAGlB,kBAAkB,CAACc,YAAY,CAAC;EACtD,IAAIT,IAAI,GAAGV,qBAAqB,CAACkB,uBAAuB,EAAEI,oBAAoB,EAAEF,OAAO,CAAC;EACxF,IAAII,MAAM,GAAG;IACXC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE;EACb,CAAC;EACD,IAAIC,OAAO,GAAG;IACZC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC;EAED,IAAIR,uBAAuB,IAAI,CAACA,uBAAuB,IAAI,CAACD,OAAO,EAAE;IACnE,IAAIlB,WAAW,CAACiB,YAAY,CAAC,KAAK,MAAM;IAAI;IAC5Cb,cAAc,CAACiB,eAAe,CAAC,EAAE;MAC/BC,MAAM,GAAGvB,aAAa,CAACkB,YAAY,CAAC;IACtC;IAEA,IAAIhB,aAAa,CAACgB,YAAY,CAAC,EAAE;MAC/BQ,OAAO,GAAG3B,qBAAqB,CAACmB,YAAY,EAAE,IAAI,CAAC;MACnDQ,OAAO,CAACC,CAAC,IAAIT,YAAY,CAACW,UAAU;MACpCH,OAAO,CAACE,CAAC,IAAIV,YAAY,CAACY,SAAS;IACrC,CAAC,MAAM,IAAIR,eAAe,EAAE;MAC1BI,OAAO,CAACC,CAAC,GAAGxB,mBAAmB,CAACmB,eAAe,CAAC;IAClD;EACF;EAEA,OAAO;IACLK,CAAC,EAAElB,IAAI,CAACsB,IAAI,GAAGR,MAAM,CAACC,UAAU,GAAGE,OAAO,CAACC,CAAC;IAC5CC,CAAC,EAAEnB,IAAI,CAACuB,GAAG,GAAGT,MAAM,CAACE,SAAS,GAAGC,OAAO,CAACE,CAAC;IAC1CjB,KAAK,EAAEF,IAAI,CAACE,KAAK;IACjBG,MAAM,EAAEL,IAAI,CAACK;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}