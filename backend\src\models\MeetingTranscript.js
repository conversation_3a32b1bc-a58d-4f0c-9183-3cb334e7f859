const mongoose = require('mongoose');

const speakerSegmentSchema = new mongoose.Schema({
  speaker: {
    type: String,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
  timestamp: {
    type: Number, // seconds from start
    required: true,
  },
  confidence: {
    type: Number,
    min: 0,
    max: 1,
    default: 1.0,
  },
  sentiment: {
    type: String,
    enum: ['positive', 'negative', 'neutral'],
    default: 'neutral',
  },
  sentiment_score: {
    type: Number,
    min: -1,
    max: 1,
    default: 0,
  },
});

const meetingTranscriptSchema = new mongoose.Schema({
  meeting_id: {
    type: Number,
    required: true,
    unique: true,
    index: true,
  },
  raw_transcript: {
    type: String,
    required: true,
  },
  processed_transcript: {
    type: String,
  },
  speaker_segments: [speakerSegmentSchema],
  audio_metadata: {
    duration_seconds: Number,
    file_size_bytes: Number,
    format: String,
    sample_rate: Number,
    channels: Number,
  },
  processing_metadata: {
    transcription_service: {
      type: String,
      default: 'openai-whisper',
    },
    processing_time_ms: Number,
    language_detected: String,
    confidence_average: Number,
    word_count: Number,
    speaker_count: Number,
  },
  entities_extracted: [{
    entity: String,
    type: String, // PERSON, ORG, MONEY, etc.
    start_pos: Number,
    end_pos: Number,
    confidence: Number,
  }],
  key_phrases: [{
    phrase: String,
    relevance_score: Number,
    frequency: Number,
  }],
  topics_discussed: [{
    topic: String,
    confidence: Number,
    keywords: [String],
    segments: [Number], // indices of speaker segments
  }],
  action_items: [{
    item: String,
    assigned_to: String,
    due_date: Date,
    priority: {
      type: String,
      enum: ['high', 'medium', 'low'],
      default: 'medium',
    },
    status: {
      type: String,
      enum: ['pending', 'in_progress', 'completed'],
      default: 'pending',
    },
  }],
  decisions_made: [{
    decision: String,
    context: String,
    participants: [String],
    timestamp: Number,
  }],
  questions_raised: [{
    question: String,
    asked_by: String,
    answered: Boolean,
    answer: String,
    timestamp: Number,
  }],
  meeting_quality_metrics: {
    engagement_score: {
      type: Number,
      min: 0,
      max: 10,
    },
    participation_balance: Number, // 0-1, higher means more balanced
    interruption_count: Number,
    silence_percentage: Number,
    speaking_time_distribution: Map, // speaker -> percentage
  },
  embeddings: {
    full_transcript_embedding: [Number],
    summary_embedding: [Number],
    key_topics_embedding: [Number],
  },
  processing_status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending',
  },
  error_logs: [{
    timestamp: {
      type: Date,
      default: Date.now,
    },
    error_type: String,
    error_message: String,
    stack_trace: String,
  }],
}, {
  timestamps: true,
  collection: 'meeting_transcripts',
});

// Indexes for better query performance
meetingTranscriptSchema.index({ meeting_id: 1 });
meetingTranscriptSchema.index({ 'processing_metadata.language_detected': 1 });
meetingTranscriptSchema.index({ 'topics_discussed.topic': 1 });
meetingTranscriptSchema.index({ processing_status: 1 });
meetingTranscriptSchema.index({ createdAt: -1 });

// Instance methods
meetingTranscriptSchema.methods.getSpeakerStats = function() {
  const stats = {};
  let totalWords = 0;
  
  this.speaker_segments.forEach(segment => {
    const wordCount = segment.text.split(' ').length;
    totalWords += wordCount;
    
    if (!stats[segment.speaker]) {
      stats[segment.speaker] = {
        segments: 0,
        words: 0,
        speaking_time: 0,
        avg_sentiment: 0,
      };
    }
    
    stats[segment.speaker].segments += 1;
    stats[segment.speaker].words += wordCount;
    stats[segment.speaker].avg_sentiment += segment.sentiment_score;
  });
  
  // Calculate percentages and averages
  Object.keys(stats).forEach(speaker => {
    stats[speaker].word_percentage = (stats[speaker].words / totalWords) * 100;
    stats[speaker].avg_sentiment = stats[speaker].avg_sentiment / stats[speaker].segments;
  });
  
  return stats;
};

meetingTranscriptSchema.methods.extractKeyMoments = function() {
  return this.speaker_segments
    .filter(segment => 
      segment.sentiment_score > 0.5 || 
      segment.sentiment_score < -0.5 ||
      segment.text.toLowerCase().includes('decision') ||
      segment.text.toLowerCase().includes('action')
    )
    .sort((a, b) => Math.abs(b.sentiment_score) - Math.abs(a.sentiment_score))
    .slice(0, 10);
};

// Static methods
meetingTranscriptSchema.statics.findByDateRange = function(startDate, endDate) {
  return this.find({
    createdAt: {
      $gte: startDate,
      $lte: endDate,
    },
    processing_status: 'completed',
  }).sort({ createdAt: -1 });
};

meetingTranscriptSchema.statics.findByTopic = function(topic) {
  return this.find({
    'topics_discussed.topic': new RegExp(topic, 'i'),
    processing_status: 'completed',
  }).sort({ createdAt: -1 });
};

meetingTranscriptSchema.statics.getProcessingStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$processing_status',
        count: { $sum: 1 },
        avg_processing_time: { $avg: '$processing_metadata.processing_time_ms' },
      },
    },
  ]);
};

const MeetingTranscript = mongoose.model('MeetingTranscript', meetingTranscriptSchema);

module.exports = MeetingTranscript;
