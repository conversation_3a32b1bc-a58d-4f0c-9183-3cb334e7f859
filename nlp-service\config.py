import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base paths
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
MODELS_DIR = BASE_DIR / "models"
LOGS_DIR = BASE_DIR / "logs"

# Create directories if they don't exist
for dir_path in [DATA_DIR, MODELS_DIR, LOGS_DIR]:
    dir_path.mkdir(exist_ok=True)

# API Configuration
API_HOST = os.getenv("NLP_API_HOST", "localhost")
API_PORT = int(os.getenv("NLP_API_PORT", "8000"))

# Database Configuration
POSTGRES_URL = os.getenv("POSTGRES_URL", "postgresql://smartcoverage_user:smartcoverage_password@localhost:5432/smartcoverage")
MONGODB_URL = os.getenv("MONGODB_URL", "mongodb://localhost:27017/smartcoverage")
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

# Model Configuration
MODELS_CONFIG = {
    "sentiment_model": {
        "name": "cardiffnlp/twitter-roberta-base-sentiment-latest",
        "local_path": MODELS_DIR / "sentiment_model",
        "type": "classification"
    },
    "embedding_model": {
        "name": "sentence-transformers/all-MiniLM-L6-v2",
        "local_path": MODELS_DIR / "embedding_model",
        "type": "embedding"
    },
    "ner_model": {
        "name": "dbmdz/bert-large-cased-finetuned-conll03-english",
        "local_path": MODELS_DIR / "ner_model",
        "type": "ner"
    },
    "summarization_model": {
        "name": "facebook/bart-large-cnn",
        "local_path": MODELS_DIR / "summarization_model",
        "type": "summarization"
    },
    "topic_model": {
        "name": "custom_lda",
        "local_path": MODELS_DIR / "topic_model",
        "type": "topic_modeling"
    }
}

# Training Configuration
TRAINING_CONFIG = {
    "batch_size": 16,
    "learning_rate": 2e-5,
    "num_epochs": 3,
    "max_length": 512,
    "validation_split": 0.2,
    "random_seed": 42,
    "save_steps": 500,
    "eval_steps": 100,
    "warmup_steps": 100,
    "weight_decay": 0.01
}

# NLP Processing Configuration
NLP_CONFIG = {
    "max_text_length": 10000,
    "min_confidence_threshold": 0.7,
    "max_tags_per_text": 20,
    "chunk_size": 1000,
    "overlap_size": 100,
    "language": "en"
}

# Financial Keywords and Categories
FINANCIAL_KEYWORDS = {
    "investment_terms": [
        "investment", "portfolio", "roi", "return", "profit", "revenue", "capital",
        "funding", "valuation", "equity", "debt", "dividend", "yield", "risk",
        "hedge", "derivative", "asset", "liability", "cash flow", "ebitda"
    ],
    "crypto_terms": [
        "blockchain", "cryptocurrency", "bitcoin", "ethereum", "defi", "nft",
        "smart contract", "token", "mining", "staking", "wallet", "exchange"
    ],
    "market_terms": [
        "bull market", "bear market", "volatility", "liquidity", "market cap",
        "ipo", "merger", "acquisition", "stocks", "bonds", "commodities"
    ],
    "business_terms": [
        "revenue", "growth", "expansion", "strategy", "competition", "market share",
        "customer acquisition", "retention", "churn", "scalability", "innovation"
    ]
}

INDUSTRY_CATEGORIES = {
    "technology": [
        "ai", "artificial intelligence", "machine learning", "software", "saas",
        "cloud", "data", "analytics", "automation", "digital transformation"
    ],
    "healthcare": [
        "medical", "pharmaceutical", "biotech", "clinical", "drug", "therapy",
        "healthcare", "telemedicine", "medical device", "diagnostics"
    ],
    "finance": [
        "banking", "fintech", "insurance", "lending", "payment", "trading",
        "wealth management", "credit", "mortgage", "financial services"
    ],
    "energy": [
        "renewable", "solar", "wind", "oil", "gas", "nuclear", "battery",
        "energy storage", "grid", "sustainability", "carbon"
    ],
    "real_estate": [
        "property", "commercial", "residential", "reit", "development",
        "construction", "real estate", "housing", "office space"
    ],
    "retail": [
        "ecommerce", "consumer", "brand", "marketplace", "supply chain",
        "retail", "shopping", "omnichannel", "customer experience"
    ]
}

# Logging Configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.StreamHandler",
        },
        "file": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.FileHandler",
            "filename": LOGS_DIR / "nlp_service.log",
            "mode": "a",
        },
    },
    "loggers": {
        "": {
            "handlers": ["default", "file"],
            "level": "INFO",
            "propagate": False
        }
    }
}
