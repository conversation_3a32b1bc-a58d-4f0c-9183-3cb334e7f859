# SmartConverge Quick Start Guide

## 🚀 Get Started in 5 Minutes

This guide will get SmartConverge up and running quickly for development and testing.

## 📋 Prerequisites Check

Before starting, ensure you have:

- **Node.js 18+**: `node --version`
- **Python 3.8+**: `python3 --version`
- **PostgreSQL 13+**: `psql --version`
- **MongoDB 5.0+**: `mongod --version`
- **Redis 6.0+**: `redis-server --version`
- **Git**: `git --version`

## ⚡ Quick Installation

### 1. <PERSON>lone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd smartcoverage

# Run automated setup
./scripts/setup.sh
```

### 2. Install AI Models
```bash
# Download and install deep learning models
python3 scripts/install-models.py
```

### 3. Start Databases
```bash
# Start PostgreSQL (Ubuntu/Debian)
sudo systemctl start postgresql

# Start MongoDB
sudo systemctl start mongod

# Start Redis
sudo systemctl start redis-server

# Or on macOS with Homebrew:
# brew services start postgresql
# brew services start mongodb-community
# brew services start redis
```

### 4. Initialize Database
```bash
cd backend
npm run migrate
npm run seed
cd ..
```

### 5. Start All Services
```bash
# Start everything with one command
./scripts/start-all.sh
```

## 🌐 Access the Application

Once all services are running:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **NLP Service**: http://localhost:8000
- **API Documentation**: http://localhost:3001/api-docs
- **NLP API Docs**: http://localhost:8000/docs

## 🔐 Login Credentials

### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Demo Accounts
- **Analyst**: `<EMAIL>` / `password123`
- **Manager**: `<EMAIL>` / `password123`

## 🧪 Test the System

### 1. Upload a Meeting
1. Login to the frontend
2. Navigate to "Meetings" → "Add Meeting"
3. Upload a text file or paste meeting content
4. Watch real-time NLP processing

### 2. Try Semantic Search
1. Go to "Analytics" → "Search"
2. Ask questions like:
   - "What did we discuss about blockchain?"
   - "Show me meetings about investment opportunities"
   - "Find discussions about AI technology"

### 3. View Analytics
1. Check the "Dashboard" for overview metrics
2. Explore "Analytics" for detailed insights
3. Generate reports in "Reports" section

## 🔧 Development Commands

### Individual Services
```bash
# Backend only
cd backend && npm run dev

# Frontend only  
cd frontend && npm start

# NLP Service only
cd nlp-service && python start.py
```

### Database Operations
```bash
# Reset database
cd backend && npm run db:reset

# Re-seed data
cd backend && npm run seed

# View logs
npm run logs
```

### Stop Services
```bash
# Stop all services
./scripts/stop-all.sh

# Or use Ctrl+C in the terminal running start-all.sh
```

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using the port
lsof -i :3000  # or :3001, :8000

# Kill the process
kill -9 <PID>
```

#### Database Connection Issues
```bash
# Check if databases are running
sudo systemctl status postgresql
sudo systemctl status mongod
sudo systemctl status redis

# Restart if needed
sudo systemctl restart postgresql
```

#### Python Dependencies Issues
```bash
# Reinstall Python dependencies
cd nlp-service
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### Node.js Dependencies Issues
```bash
# Clean and reinstall
npm run clean
npm run install:all
```

### Model Download Issues
```bash
# Manually download models
cd nlp-service
python3 -c "
from transformers import AutoTokenizer, AutoModel
AutoTokenizer.from_pretrained('cardiffnlp/twitter-roberta-base-sentiment-latest')
AutoModel.from_pretrained('cardiffnlp/twitter-roberta-base-sentiment-latest')
"
```

### Memory Issues
If you encounter memory issues:
1. Close other applications
2. Reduce NLP service workers in `.env`:
   ```
   NLP_API_WORKERS=1
   ```
3. Use CPU-only models if no GPU available

## 📊 System Status

Check if everything is working:
```bash
# Health check all services
npm run health

# Check individual services
curl http://localhost:3001/health
curl http://localhost:8000/health
```

## 🔄 Next Steps

After getting the system running:

1. **Explore Features**: Try all the NLP capabilities
2. **Add Real Data**: Upload your own meeting transcripts
3. **Customize Models**: Train models on your domain-specific data
4. **Configure Settings**: Adjust NLP parameters in settings
5. **Set Up Production**: Follow the deployment guide for production setup

## 📚 Additional Resources

- **Full Documentation**: See `README.md`
- **API Documentation**: http://localhost:3001/api-docs
- **Deployment Guide**: See `DEPLOYMENT.md`
- **Architecture Details**: Check the codebase structure

## 🆘 Getting Help

If you encounter issues:

1. Check the logs: `npm run logs`
2. Verify all services are running: `npm run health`
3. Review the troubleshooting section above
4. Check the GitHub issues page
5. Ensure all prerequisites are properly installed

## 🎯 Key Features to Try

### Real-Time NLP Processing
- Upload meeting content and see instant analysis
- Watch sentiment analysis in real-time
- See tags being extracted automatically

### Semantic Search
- Ask natural language questions about meetings
- Search for specific topics or themes
- Find relevant content across all meetings

### Analytics Dashboard
- View meeting trends and patterns
- Analyze client engagement metrics
- Track discussion topics over time

### Custom Model Training
- Train sentiment models on your data
- Fine-tune classification models
- Evaluate model performance

---

**🎉 Congratulations!** You now have SmartConverge running with advanced AI-powered meeting analysis capabilities. Start exploring the features and see how AI can transform your meeting insights!
