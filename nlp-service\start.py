#!/usr/bin/env python3
"""
SmartConverge NLP Service Startup Script
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
import uvicorn
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

# Load environment variables from root .env file
load_dotenv(dotenv_path='../.env')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/nlp_service.log')
    ]
)

logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'torch', 'transformers', 'sentence-transformers', 'fastapi',
        'uvicorn', 'numpy', 'pandas', 'scikit-learn', 'faiss-cpu'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.error("Please install them using: pip install -r requirements.txt")
        return False

    return True

def check_gpu_availability():
    """Check GPU availability for deep learning models"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            logger.info(f"✅ GPU available: {gpu_name} (Count: {gpu_count})")
            return True
        else:
            logger.warning("⚠️  No GPU available. Models will run on CPU (slower performance)")
            return False
    except ImportError:
        logger.warning("⚠️  PyTorch not available")
        return False

def setup_directories():
    """Create necessary directories"""
    directories = [
        'data',
        'models',
        'logs',
        'data/faiss_index',
        'models/trained',
        'models/training_plots'
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ Directory created/verified: {directory}")

async def download_models():
    """Download required models if not present"""
    from models.real_time_processor import RealTimeNLPProcessor
    from services.semantic_search import SemanticSearchEngine

    logger.info("🔄 Initializing and downloading models...")

    try:
        # Initialize NLP processor (this will download models)
        processor = RealTimeNLPProcessor({})
        await processor.initialize()

        # Initialize search engine
        search_engine = SemanticSearchEngine({
            'index_path': 'data/faiss_index',
            'metadata_path': 'data/metadata.pkl',
            'db_path': 'data/search_db.sqlite'
        })
        await search_engine.initialize()

        logger.info("✅ All models initialized successfully")

        # Cleanup
        processor.shutdown()

    except Exception as e:
        logger.error(f"❌ Model initialization failed: {e}")
        raise

def main():
    """Main startup function"""
    logger.info("🚀 Starting SmartConverge NLP Service...")

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Check GPU
    check_gpu_availability()

    # Setup directories
    setup_directories()

    # Download models
    try:
        asyncio.run(download_models())
    except Exception as e:
        logger.error(f"❌ Failed to initialize models: {e}")
        logger.info("🔄 Continuing with basic initialization...")

    # Get configuration
    host = os.getenv('NLP_API_HOST', 'localhost')
    port = int(os.getenv('NLP_API_PORT', 8000))
    reload = os.getenv('NLP_API_RELOAD', 'true').lower() == 'true'
    workers = int(os.getenv('NLP_API_WORKERS', 1))

    logger.info(f"🌐 Starting server on {host}:{port}")
    logger.info(f"🔧 Configuration: reload={reload}, workers={workers}")

    # Start the server
    try:
        uvicorn.run(
            "api.main:app",
            host=host,
            port=port,
            reload=reload,
            workers=workers if not reload else 1,  # Reload doesn't work with multiple workers
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server failed to start: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
