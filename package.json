{"name": "smartcoverage", "version": "1.0.0", "description": "SmartConverge - Intelligent Meeting Analysis System with Advanced NLP", "main": "index.js", "scripts": {"setup": "./scripts/setup.sh", "start": "./scripts/start-all.sh", "stop": "./scripts/stop-all.sh", "dev": "./scripts/start-all.sh", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "install:all": "npm run install:backend && npm run install:frontend && npm run install:nlp", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "install:nlp": "cd nlp-service && pip install -r requirements.txt", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "migrate": "cd backend && npm run migrate", "seed": "cd backend && npm run seed", "db:reset": "cd backend && npm run db:reset", "logs": "tail -f backend/logs/app.log nlp-service/logs/nlp_service.log", "health": "curl -s http://localhost:3001/health && curl -s http://localhost:8000/health", "clean": "npm run clean:backend && npm run clean:frontend && npm run clean:nlp", "clean:backend": "cd backend && rm -rf node_modules package-lock.json", "clean:frontend": "cd frontend && rm -rf node_modules package-lock.json build", "clean:nlp": "cd nlp-service && rm -rf __pycache__ .pytest_cache models/trained data/faiss_index"}, "keywords": ["nlp", "meeting-analysis", "ai", "machine-learning", "deep-learning", "sentiment-analysis", "semantic-search", "business-intelligence", "react", "nodejs", "python", "<PERSON><PERSON><PERSON>", "transformers", "bert", "faiss"], "author": "SmartConverge Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/smartcoverage.git"}, "bugs": {"url": "https://github.com/your-org/smartcoverage/issues"}, "homepage": "https://github.com/your-org/smartcoverage#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0", "python": ">=3.8.0"}, "workspaces": ["backend", "frontend"], "devDependencies": {"concurrently": "^8.2.0", "cross-env": "^7.0.3"}, "dependencies": {}, "config": {"ports": {"frontend": 3000, "backend": 3001, "nlp": 8000, "postgres": 5432, "mongodb": 27017, "redis": 6379}}}