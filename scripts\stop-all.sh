#!/bin/bash

# SmartConverge - Stop All Services Script
# This script stops all running SmartConverge services

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to stop service by PID file
stop_service_by_pid() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        print_status "Stopping $service_name (PID: $pid)..."
        
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            
            # Wait for process to stop
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "Force killing $service_name..."
                kill -9 "$pid" 2>/dev/null || true
            fi
            
            print_status "$service_name stopped successfully"
        else
            print_warning "$service_name was not running"
        fi
        
        rm "$pid_file"
    else
        print_warning "No PID file found for $service_name"
    fi
}

# Function to stop service by port
stop_service_by_port() {
    local service_name=$1
    local port=$2
    
    print_status "Checking for $service_name on port $port..."
    
    # Find process using the port
    local pid=$(lsof -ti:$port 2>/dev/null)
    
    if [ -n "$pid" ]; then
        print_status "Found $service_name running on port $port (PID: $pid)"
        kill "$pid" 2>/dev/null || true
        
        # Wait for process to stop
        local count=0
        while lsof -ti:$port >/dev/null 2>&1 && [ $count -lt 10 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        if lsof -ti:$port >/dev/null 2>&1; then
            print_warning "Force killing $service_name..."
            kill -9 "$pid" 2>/dev/null || true
        fi
        
        print_status "$service_name stopped successfully"
    else
        print_status "$service_name is not running on port $port"
    fi
}

# Main stop function
main() {
    print_header "🛑 Stopping SmartConverge Services"
    echo "===================================="
    
    # Stop services using PID files first
    stop_service_by_pid "Frontend" ".frontend.pid"
    stop_service_by_pid "Backend API" ".backend.pid"
    stop_service_by_pid "NLP Service" ".nlp_service.pid"
    
    # Stop any remaining services by port
    print_header "Checking for remaining services..."
    stop_service_by_port "Frontend" 3000
    stop_service_by_port "Backend API" 3001
    stop_service_by_port "NLP Service" 8000
    
    # Clean up any remaining PID files
    print_header "Cleaning up..."
    rm -f .frontend.pid .backend.pid .nlp_service.pid
    
    # Stop any Node.js processes that might be related
    print_status "Stopping any remaining Node.js processes..."
    pkill -f "npm.*start" 2>/dev/null || true
    pkill -f "npm.*dev" 2>/dev/null || true
    pkill -f "react-scripts" 2>/dev/null || true
    pkill -f "nodemon" 2>/dev/null || true
    
    # Stop Python processes
    print_status "Stopping any remaining Python NLP processes..."
    pkill -f "start.py" 2>/dev/null || true
    pkill -f "uvicorn.*main:app" 2>/dev/null || true
    
    print_header "✅ All SmartConverge services stopped"
    print_status "All services have been shut down successfully"
}

# Check if running from correct directory
if [ ! -d "backend" ] && [ ! -d "frontend" ] && [ ! -d "nlp-service" ]; then
    print_error "Please run this script from the SmartConverge root directory"
    exit 1
fi

# Run main function
main "$@"
