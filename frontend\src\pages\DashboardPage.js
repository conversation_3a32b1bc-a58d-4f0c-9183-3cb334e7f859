import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import AdminDashboard from '../components/dashboards/AdminDashboard';
import ManagerDashboard from '../components/dashboards/ManagerDashboard';
import ClientDashboard from '../components/dashboards/ClientDashboard';
import { Box, Alert, CircularProgress } from '@mui/material';

const DashboardPage = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!user) {
    return (
      <Box p={3}>
        <Alert severity="error">
          User not authenticated. Please log in again.
        </Alert>
      </Box>
    );
  }

  // Route to appropriate dashboard based on user role
  switch (user.role) {
    case 'admin':
      return <AdminDashboard />;
    case 'manager':
      return <ManagerDashboard />;
    case 'client':
      return <ClientDashboard />;
    default:
      return (
        <Box p={3}>
          <Alert severity="warning">
            Unknown user role: {user.role}. Please contact support.
          </Alert>
        </Box>
      );
  }
};

export default DashboardPage;
