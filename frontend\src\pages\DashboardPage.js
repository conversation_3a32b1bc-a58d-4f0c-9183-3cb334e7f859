import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  Avatar,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Event,
  People,
  Tag,
  AttachMoney,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { format } from 'date-fns';

import { analyticsAPI } from '../services/api';
import LoadingSpinner from '../components/Common/LoadingSpinner';

const DashboardPage = () => {
  const [period, setPeriod] = useState('month');

  const { data: dashboardData, isLoading, error } = useQuery(
    ['dashboard', period],
    () => analyticsAPI.getDashboard({ period }),
    {
      select: (response) => response.data.data,
    }
  );

  const handlePeriodChange = (event) => {
    setPeriod(event.target.value);
  };

  if (isLoading) {
    return <LoadingSpinner message="Loading dashboard..." />;
  }

  if (error) {
    return (
      <Box p={3}>
        <Typography color="error">
          Failed to load dashboard data. Please try again.
        </Typography>
      </Box>
    );
  }

  const metrics = dashboardData?.metrics || {};
  const recentMeetings = dashboardData?.recent_meetings || [];
  const topTags = dashboardData?.top_tags || [];
  const clientDistribution = dashboardData?.client_distribution || [];
  const sentimentDistribution = dashboardData?.sentiment_distribution || {};
  const investmentMetrics = dashboardData?.investment_metrics || {};

  // Calculate sentiment percentage
  const totalSentimentCount = sentimentDistribution.positive + sentimentDistribution.negative + sentimentDistribution.neutral;
  const sentimentPercentages = {
    positive: totalSentimentCount > 0 ? (sentimentDistribution.positive / totalSentimentCount) * 100 : 0,
    negative: totalSentimentCount > 0 ? (sentimentDistribution.negative / totalSentimentCount) * 100 : 0,
    neutral: totalSentimentCount > 0 ? (sentimentDistribution.neutral / totalSentimentCount) * 100 : 0,
  };

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Dashboard
        </Typography>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Period</InputLabel>
          <Select
            value={period}
            label="Period"
            onChange={handlePeriodChange}
          >
            <MenuItem value="week">Last Week</MenuItem>
            <MenuItem value="month">Last Month</MenuItem>
            <MenuItem value="quarter">Last Quarter</MenuItem>
            <MenuItem value="year">Last Year</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Grid container spacing={3}>
        {/* Key Metrics */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Total Meetings
                  </Typography>
                  <Typography variant="h4">
                    {metrics.total_meetings || 0}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <Event />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Active Clients
                  </Typography>
                  <Typography variant="h4">
                    {metrics.total_clients || 0}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'secondary.main' }}>
                  <People />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Tags Generated
                  </Typography>
                  <Typography variant="h4">
                    {metrics.total_tags || 0}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <Tag />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Investment Discussed
                  </Typography>
                  <Typography variant="h4">
                    ${(metrics.total_investment_discussed / 1000000).toFixed(1)}M
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Sentiment Analysis */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sentiment Analysis
              </Typography>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Positive</Typography>
                  <Typography variant="body2">{sentimentPercentages.positive.toFixed(1)}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={sentimentPercentages.positive}
                  color="success"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Neutral</Typography>
                  <Typography variant="body2">{sentimentPercentages.neutral.toFixed(1)}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={sentimentPercentages.neutral}
                  color="info"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              <Box>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Negative</Typography>
                  <Typography variant="body2">{sentimentPercentages.negative.toFixed(1)}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={sentimentPercentages.negative}
                  color="error"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Investment Metrics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Investment Metrics
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    Total Amount Discussed
                  </Typography>
                  <Typography variant="h6">
                    ${(investmentMetrics.total_amount / 1000000).toFixed(2)}M
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    Average per Meeting
                  </Typography>
                  <Typography variant="h6">
                    ${(investmentMetrics.average_amount / 1000).toFixed(0)}K
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    Meetings with Investment
                  </Typography>
                  <Typography variant="h6">
                    {investmentMetrics.meetings_with_investment || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Meetings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Meetings
              </Typography>
              <List dense>
                {recentMeetings.slice(0, 5).map((meeting) => (
                  <ListItem key={meeting.id} divider>
                    <ListItemText
                      primary={meeting.meeting_subject}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {meeting.client_name} • {format(new Date(meeting.meeting_date), 'MMM dd, yyyy')}
                          </Typography>
                          {meeting.sentiment_score !== null && (
                            <Chip
                              size="small"
                              label={
                                meeting.sentiment_score > 0.1 ? 'Positive' :
                                meeting.sentiment_score < -0.1 ? 'Negative' : 'Neutral'
                              }
                              color={
                                meeting.sentiment_score > 0.1 ? 'success' :
                                meeting.sentiment_score < -0.1 ? 'error' : 'default'
                              }
                              sx={{ mt: 0.5 }}
                            />
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Tags */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Discussion Topics
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {topTags.slice(0, 15).map((tag, index) => (
                  <Chip
                    key={index}
                    label={`${tag.name} (${tag.count})`}
                    size="small"
                    variant="outlined"
                    color={tag.avg_confidence > 0.8 ? 'primary' : 'default'}
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Client Distribution */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Client Distribution by Industry
              </Typography>
              <Grid container spacing={2}>
                {clientDistribution.map((item, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Box textAlign="center" p={2}>
                      <Typography variant="h4" color="primary">
                        {item.count}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {item.industry || 'Unknown'}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
