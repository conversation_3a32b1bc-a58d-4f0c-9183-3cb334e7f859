{
  service: 'smartcoverage-backend',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'NLP Service WebSocket error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1118:18)\n' +
    '    at afterConnectMultiple (node:net:1685:7)',
  timestamp: '2025-05-31 10:42:51'
}
{
  service: 'smartcoverage-backend',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'NLP Service WebSocket error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1118:18)\n' +
    '    at afterConnectMultiple (node:net:1685:7)',
  timestamp: '2025-05-31 10:44:26'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 10:44:39'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 10:44:39'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 10:44:39'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeConnectionError',
  parent: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  original: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'SequelizeConnectionError: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:149:20)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Client._handleAuthSASLContinue (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:284:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)',
  timestamp: '2025-05-31 10:44:39'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 10:44:53'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 10:44:53'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 10:44:53'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeConnectionError',
  parent: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  original: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'SequelizeConnectionError: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:149:20)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Client._handleAuthSASLContinue (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:284:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)',
  timestamp: '2025-05-31 10:44:53'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 11:02:06'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 11:02:06'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 11:02:06'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeHostNotFoundError',
  parent: Error: getaddrinfo ENOTFOUND postgres
      at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'postgres'
  },
  original: Error: getaddrinfo ENOTFOUND postgres
      at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'postgres'
  },
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: getaddrinfo ENOTFOUND postgres',
  stack: 'SequelizeHostNotFoundError: getaddrinfo ENOTFOUND postgres\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:136:24)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Socket.reportStreamError (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\connection.js:57:12)\n' +
    '    at Socket.emit (node:events:519:28)\n' +
    '    at emitErrorNT (node:internal/streams/destroy:169:8)\n' +
    '    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)',
  timestamp: '2025-05-31 11:02:09'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 12:54:18'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:54:18'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:54:18'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:54:18'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Meeting.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:34:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 12:54:19'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 12:57:04'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:57:04'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:57:04'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:57:04'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Meeting.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:34:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 12:57:04'
}
