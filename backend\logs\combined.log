{
  service: 'smartcoverage-backend',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'NLP Service WebSocket error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1118:18)\n' +
    '    at afterConnectMultiple (node:net:1685:7)',
  timestamp: '2025-05-31 10:42:51'
}
{
  service: 'smartcoverage-backend',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'NLP Service WebSocket error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1118:18)\n' +
    '    at afterConnectMultiple (node:net:1685:7)',
  timestamp: '2025-05-31 10:44:26'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 10:44:39'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 10:44:39'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 10:44:39'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeConnectionError',
  parent: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  original: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'SequelizeConnectionError: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:149:20)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Client._handleAuthSASLContinue (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:284:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)',
  timestamp: '2025-05-31 10:44:39'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 10:44:53'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 10:44:53'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 10:44:53'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeConnectionError',
  parent: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  original: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'SequelizeConnectionError: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:149:20)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Client._handleAuthSASLContinue (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:284:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)',
  timestamp: '2025-05-31 10:44:53'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 11:02:06'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 11:02:06'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 11:02:06'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeHostNotFoundError',
  parent: Error: getaddrinfo ENOTFOUND postgres
      at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'postgres'
  },
  original: Error: getaddrinfo ENOTFOUND postgres
      at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'postgres'
  },
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: getaddrinfo ENOTFOUND postgres',
  stack: 'SequelizeHostNotFoundError: getaddrinfo ENOTFOUND postgres\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:136:24)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Socket.reportStreamError (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\connection.js:57:12)\n' +
    '    at Socket.emit (node:events:519:28)\n' +
    '    at emitErrorNT (node:internal/streams/destroy:169:8)\n' +
    '    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)',
  timestamp: '2025-05-31 11:02:09'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 12:54:18'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:54:18'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:54:18'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:54:18'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Meeting.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:34:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 12:54:19'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 12:57:04'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:57:04'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:57:04'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:57:04'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "meetings" ("id"  SERIAL , "meeting_subject" VARCHAR(255) NOT NULL, "meeting_notes" TEXT NOT NULL, "organizer_name" VARCHAR(255) NOT NULL, "organizer_department" VARCHAR(255), "client_name" VARCHAR(255) NOT NULL, "client_industry" VARCHAR(255), "meeting_date" TIMESTAMP WITH TIME ZONE NOT NULL, "manual_tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "attendees" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "key_decisions" TEXT, "investment_amount_discussed" DECIMAL(15,2), "roi_expectation" VARCHAR(255), "summary" TEXT, "sentiment_score" FLOAT, "client_satisfaction" FLOAT, "meeting_duration_minutes" INTEGER, "meeting_type" "public"."enum_meetings_meeting_type" DEFAULT 'virtual', "meeting_platform" "public"."enum_meetings_meeting_platform" NOT NULL DEFAULT 'zoom', "zoom_meeting_id" VARCHAR(255), "zoom_join_url" TEXT, "zoom_start_url" TEXT, "zoom_password" VARCHAR(255), "status" "public"."enum_meetings_status" NOT NULL DEFAULT 'scheduled', "is_billable" BOOLEAN NOT NULL DEFAULT true, "billing_amount" DECIMAL(10,2), "created_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Meeting.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:34:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 12:57:04'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 12:58:23'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:58:23'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:58:23'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:58:23'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "tags" ("id"  SERIAL , "meeting_id" INTEGER NOT NULL REFERENCES "meetings" ("id") ON DELETE CASCADE ON UPDATE CASCADE, "tag_name" VARCHAR(255) NOT NULL, "tag_type" "public"."enum_tags_tag_type" NOT NULL DEFAULT 'explicit', "confidence_score" FLOAT NOT NULL DEFAULT '1', "category" VARCHAR(255), "source" "public"."enum_tags_source" NOT NULL DEFAULT 'nlp_model', "context" TEXT , "relevance_score" FLOAT, "frequency" INTEGER NOT NULL DEFAULT 1, "is_validated" BOOLEAN NOT NULL DEFAULT false, "validated_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "validation_date" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id")); COMMENT ON COLUMN "tags"."context" IS 'Context where the tag was extracted from';`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "tags" ("id"  SERIAL , "meeting_id" INTEGER NOT NULL REFERENCES "meetings" ("id") ON DELETE CASCADE ON UPDATE CASCADE, "tag_name" VARCHAR(255) NOT NULL, "tag_type" "public"."enum_tags_tag_type" NOT NULL DEFAULT 'explicit', "confidence_score" FLOAT NOT NULL DEFAULT '1', "category" VARCHAR(255), "source" "public"."enum_tags_source" NOT NULL DEFAULT 'nlp_model', "context" TEXT , "relevance_score" FLOAT, "frequency" INTEGER NOT NULL DEFAULT 1, "is_validated" BOOLEAN NOT NULL DEFAULT false, "validated_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "validation_date" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id")); COMMENT ON COLUMN "tags"."context" IS 'Context where the tag was extracted from';`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "tags" ("id"  SERIAL , "meeting_id" INTEGER NOT NULL REFERENCES "meetings" ("id") ON DELETE CASCADE ON UPDATE CASCADE, "tag_name" VARCHAR(255) NOT NULL, "tag_type" "public"."enum_tags_tag_type" NOT NULL DEFAULT 'explicit', "confidence_score" FLOAT NOT NULL DEFAULT '1', "category" VARCHAR(255), "source" "public"."enum_tags_source" NOT NULL DEFAULT 'nlp_model', "context" TEXT , "relevance_score" FLOAT, "frequency" INTEGER NOT NULL DEFAULT 1, "is_validated" BOOLEAN NOT NULL DEFAULT false, "validated_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "validation_date" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id")); COMMENT ON COLUMN "tags"."context" IS 'Context where the tag was extracted from';`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Tag.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:34:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 12:58:24'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 12:58:55'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:58:55'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:58:55'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:58:55'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "tags" ("id"  SERIAL , "meeting_id" INTEGER NOT NULL REFERENCES "meetings" ("id") ON DELETE CASCADE ON UPDATE CASCADE, "tag_name" VARCHAR(255) NOT NULL, "tag_type" "public"."enum_tags_tag_type" NOT NULL DEFAULT 'explicit', "confidence_score" FLOAT NOT NULL DEFAULT '1', "category" VARCHAR(255), "source" "public"."enum_tags_source" NOT NULL DEFAULT 'nlp_model', "context" TEXT , "relevance_score" FLOAT, "frequency" INTEGER NOT NULL DEFAULT 1, "is_validated" BOOLEAN NOT NULL DEFAULT false, "validated_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "validation_date" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id")); COMMENT ON COLUMN "tags"."context" IS 'Context where the tag was extracted from';`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "tags" ("id"  SERIAL , "meeting_id" INTEGER NOT NULL REFERENCES "meetings" ("id") ON DELETE CASCADE ON UPDATE CASCADE, "tag_name" VARCHAR(255) NOT NULL, "tag_type" "public"."enum_tags_tag_type" NOT NULL DEFAULT 'explicit', "confidence_score" FLOAT NOT NULL DEFAULT '1', "category" VARCHAR(255), "source" "public"."enum_tags_source" NOT NULL DEFAULT 'nlp_model', "context" TEXT , "relevance_score" FLOAT, "frequency" INTEGER NOT NULL DEFAULT 1, "is_validated" BOOLEAN NOT NULL DEFAULT false, "validated_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "validation_date" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id")); COMMENT ON COLUMN "tags"."context" IS 'Context where the tag was extracted from';`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "tags" ("id"  SERIAL , "meeting_id" INTEGER NOT NULL REFERENCES "meetings" ("id") ON DELETE CASCADE ON UPDATE CASCADE, "tag_name" VARCHAR(255) NOT NULL, "tag_type" "public"."enum_tags_tag_type" NOT NULL DEFAULT 'explicit', "confidence_score" FLOAT NOT NULL DEFAULT '1', "category" VARCHAR(255), "source" "public"."enum_tags_source" NOT NULL DEFAULT 'nlp_model', "context" TEXT , "relevance_score" FLOAT, "frequency" INTEGER NOT NULL DEFAULT 1, "is_validated" BOOLEAN NOT NULL DEFAULT false, "validated_by" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "validation_date" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id")); COMMENT ON COLUMN "tags"."context" IS 'Context where the tag was extracted from';`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Tag.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:34:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 12:58:55'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 12:59:07'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:59:07'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:59:07'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:59:07'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Client.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:34:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 12:59:08'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 12:59:56'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:59:56'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 12:59:56'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeConnectionError',
  parent: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  original: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'SequelizeConnectionError: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:149:20)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Client._handleAuthSASLContinue (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:284:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)',
  timestamp: '2025-05-31 12:59:56'
}
{
  message: 'NLP WebSocket connection disabled',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 13:00:25'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 13:00:26'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Client.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:34:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 13:00:26'
}
{
  message: 'NLP WebSocket connection disabled',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 13:00:48'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 13:00:48'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Client.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:34:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 13:00:49'
}
{
  message: 'NLP WebSocket connection disabled',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 13:01:00'
}
{
  service: 'smartcoverage-backend',
  host: 'localhost',
  port: '5432',
  database: 'smartcoverage',
  username: 'postgres',
  password_length: 4,
  level: 'info',
  message: '🔧 Database configuration:',
  timestamp: '2025-05-31 13:01:00'
}
{
  message: '✅ PostgreSQL connection established successfully',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 13:01:00'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeDatabaseError',
  parent: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  original: error: relation "Users" does not exist
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 209,
    severity: 'ERROR',
    code: '42P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\catalog\\namespace.c',
    line: '435',
    routine: 'RangeVarGetRelidExtended',
    sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
    parameters: undefined
  },
  sql: `CREATE TABLE IF NOT EXISTS "clients" ("id"  SERIAL , "name" VARCHAR(255) NOT NULL UNIQUE, "industry" VARCHAR(255), "contact_email" VARCHAR(255), "contact_phone" VARCHAR(255), "contact_person" VARCHAR(255), "address" TEXT, "billing_rate" DECIMAL(10,2) DEFAULT 150, "billing_currency" VARCHAR(3) NOT NULL DEFAULT 'USD', "payment_terms" VARCHAR(255) DEFAULT 'Net 30', "status" "public"."enum_clients_status" NOT NULL DEFAULT 'active', "tier" "public"."enum_clients_tier" NOT NULL DEFAULT 'standard', "onboarding_date" TIMESTAMP WITH TIME ZONE, "last_meeting_date" TIMESTAMP WITH TIME ZONE, "total_meetings" INTEGER NOT NULL DEFAULT 0, "total_billed_amount" DECIMAL(15,2) NOT NULL DEFAULT 0, "notes" TEXT, "preferences" JSONB DEFAULT '{}', "tags" VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[], "assigned_analyst" INTEGER REFERENCES "Users" ("id") ON DELETE SET NULL ON UPDATE CASCADE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, PRIMARY KEY ("id"));`,
  parameters: {},
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: relation "Users" does not exist',
  stack: 'Error\n' +
    '    at Query.run (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async PostgresQueryInterface.createTable (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n' +
    '    at async Client.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\model.js:942:7)\n' +
    '    at async Sequelize.sync (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n' +
    '    at async connectPostgreSQL (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\src\\config\\database.js:43:7)\n' +
    '    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\backend\\server.js:97:5)',
  timestamp: '2025-05-31 13:01:01'
}
{
  service: 'smartcoverage-backend',
  host: 'localhost',
  port: 5432,
  database: 'smartcoverage',
  username: 'smartcoverage_user',
  password_length: 22,
  level: 'info',
  message: '🔧 Database configuration:',
  timestamp: '2025-05-31 13:01:13'
}
{
  service: 'smartcoverage-backend',
  level: 'warn',
  message: 'NLP Service WebSocket error (service may not be running):',
  timestamp: '2025-05-31 13:01:13'
}
{
  message: '❌ NLP Service WebSocket connection closed',
  level: 'warn',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 13:01:13'
}
{
  message: 'Attempting to reconnect to NLP Service (1/3)...',
  level: 'info',
  service: 'smartcoverage-backend',
  timestamp: '2025-05-31 13:01:13'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeConnectionError',
  parent: error: password authentication failed for user "smartcoverage_user"
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 218,
    severity: 'FATAL',
    code: '28P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\libpq\\auth.c',
    line: '334',
    routine: 'auth_failed'
  },
  original: error: password authentication failed for user "smartcoverage_user"
      at Parser.parseErrorMessage (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:285:98)
      at Parser.handlePacket (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:122:29)
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:35:38)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5)
      at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
    length: 218,
    severity: 'FATAL',
    code: '28P01',
    detail: undefined,
    hint: undefined,
    position: undefined,
    internalPosition: undefined,
    internalQuery: undefined,
    where: undefined,
    schema: undefined,
    table: undefined,
    column: undefined,
    dataType: undefined,
    constraint: undefined,
    file: 'D:\\a\\postgresql-packaging-foundation\\postgresql-packaging-foundation\\postgresql-13.21\\src\\backend\\libpq\\auth.c',
    line: '334',
    routine: 'auth_failed'
  },
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: password authentication failed for user "smartcoverage_user"',
  stack: 'SequelizeConnectionError: password authentication failed for user "smartcoverage_user"\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:145:24)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorMessage (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:356:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\connection.js:116:12\n' +
    '    at Parser.parse (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg-protocol\\dist\\parser.js:36:17)\n' +
    '    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg-protocol\\dist\\index.js:11:42)\n' +
    '    at Socket.emit (node:events:519:28)\n' +
    '    at addChunk (node:internal/streams/readable:559:12)\n' +
    '    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)',
  timestamp: '2025-05-31 13:01:13'
}
