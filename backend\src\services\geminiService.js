const axios = require('axios');
const logger = require('../utils/logger');

class GeminiService {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY;
    this.model = process.env.GEMINI_MODEL || 'gemini-1.5-pro';
    this.endpoint = process.env.GEMINI_ENDPOINT || 'https://generativelanguage.googleapis.com/v1beta';
    this.baseURL = `${this.endpoint}/models/${this.model}:generateContent`;
  }

  // Make request to Gemini API
  async makeRequest(prompt, options = {}) {
    try {
      if (!this.apiKey) {
        throw new Error('Gemini API key not configured');
      }

      const requestBody = {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: options.temperature || 0.3,
          topK: options.topK || 40,
          topP: options.topP || 0.95,
          maxOutputTokens: options.maxTokens || 2048,
          stopSequences: options.stopSequences || []
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      };

      const response = await axios.post(`${this.baseURL}?key=${this.apiKey}`, requestBody, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.candidates && response.data.candidates.length > 0) {
        return response.data.candidates[0].content.parts[0].text;
      } else {
        throw new Error('No response generated from Gemini');
      }

    } catch (error) {
      logger.error('Gemini API request failed:', error.response?.data || error.message);
      throw new Error(`Gemini API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Extract implicit tags using Gemini
  async extractImplicitTags(text, meetingContext = {}) {
    try {
      const prompt = `
        Analyze the following business meeting text and extract implicit topics, themes, and concepts that are discussed but not explicitly mentioned.
        
        Meeting Context:
        - Client: ${meetingContext.client_name || 'Unknown'}
        - Industry: ${meetingContext.client_industry || 'Unknown'}
        - Meeting Type: ${meetingContext.meeting_type || 'business'}
        
        Focus on:
        - Business strategies and approaches
        - Market conditions and trends
        - Risk factors and concerns
        - Opportunities and potential outcomes
        - Stakeholder relationships
        - Industry-specific concepts
        - Investment themes and financial implications
        
        Return ONLY a valid JSON array of objects with the following structure:
        [{"name": "tag_name", "confidence": 0.8, "category": "strategy", "reasoning": "brief explanation"}]
        
        Text: ${text}
      `;

      const response = await this.makeRequest(prompt, {
        temperature: 0.3,
        maxTokens: 1000
      });

      // Parse JSON response
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        logger.warning('No valid JSON found in Gemini response');
        return [];
      }

      const implicitTags = JSON.parse(jsonMatch[0]);

      return implicitTags.map(tag => ({
        name: tag.name.toLowerCase(),
        type: 'implicit',
        confidence: tag.confidence || 0.7,
        source: 'gemini_ai',
        category: tag.category || 'general',
        reasoning: tag.reasoning,
        frequency: 1
      }));

    } catch (error) {
      logger.error('Error extracting implicit tags with Gemini:', error);
      return [];
    }
  }

  // Generate meeting summary using Gemini
  async generateSummary(text, meetingContext = {}) {
    try {
      const prompt = `
        Generate a comprehensive summary of this business meeting transcript.
        
        Meeting Context:
        - Client: ${meetingContext.client_name || 'Unknown'}
        - Industry: ${meetingContext.client_industry || 'Unknown'}
        - Meeting Type: ${meetingContext.meeting_type || 'business'}
        - Duration: ${meetingContext.duration || 'Unknown'} minutes
        
        Please provide:
        1. Executive Summary (2-3 sentences)
        2. Key Discussion Points (bullet points)
        3. Decisions Made
        4. Action Items
        5. Next Steps
        6. Important Mentions (people, companies, amounts)
        
        Keep the summary professional, concise, and actionable.
        
        Meeting Transcript:
        ${text}
      `;

      const summary = await this.makeRequest(prompt, {
        temperature: 0.2,
        maxTokens: 1500
      });

      return summary;

    } catch (error) {
      logger.error('Error generating summary with Gemini:', error);
      return 'Summary generation failed. Please try again.';
    }
  }

  // Analyze sentiment using Gemini
  async analyzeSentiment(text, meetingContext = {}) {
    try {
      const prompt = `
        Analyze the sentiment of this business meeting text. Consider the context and provide a detailed sentiment analysis.
        
        Meeting Context:
        - Client: ${meetingContext.client_name || 'Unknown'}
        - Industry: ${meetingContext.client_industry || 'Unknown'}
        
        Return ONLY a valid JSON object with this exact structure:
        {
          "overall": "positive|negative|neutral",
          "confidence": 0.85,
          "sentiment_score": 0.3,
          "scores": {
            "positive": 0.6,
            "negative": 0.1,
            "neutral": 0.3
          },
          "reasoning": "Brief explanation of the sentiment analysis",
          "key_indicators": ["positive phrase 1", "concern mentioned"]
        }
        
        Text: ${text}
      `;

      const response = await this.makeRequest(prompt, {
        temperature: 0.1,
        maxTokens: 500
      });

      // Parse JSON response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        logger.warning('No valid JSON found in Gemini sentiment response');
        return this.getFallbackSentiment();
      }

      const sentiment = JSON.parse(jsonMatch[0]);
      
      // Validate and normalize the response
      return {
        overall: sentiment.overall || 'neutral',
        confidence: Math.min(Math.max(sentiment.confidence || 0.5, 0), 1),
        sentiment_score: Math.min(Math.max(sentiment.sentiment_score || 0, -1), 1),
        scores: {
          positive: Math.min(Math.max(sentiment.scores?.positive || 0.33, 0), 1),
          negative: Math.min(Math.max(sentiment.scores?.negative || 0.33, 0), 1),
          neutral: Math.min(Math.max(sentiment.scores?.neutral || 0.34, 0), 1)
        },
        reasoning: sentiment.reasoning || 'Sentiment analysis completed',
        key_indicators: sentiment.key_indicators || []
      };

    } catch (error) {
      logger.error('Error analyzing sentiment with Gemini:', error);
      return this.getFallbackSentiment();
    }
  }

  // Extract key insights and recommendations
  async extractInsights(text, meetingContext = {}) {
    try {
      const prompt = `
        Analyze this business meeting and extract key insights, opportunities, and recommendations.
        
        Meeting Context:
        - Client: ${meetingContext.client_name || 'Unknown'}
        - Industry: ${meetingContext.client_industry || 'Unknown'}
        - Meeting Type: ${meetingContext.meeting_type || 'business'}
        
        Provide insights on:
        1. Business Opportunities
        2. Risk Factors
        3. Client Needs and Pain Points
        4. Competitive Landscape
        5. Strategic Recommendations
        6. Follow-up Actions
        
        Return ONLY a valid JSON object:
        {
          "opportunities": ["opportunity 1", "opportunity 2"],
          "risks": ["risk 1", "risk 2"],
          "client_needs": ["need 1", "need 2"],
          "recommendations": ["recommendation 1", "recommendation 2"],
          "follow_ups": ["action 1", "action 2"],
          "key_metrics": {"metric1": "value1", "metric2": "value2"}
        }
        
        Text: ${text}
      `;

      const response = await this.makeRequest(prompt, {
        temperature: 0.4,
        maxTokens: 1000
      });

      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        logger.warning('No valid JSON found in Gemini insights response');
        return this.getFallbackInsights();
      }

      return JSON.parse(jsonMatch[0]);

    } catch (error) {
      logger.error('Error extracting insights with Gemini:', error);
      return this.getFallbackInsights();
    }
  }

  // Generate action items from meeting text
  async extractActionItems(text, meetingContext = {}) {
    try {
      const prompt = `
        Extract action items and tasks from this business meeting transcript.
        
        Meeting Context:
        - Client: ${meetingContext.client_name || 'Unknown'}
        - Participants: ${meetingContext.participants?.join(', ') || 'Unknown'}
        
        Return ONLY a valid JSON array:
        [
          {
            "task": "Description of the task",
            "assignee": "Person responsible",
            "deadline": "timeframe or date mentioned",
            "priority": "high|medium|low",
            "category": "follow_up|research|preparation|decision"
          }
        ]
        
        Text: ${text}
      `;

      const response = await this.makeRequest(prompt, {
        temperature: 0.2,
        maxTokens: 800
      });

      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        logger.warning('No valid JSON found in Gemini action items response');
        return [];
      }

      return JSON.parse(jsonMatch[0]);

    } catch (error) {
      logger.error('Error extracting action items with Gemini:', error);
      return [];
    }
  }

  // Generate client relationship insights
  async analyzeClientRelationship(text, clientHistory = {}) {
    try {
      const prompt = `
        Analyze this meeting in the context of the client relationship and provide insights.
        
        Client History:
        - Previous meetings: ${clientHistory.meeting_count || 0}
        - Relationship duration: ${clientHistory.relationship_duration || 'Unknown'}
        - Previous sentiment: ${clientHistory.avg_sentiment || 'Unknown'}
        
        Analyze:
        1. Relationship health and trajectory
        2. Client satisfaction indicators
        3. Engagement level
        4. Trust and rapport building
        5. Potential concerns or red flags
        6. Opportunities to strengthen relationship
        
        Return ONLY a valid JSON object:
        {
          "relationship_health": "excellent|good|fair|poor",
          "engagement_level": "high|medium|low",
          "satisfaction_indicators": ["indicator1", "indicator2"],
          "concerns": ["concern1", "concern2"],
          "opportunities": ["opportunity1", "opportunity2"],
          "recommended_actions": ["action1", "action2"],
          "relationship_score": 0.85
        }
        
        Meeting Text: ${text}
      `;

      const response = await this.makeRequest(prompt, {
        temperature: 0.3,
        maxTokens: 800
      });

      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        logger.warning('No valid JSON found in Gemini relationship analysis');
        return this.getFallbackRelationshipAnalysis();
      }

      return JSON.parse(jsonMatch[0]);

    } catch (error) {
      logger.error('Error analyzing client relationship with Gemini:', error);
      return this.getFallbackRelationshipAnalysis();
    }
  }

  // Generate meeting questions for preparation
  async generateMeetingQuestions(clientContext = {}) {
    try {
      const prompt = `
        Generate thoughtful questions for an upcoming business meeting.
        
        Client Context:
        - Client: ${clientContext.client_name || 'Unknown'}
        - Industry: ${clientContext.client_industry || 'Unknown'}
        - Meeting Purpose: ${clientContext.meeting_purpose || 'General discussion'}
        - Previous topics: ${clientContext.previous_topics?.join(', ') || 'None'}
        
        Generate 10-15 strategic questions covering:
        1. Business objectives and goals
        2. Current challenges and pain points
        3. Market opportunities
        4. Competitive landscape
        5. Investment priorities
        6. Risk management
        7. Future planning
        
        Return ONLY a valid JSON array:
        ["Question 1?", "Question 2?", "Question 3?"]
      `;

      const response = await this.makeRequest(prompt, {
        temperature: 0.6,
        maxTokens: 1000
      });

      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        logger.warning('No valid JSON found in Gemini questions response');
        return this.getFallbackQuestions();
      }

      return JSON.parse(jsonMatch[0]);

    } catch (error) {
      logger.error('Error generating meeting questions with Gemini:', error);
      return this.getFallbackQuestions();
    }
  }

  // Fallback methods
  getFallbackSentiment() {
    return {
      overall: 'neutral',
      confidence: 0.5,
      sentiment_score: 0,
      scores: {
        positive: 0.33,
        negative: 0.33,
        neutral: 0.34
      },
      reasoning: 'Fallback sentiment analysis',
      key_indicators: []
    };
  }

  getFallbackInsights() {
    return {
      opportunities: ['Follow up on discussed topics'],
      risks: ['Monitor for any concerns raised'],
      client_needs: ['Address questions from meeting'],
      recommendations: ['Schedule follow-up meeting'],
      follow_ups: ['Send meeting summary'],
      key_metrics: {}
    };
  }

  getFallbackRelationshipAnalysis() {
    return {
      relationship_health: 'good',
      engagement_level: 'medium',
      satisfaction_indicators: ['Active participation'],
      concerns: [],
      opportunities: ['Continue regular communication'],
      recommended_actions: ['Schedule next meeting'],
      relationship_score: 0.7
    };
  }

  getFallbackQuestions() {
    return [
      'What are your main business objectives for this quarter?',
      'What challenges are you currently facing?',
      'How can we better support your goals?',
      'What opportunities do you see in your market?',
      'What are your investment priorities?'
    ];
  }

  // Test Gemini connection
  async testConnection() {
    try {
      const response = await this.makeRequest('Hello, this is a test. Please respond with "Connection successful".');
      return response.includes('Connection successful') || response.includes('successful');
    } catch (error) {
      logger.error('Gemini connection test failed:', error);
      return false;
    }
  }
}

module.exports = new GeminiService();
