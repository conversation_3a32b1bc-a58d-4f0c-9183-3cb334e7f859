#!/usr/bin/env python3
"""
SmartConverge Model Installation Script
Downloads and sets up all required deep learning models
"""

import os
import sys
import logging
import asyncio
from pathlib import Path
from typing import List, Dict, Any
import subprocess
import shutil

# Add nlp-service to path
sys.path.append(str(Path(__file__).parent.parent / "nlp-service"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModelInstaller:
    """Handles downloading and setting up ML models"""
    
    def __init__(self):
        self.models_dir = Path("nlp-service/models")
        self.cache_dir = Path("nlp-service/cache")
        self.data_dir = Path("nlp-service/data")
        
        # Create directories
        for directory in [self.models_dir, self.cache_dir, self.data_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def check_dependencies(self) -> bool:
        """Check if required packages are installed"""
        required_packages = [
            'torch', 'transformers', 'sentence-transformers', 
            'faiss-cpu', 'spacy', 'scikit-learn'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                logger.info(f"✅ {package} is installed")
            except ImportError:
                missing_packages.append(package)
                logger.error(f"❌ {package} is missing")
        
        if missing_packages:
            logger.error(f"Missing packages: {', '.join(missing_packages)}")
            logger.error("Please install them using: pip install -r nlp-service/requirements.txt")
            return False
        
        return True
    
    def download_huggingface_model(self, model_name: str, local_path: Path) -> bool:
        """Download a model from Hugging Face"""
        try:
            logger.info(f"📥 Downloading {model_name}...")
            
            from transformers import AutoTokenizer, AutoModel
            
            # Download tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModel.from_pretrained(model_name)
            
            # Save locally
            local_path.mkdir(parents=True, exist_ok=True)
            tokenizer.save_pretrained(local_path)
            model.save_pretrained(local_path)
            
            logger.info(f"✅ {model_name} downloaded to {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to download {model_name}: {e}")
            return False
    
    def download_sentence_transformer(self, model_name: str, local_path: Path) -> bool:
        """Download a sentence transformer model"""
        try:
            logger.info(f"📥 Downloading sentence transformer {model_name}...")
            
            from sentence_transformers import SentenceTransformer
            
            # Download model
            model = SentenceTransformer(model_name)
            
            # Save locally
            local_path.mkdir(parents=True, exist_ok=True)
            model.save(str(local_path))
            
            logger.info(f"✅ {model_name} downloaded to {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to download {model_name}: {e}")
            return False
    
    def download_spacy_model(self, model_name: str) -> bool:
        """Download a spaCy model"""
        try:
            logger.info(f"📥 Downloading spaCy model {model_name}...")
            
            # Use subprocess to download spaCy model
            result = subprocess.run([
                sys.executable, "-m", "spacy", "download", model_name
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ spaCy model {model_name} downloaded")
                return True
            else:
                logger.error(f"❌ Failed to download spaCy model: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to download spaCy model {model_name}: {e}")
            return False
    
    def setup_faiss_index(self) -> bool:
        """Set up FAISS index directory"""
        try:
            faiss_dir = self.data_dir / "faiss_index"
            faiss_dir.mkdir(parents=True, exist_ok=True)
            
            # Create empty index file
            index_file = faiss_dir / "index.faiss"
            if not index_file.exists():
                import faiss
                import numpy as np
                
                # Create empty index
                dimension = 384  # MiniLM dimension
                index = faiss.IndexFlatIP(dimension)
                faiss.write_index(index, str(index_file))
                
                logger.info(f"✅ Created empty FAISS index at {index_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup FAISS index: {e}")
            return False
    
    def install_core_models(self) -> bool:
        """Install core models required for the system"""
        models_to_install = [
            {
                "name": "cardiffnlp/twitter-roberta-base-sentiment-latest",
                "type": "huggingface",
                "local_path": self.models_dir / "sentiment_model",
                "description": "Sentiment Analysis Model"
            },
            {
                "name": "dbmdz/bert-large-cased-finetuned-conll03-english",
                "type": "huggingface", 
                "local_path": self.models_dir / "ner_model",
                "description": "Named Entity Recognition Model"
            },
            {
                "name": "facebook/bart-large-cnn",
                "type": "huggingface",
                "local_path": self.models_dir / "summarization_model",
                "description": "Text Summarization Model"
            },
            {
                "name": "all-MiniLM-L6-v2",
                "type": "sentence_transformer",
                "local_path": self.models_dir / "embedding_model",
                "description": "Sentence Embedding Model"
            },
            {
                "name": "en_core_web_sm",
                "type": "spacy",
                "description": "spaCy English Model"
            }
        ]
        
        success_count = 0
        total_count = len(models_to_install)
        
        for model_info in models_to_install:
            logger.info(f"🔄 Installing {model_info['description']}...")
            
            # Skip if already exists
            if model_info.get("local_path") and model_info["local_path"].exists():
                logger.info(f"⏭️  {model_info['description']} already exists, skipping...")
                success_count += 1
                continue
            
            success = False
            
            if model_info["type"] == "huggingface":
                success = self.download_huggingface_model(
                    model_info["name"], 
                    model_info["local_path"]
                )
            elif model_info["type"] == "sentence_transformer":
                success = self.download_sentence_transformer(
                    model_info["name"],
                    model_info["local_path"]
                )
            elif model_info["type"] == "spacy":
                success = self.download_spacy_model(model_info["name"])
            
            if success:
                success_count += 1
            else:
                logger.warning(f"⚠️  Failed to install {model_info['description']}")
        
        logger.info(f"📊 Model installation complete: {success_count}/{total_count} successful")
        return success_count == total_count
    
    def verify_installation(self) -> bool:
        """Verify that all models are properly installed"""
        logger.info("🔍 Verifying model installation...")
        
        try:
            # Test imports
            from transformers import AutoTokenizer, AutoModel
            from sentence_transformers import SentenceTransformer
            import spacy
            import faiss
            
            # Test model loading
            models_to_test = [
                ("sentiment_model", "Sentiment Analysis"),
                ("ner_model", "Named Entity Recognition"),
                ("summarization_model", "Text Summarization"),
                ("embedding_model", "Sentence Embeddings")
            ]
            
            for model_dir, description in models_to_test:
                model_path = self.models_dir / model_dir
                if model_path.exists():
                    try:
                        if model_dir == "embedding_model":
                            SentenceTransformer(str(model_path))
                        else:
                            AutoTokenizer.from_pretrained(model_path)
                            AutoModel.from_pretrained(model_path)
                        logger.info(f"✅ {description} model verified")
                    except Exception as e:
                        logger.error(f"❌ {description} model verification failed: {e}")
                        return False
                else:
                    logger.error(f"❌ {description} model not found at {model_path}")
                    return False
            
            # Test spaCy model
            try:
                nlp = spacy.load("en_core_web_sm")
                doc = nlp("Test sentence")
                logger.info("✅ spaCy model verified")
            except Exception as e:
                logger.error(f"❌ spaCy model verification failed: {e}")
                return False
            
            # Test FAISS
            try:
                index_path = self.data_dir / "faiss_index" / "index.faiss"
                if index_path.exists():
                    faiss.read_index(str(index_path))
                    logger.info("✅ FAISS index verified")
                else:
                    logger.warning("⚠️  FAISS index not found, will be created on first use")
            except Exception as e:
                logger.error(f"❌ FAISS verification failed: {e}")
                return False
            
            logger.info("✅ All models verified successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Model verification failed: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about installed models"""
        info = {
            "models_directory": str(self.models_dir),
            "cache_directory": str(self.cache_dir),
            "data_directory": str(self.data_dir),
            "installed_models": [],
            "total_size_mb": 0
        }
        
        for model_dir in self.models_dir.iterdir():
            if model_dir.is_dir():
                size_mb = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file()) / (1024 * 1024)
                info["installed_models"].append({
                    "name": model_dir.name,
                    "path": str(model_dir),
                    "size_mb": round(size_mb, 2)
                })
                info["total_size_mb"] += size_mb
        
        info["total_size_mb"] = round(info["total_size_mb"], 2)
        return info

def main():
    """Main installation function"""
    logger.info("🚀 Starting SmartConverge Model Installation")
    logger.info("=" * 50)
    
    installer = ModelInstaller()
    
    # Check dependencies
    if not installer.check_dependencies():
        logger.error("❌ Dependency check failed. Please install required packages first.")
        sys.exit(1)
    
    # Install models
    logger.info("📦 Installing core models...")
    if not installer.install_core_models():
        logger.warning("⚠️  Some models failed to install, but continuing...")
    
    # Setup FAISS
    logger.info("🔧 Setting up FAISS index...")
    installer.setup_faiss_index()
    
    # Verify installation
    logger.info("🔍 Verifying installation...")
    if installer.verify_installation():
        logger.info("✅ Model installation completed successfully!")
    else:
        logger.warning("⚠️  Some verification checks failed")
    
    # Show model info
    info = installer.get_model_info()
    logger.info("📊 Installation Summary:")
    logger.info(f"   Models installed: {len(info['installed_models'])}")
    logger.info(f"   Total size: {info['total_size_mb']} MB")
    logger.info(f"   Models directory: {info['models_directory']}")
    
    for model in info['installed_models']:
        logger.info(f"   - {model['name']}: {model['size_mb']} MB")
    
    logger.info("🎉 SmartConverge models are ready!")
    logger.info("You can now start the NLP service with: cd nlp-service && python start.py")

if __name__ == "__main__":
    main()
