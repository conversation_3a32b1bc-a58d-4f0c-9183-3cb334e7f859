import api from './api';

class UserService {
  // Get all users
  async getUsers(filters = {}) {
    try {
      const response = await api.get('/users', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get users:', error);
      throw error;
    }
  }

  // Get user by ID
  async getUserById(id) {
    try {
      const response = await api.get(`/users/${id}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get user by ID:', error);
      throw error;
    }
  }

  // Create new user
  async createUser(userData) {
    try {
      const response = await api.post('/users', userData);
      return response.data;
    } catch (error) {
      console.error('Failed to create user:', error);
      throw error;
    }
  }

  // Update user
  async updateUser(id, updateData) {
    try {
      const response = await api.put(`/users/${id}`, updateData);
      return response.data;
    } catch (error) {
      console.error('Failed to update user:', error);
      throw error;
    }
  }

  // Delete user
  async deleteUser(id) {
    try {
      const response = await api.delete(`/users/${id}`);
      return response.data;
    } catch (error) {
      console.error('Failed to delete user:', error);
      throw error;
    }
  }

  // Get user profile
  async getUserProfile() {
    try {
      const response = await api.get('/users/profile');
      return response.data;
    } catch (error) {
      console.error('Failed to get user profile:', error);
      throw error;
    }
  }

  // Update user profile
  async updateUserProfile(profileData) {
    try {
      const response = await api.put('/users/profile', profileData);
      return response.data;
    } catch (error) {
      console.error('Failed to update user profile:', error);
      throw error;
    }
  }

  // Change password
  async changePassword(passwordData) {
    try {
      const response = await api.post('/users/change-password', passwordData);
      return response.data;
    } catch (error) {
      console.error('Failed to change password:', error);
      throw error;
    }
  }

  // Get user statistics
  async getUserStats() {
    try {
      const response = await api.get('/users/stats');
      return response.data;
    } catch (error) {
      console.error('Failed to get user stats:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          totalUsers: 0,
          activeUsers: 0,
          newUsersThisMonth: 0,
          usersByRole: {
            admin: 0,
            manager: 0,
            client: 0
          }
        }
      };
    }
  }

  // Search users
  async searchUsers(query, filters = {}) {
    try {
      const response = await api.get('/users/search', {
        params: { q: query, ...filters }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to search users:', error);
      throw error;
    }
  }

  // Get users by role
  async getUsersByRole(role) {
    try {
      const response = await api.get(`/users/role/${role}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get users by role:', error);
      throw error;
    }
  }

  // Activate user
  async activateUser(id) {
    try {
      const response = await api.post(`/users/${id}/activate`);
      return response.data;
    } catch (error) {
      console.error('Failed to activate user:', error);
      throw error;
    }
  }

  // Deactivate user
  async deactivateUser(id) {
    try {
      const response = await api.post(`/users/${id}/deactivate`);
      return response.data;
    } catch (error) {
      console.error('Failed to deactivate user:', error);
      throw error;
    }
  }

  // Reset user password
  async resetUserPassword(id) {
    try {
      const response = await api.post(`/users/${id}/reset-password`);
      return response.data;
    } catch (error) {
      console.error('Failed to reset user password:', error);
      throw error;
    }
  }

  // Get user activity
  async getUserActivity(id, filters = {}) {
    try {
      const response = await api.get(`/users/${id}/activity`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get user activity:', error);
      throw error;
    }
  }

  // Get user permissions
  async getUserPermissions(id) {
    try {
      const response = await api.get(`/users/${id}/permissions`);
      return response.data;
    } catch (error) {
      console.error('Failed to get user permissions:', error);
      throw error;
    }
  }

  // Update user permissions
  async updateUserPermissions(id, permissions) {
    try {
      const response = await api.put(`/users/${id}/permissions`, { permissions });
      return response.data;
    } catch (error) {
      console.error('Failed to update user permissions:', error);
      throw error;
    }
  }

  // Upload user avatar
  async uploadAvatar(file) {
    try {
      const formData = new FormData();
      formData.append('avatar', file);
      
      const response = await api.post('/users/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to upload avatar:', error);
      throw error;
    }
  }

  // Get user preferences
  async getUserPreferences() {
    try {
      const response = await api.get('/users/preferences');
      return response.data;
    } catch (error) {
      console.error('Failed to get user preferences:', error);
      throw error;
    }
  }

  // Update user preferences
  async updateUserPreferences(preferences) {
    try {
      const response = await api.put('/users/preferences', preferences);
      return response.data;
    } catch (error) {
      console.error('Failed to update user preferences:', error);
      throw error;
    }
  }

  // Export users
  async exportUsers(format = 'csv', filters = {}) {
    try {
      const response = await api.get('/users/export', {
        params: { format, ...filters },
        responseType: 'blob'
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `users-export.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return { success: true };
    } catch (error) {
      console.error('Failed to export users:', error);
      throw error;
    }
  }

  // Bulk update users
  async bulkUpdateUsers(userIds, updateData) {
    try {
      const response = await api.put('/users/bulk-update', {
        userIds,
        updateData
      });
      return response.data;
    } catch (error) {
      console.error('Failed to bulk update users:', error);
      throw error;
    }
  }

  // Bulk delete users
  async bulkDeleteUsers(userIds) {
    try {
      const response = await api.delete('/users/bulk-delete', {
        data: { userIds }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to bulk delete users:', error);
      throw error;
    }
  }

  // Send invitation email
  async sendInvitation(email, role = 'client') {
    try {
      const response = await api.post('/users/invite', { email, role });
      return response.data;
    } catch (error) {
      console.error('Failed to send invitation:', error);
      throw error;
    }
  }

  // Get user dashboard data
  async getUserDashboardData(userId) {
    try {
      const response = await api.get(`/users/${userId}/dashboard`);
      return response.data;
    } catch (error) {
      console.error('Failed to get user dashboard data:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          meetingsCount: 0,
          upcomingMeetings: [],
          recentActivity: [],
          stats: {}
        }
      };
    }
  }

  // Get user notifications
  async getUserNotifications() {
    try {
      const response = await api.get('/users/notifications');
      return response.data;
    } catch (error) {
      console.error('Failed to get user notifications:', error);
      throw error;
    }
  }

  // Mark notification as read
  async markNotificationAsRead(notificationId) {
    try {
      const response = await api.put(`/users/notifications/${notificationId}/read`);
      return response.data;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  // Get user roles
  async getUserRoles() {
    try {
      const response = await api.get('/users/roles');
      return response.data;
    } catch (error) {
      console.error('Failed to get user roles:', error);
      // Return fallback data
      return {
        success: true,
        data: [
          { value: 'admin', label: 'Administrator' },
          { value: 'manager', label: 'Manager' },
          { value: 'client', label: 'Client' }
        ]
      };
    }
  }
}

export const userService = new UserService();
export default userService;
