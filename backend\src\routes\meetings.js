const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { Meeting, Tag, MeetingTranscript } = require('../models');
const nlpService = require('../services/nlpService');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @swagger
 * /meetings:
 *   get:
 *     summary: Get all meetings with optional filtering
 *     tags: [Meetings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: client_name
 *         schema:
 *           type: string
 *       - in: query
 *         name: organizer_name
 *         schema:
 *           type: string
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: List of meetings
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;

  // Build where clause
  const where = {};
  if (req.query.client_name) {
    where.client_name = { [require('sequelize').Op.iLike]: `%${req.query.client_name}%` };
  }
  if (req.query.organizer_name) {
    where.organizer_name = { [require('sequelize').Op.iLike]: `%${req.query.organizer_name}%` };
  }
  if (req.query.start_date && req.query.end_date) {
    where.meeting_date = {
      [require('sequelize').Op.between]: [req.query.start_date, req.query.end_date],
    };
  }

  const { count, rows: meetings } = await Meeting.findAndCountAll({
    where,
    include: [
      {
        model: Tag,
        as: 'tags',
        attributes: ['id', 'tag_name', 'tag_type', 'confidence_score', 'category'],
      },
    ],
    order: [['meeting_date', 'DESC']],
    limit,
    offset,
  });

  res.json({
    success: true,
    data: {
      meetings,
      pagination: {
        current_page: page,
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: limit,
      },
    },
  });
}));

/**
 * @swagger
 * /meetings/{id}:
 *   get:
 *     summary: Get a specific meeting by ID
 *     tags: [Meetings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Meeting details
 *       404:
 *         description: Meeting not found
 */
router.get('/:id', catchAsync(async (req, res, next) => {
  const meeting = await Meeting.findByPk(req.params.id, {
    include: [
      {
        model: Tag,
        as: 'tags',
        attributes: ['id', 'tag_name', 'tag_type', 'confidence_score', 'category', 'source'],
      },
    ],
  });

  if (!meeting) {
    return next(new AppError('Meeting not found', 404));
  }

  // Get transcript if available
  let transcript = null;
  try {
    transcript = await MeetingTranscript.findOne({ meeting_id: meeting.id });
  } catch (error) {
    logger.warn(`No transcript found for meeting ${meeting.id}`);
  }

  res.json({
    success: true,
    data: {
      meeting,
      transcript: transcript ? {
        id: transcript._id,
        speaker_segments: transcript.speaker_segments,
        topics_discussed: transcript.topics_discussed,
        key_phrases: transcript.key_phrases,
        processing_status: transcript.processing_status,
      } : null,
    },
  });
}));

/**
 * @swagger
 * /meetings:
 *   post:
 *     summary: Create a new meeting
 *     tags: [Meetings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Meeting'
 *     responses:
 *       201:
 *         description: Meeting created successfully
 *       400:
 *         description: Validation error
 */
router.post('/', [
  body('meeting_subject').notEmpty().trim(),
  body('meeting_notes').notEmpty().trim(),
  body('organizer_name').notEmpty().trim(),
  body('client_name').notEmpty().trim(),
  body('meeting_date').isISO8601(),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }

  const meetingData = {
    ...req.body,
    created_by: req.user.id,
  };

  // Create meeting
  const meeting = await Meeting.create(meetingData);

  // Process meeting notes with NLP
  try {
    const nlpResults = await nlpService.processText(meeting.meeting_notes, {
      subject: meeting.meeting_subject,
      client_name: meeting.client_name,
      organizer_name: meeting.organizer_name,
    });

    // Update meeting with generated summary
    if (nlpResults.summary) {
      meeting.summary = nlpResults.summary;
      meeting.sentiment_score = nlpResults.sentiment.score;
      await meeting.save();
    }

    // Create tags
    const tagPromises = nlpResults.all_tags.map(tag => 
      Tag.create({
        meeting_id: meeting.id,
        tag_name: tag.name,
        tag_type: tag.type,
        confidence_score: tag.confidence,
        category: tag.category,
        source: tag.source,
        context: tag.context,
        frequency: tag.frequency,
      })
    );

    await Promise.all(tagPromises);

    logger.info(`Meeting created and processed: ${meeting.id}`);
  } catch (error) {
    logger.error('Error processing meeting with NLP:', error);
    // Continue without NLP processing
  }

  // Fetch the complete meeting with tags
  const completeeMeeting = await Meeting.findByPk(meeting.id, {
    include: [
      {
        model: Tag,
        as: 'tags',
        attributes: ['id', 'tag_name', 'tag_type', 'confidence_score', 'category'],
      },
    ],
  });

  res.status(201).json({
    success: true,
    message: 'Meeting created successfully',
    data: completeeMeeting,
  });
}));

/**
 * @swagger
 * /meetings/{id}:
 *   put:
 *     summary: Update a meeting
 *     tags: [Meetings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Meeting'
 *     responses:
 *       200:
 *         description: Meeting updated successfully
 *       404:
 *         description: Meeting not found
 */
router.put('/:id', [
  body('meeting_subject').optional().notEmpty().trim(),
  body('meeting_notes').optional().notEmpty().trim(),
  body('organizer_name').optional().notEmpty().trim(),
  body('client_name').optional().notEmpty().trim(),
  body('meeting_date').optional().isISO8601(),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }

  const meeting = await Meeting.findByPk(req.params.id);
  if (!meeting) {
    return next(new AppError('Meeting not found', 404));
  }

  // Update meeting
  await meeting.update(req.body);

  // If notes were updated, reprocess with NLP
  if (req.body.meeting_notes) {
    try {
      const nlpResults = await nlpService.processText(req.body.meeting_notes, {
        subject: meeting.meeting_subject,
        client_name: meeting.client_name,
        organizer_name: meeting.organizer_name,
      });

      // Update summary and sentiment
      if (nlpResults.summary) {
        meeting.summary = nlpResults.summary;
        meeting.sentiment_score = nlpResults.sentiment.score;
        await meeting.save();
      }

      // Delete existing auto-generated tags and create new ones
      await Tag.destroy({
        where: {
          meeting_id: meeting.id,
          tag_type: ['explicit', 'implicit'],
        },
      });

      const tagPromises = nlpResults.all_tags.map(tag => 
        Tag.create({
          meeting_id: meeting.id,
          tag_name: tag.name,
          tag_type: tag.type,
          confidence_score: tag.confidence,
          category: tag.category,
          source: tag.source,
          context: tag.context,
          frequency: tag.frequency,
        })
      );

      await Promise.all(tagPromises);
    } catch (error) {
      logger.error('Error reprocessing meeting with NLP:', error);
    }
  }

  // Fetch updated meeting with tags
  const updatedMeeting = await Meeting.findByPk(meeting.id, {
    include: [
      {
        model: Tag,
        as: 'tags',
        attributes: ['id', 'tag_name', 'tag_type', 'confidence_score', 'category'],
      },
    ],
  });

  res.json({
    success: true,
    message: 'Meeting updated successfully',
    data: updatedMeeting,
  });
}));

/**
 * @swagger
 * /meetings/{id}:
 *   delete:
 *     summary: Delete a meeting
 *     tags: [Meetings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Meeting deleted successfully
 *       404:
 *         description: Meeting not found
 */
router.delete('/:id', catchAsync(async (req, res, next) => {
  const meeting = await Meeting.findByPk(req.params.id);
  if (!meeting) {
    return next(new AppError('Meeting not found', 404));
  }

  // Delete associated transcript
  try {
    await MeetingTranscript.deleteOne({ meeting_id: meeting.id });
  } catch (error) {
    logger.warn(`No transcript to delete for meeting ${meeting.id}`);
  }

  // Delete meeting (tags will be deleted due to CASCADE)
  await meeting.destroy();

  logger.info(`Meeting deleted: ${meeting.id}`);

  res.json({
    success: true,
    message: 'Meeting deleted successfully',
  });
}));

module.exports = router;
