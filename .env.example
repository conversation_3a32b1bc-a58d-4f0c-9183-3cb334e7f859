# OpenAI API Key (required for NLP processing)
OPENAI_API_KEY=your_openai_api_key_here

# Pinecone API Key (optional, for vector database)
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment

# Database Configuration (automatically set by Docker Compose)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=smartcoverage
DB_USER=smartcoverage_user
DB_PASS=smartcoverage_password

# MongoDB Configuration (automatically set by Docker Compose)
MONGODB_URI=************************************************************************************************

# JWT Secret (change in production)
JWT_SECRET=your_super_secret_jwt_key_change_in_production

# Application Configuration
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000
