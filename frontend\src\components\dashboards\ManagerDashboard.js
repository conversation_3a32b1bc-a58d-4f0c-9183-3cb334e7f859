import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Ty<PERSON>graphy,
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  VideoCall as VideoCallIcon,
  Analytics as AnalyticsIcon,
  Assignment as AssignmentIcon,
  TrendingUp as TrendingUpIcon,
  Add as AddIcon,
  Edit as EditIcon,
  PlayArrow as PlayIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Schedule as ScheduleIcon,
  Group as GroupIcon
} from '@mui/icons-material';
import { Line, Bar, Pie } from 'react-chartjs-2';
import '../../utils/chartSetup'; // Import Chart.js setup
import { useAuth } from '../../contexts/AuthContext';
import { meetingService } from '../../services/meetingService';
import { zoomService } from '../../services/zoomService';
import { analyticsService } from '../../services/analyticsService';

const ManagerDashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [meetings, setMeetings] = useState([]);
  const [analytics, setAnalytics] = useState({});
  const [loading, setLoading] = useState(true);
  const [createMeetingOpen, setCreateMeetingOpen] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: 'week',
    status: 'all',
    client: '',
    meetingType: 'all'
  });
  const [newMeeting, setNewMeeting] = useState({
    topic: '',
    client_name: '',
    client_industry: '',
    start_time: '',
    duration: 60,
    host_email: user?.email || '',
    meeting_type: 'client_call'
  });

  useEffect(() => {
    loadManagerData();
  }, [filters]);

  const loadManagerData = async () => {
    try {
      setLoading(true);
      const [meetingsData, analyticsData] = await Promise.all([
        meetingService.getMeetingsByManager(user.id, filters),
        analyticsService.getManagerAnalytics(user.id, filters)
      ]);

      setMeetings(meetingsData.data || []);
      setAnalytics(analyticsData.data || {});
    } catch (error) {
      console.error('Failed to load manager data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMeeting = async () => {
    try {
      const meetingData = {
        ...newMeeting,
        enable_nlp_analysis: true,
        created_by: user.id,
        settings: {
          auto_recording: 'cloud',
          waiting_room: true,
          mute_upon_entry: true,
          participant_video: true
        }
      };

      await zoomService.createMeetingWithNLP(meetingData);
      setCreateMeetingOpen(false);
      resetNewMeeting();
      loadManagerData();
    } catch (error) {
      console.error('Failed to create meeting:', error);
    }
  };

  const resetNewMeeting = () => {
    setNewMeeting({
      topic: '',
      client_name: '',
      client_industry: '',
      start_time: '',
      duration: 60,
      host_email: user?.email || '',
      meeting_type: 'client_call'
    });
  };

  const handleJoinMeeting = async (meetingId) => {
    try {
      const joinInfo = await zoomService.joinMeeting(meetingId);
      window.open(joinInfo.join_url, '_blank');
    } catch (error) {
      console.error('Failed to join meeting:', error);
    }
  };

  const handleStartMeeting = async (meetingId) => {
    try {
      const startInfo = await zoomService.startMeeting(meetingId);
      window.open(startInfo.start_url, '_blank');
    } catch (error) {
      console.error('Failed to start meeting:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled': return 'primary';
      case 'in_progress': return 'success';
      case 'completed': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const chartData = {
    meetingTrends: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
      datasets: [{
        label: 'Meetings Conducted',
        data: analytics.weeklyMeetings || [8, 12, 15, 10],
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        tension: 0.1
      }]
    },
    clientSentiment: {
      labels: ['Very Positive', 'Positive', 'Neutral', 'Negative', 'Very Negative'],
      datasets: [{
        data: analytics.sentimentDistribution || [25, 35, 25, 10, 5],
        backgroundColor: ['#4CAF50', '#8BC34A', '#FFC107', '#FF9800', '#F44336']
      }]
    },
    meetingTypes: {
      labels: ['Client Calls', 'Internal', 'Presentations', 'Training'],
      datasets: [{
        data: analytics.meetingTypeDistribution || [45, 25, 20, 10],
        backgroundColor: ['#2196F3', '#9C27B0', '#FF5722', '#607D8B']
      }]
    }
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Manager Dashboard - Records & Analytics
      </Typography>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <VideoCallIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    This Month
                  </Typography>
                  <Typography variant="h5">
                    {analytics.monthlyMeetings || 0}
                  </Typography>
                  <Typography variant="caption" color="success.main">
                    +12% from last month
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Avg Sentiment
                  </Typography>
                  <Typography variant="h5">
                    {analytics.averageSentiment || 'Positive'}
                  </Typography>
                  <Typography variant="caption" color="success.main">
                    +5% improvement
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <GroupIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Active Clients
                  </Typography>
                  <Typography variant="h5">
                    {analytics.activeClients || 0}
                  </Typography>
                  <Typography variant="caption" color="info.main">
                    Across all meetings
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <ScheduleIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Avg Duration
                  </Typography>
                  <Typography variant="h5">
                    {analytics.averageDuration || '45m'}
                  </Typography>
                  <Typography variant="caption" color="warning.main">
                    Optimal range
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for different views */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Meeting Records" />
            <Tab label="Analytics" />
            <Tab label="Client Insights" />
            <Tab label="Reports" />
          </Tabs>
        </Box>

        {/* Meeting Records Tab */}
        <TabPanel value={activeTab} index={0}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Meeting Records Management
            </Typography>
            <Box>
              <Button
                startIcon={<FilterIcon />}
                onClick={() => setFilterOpen(true)}
                sx={{ mr: 1 }}
              >
                Filter
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setCreateMeetingOpen(true)}
              >
                Schedule Meeting
              </Button>
            </Box>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Meeting Topic</TableCell>
                  <TableCell>Client</TableCell>
                  <TableCell>Date/Time</TableCell>
                  <TableCell>Duration</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Sentiment</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {meetings.map((meeting) => (
                  <TableRow key={meeting.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {meeting.subject}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {meeting.meeting_type}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          {meeting.client_name}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {meeting.client_industry}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {new Date(meeting.meeting_date).toLocaleString()}
                    </TableCell>
                    <TableCell>
                      {meeting.meeting_duration_minutes || 'N/A'} min
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={meeting.status}
                        color={getStatusColor(meeting.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={meeting.sentiment_score > 0 ? 'Positive' : meeting.sentiment_score < 0 ? 'Negative' : 'Neutral'}
                        color={meeting.sentiment_score > 0 ? 'success' : meeting.sentiment_score < 0 ? 'error' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {meeting.status === 'scheduled' && (
                        <>
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleStartMeeting(meeting.zoom_meeting_id)}
                            title="Start Meeting"
                          >
                            <PlayIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="success"
                            onClick={() => handleJoinMeeting(meeting.zoom_meeting_id)}
                            title="Join Meeting"
                          >
                            <VideoCallIcon />
                          </IconButton>
                        </>
                      )}
                      <IconButton size="small" color="default" title="View Details">
                        <ViewIcon />
                      </IconButton>
                      <IconButton size="small" color="default" title="Edit">
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Analytics Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Meeting Trends
                  </Typography>
                  <Line data={chartData.meetingTrends} />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Client Sentiment Distribution
                  </Typography>
                  <Pie data={chartData.clientSentiment} />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Meeting Types
                  </Typography>
                  <Pie data={chartData.meetingTypes} />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Key Insights
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <TrendingUpIcon color="success" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Client satisfaction improved by 15%"
                        secondary="Based on sentiment analysis"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <AnalyticsIcon color="info" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Average meeting duration optimized"
                        secondary="45 minutes is the sweet spot"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <GroupIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Top performing client: TechCorp"
                        secondary="Highest engagement scores"
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Client Insights Tab */}
        <TabPanel value={activeTab} index={2}>
          <Typography variant="h6" gutterBottom>
            Client Relationship Insights
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Client Engagement Timeline
                  </Typography>
                  {/* Client engagement chart would go here */}
                  <Alert severity="info">
                    AI-powered insights show client engagement patterns and recommend optimal follow-up timing.
                  </Alert>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Top Clients This Month
                  </Typography>
                  <List>
                    {(analytics.topClients || []).map((client, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={client.name}
                          secondary={`${client.meetings} meetings, ${client.sentiment} sentiment`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Reports Tab */}
        <TabPanel value={activeTab} index={3}>
          <Typography variant="h6" gutterBottom>
            Generate Reports
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Available Reports
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemText
                        primary="Monthly Meeting Summary"
                        secondary="Comprehensive overview of all meetings"
                      />
                      <Button startIcon={<DownloadIcon />} size="small">
                        Download
                      </Button>
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Client Sentiment Analysis"
                        secondary="Detailed sentiment trends and insights"
                      />
                      <Button startIcon={<DownloadIcon />} size="small">
                        Download
                      </Button>
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Performance Metrics"
                        secondary="Meeting efficiency and outcomes"
                      />
                      <Button startIcon={<DownloadIcon />} size="small">
                        Download
                      </Button>
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Card>

      {/* Create Meeting Dialog */}
      <Dialog open={createMeetingOpen} onClose={() => setCreateMeetingOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Schedule New Meeting</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meeting Topic"
                value={newMeeting.topic}
                onChange={(e) => setNewMeeting({...newMeeting, topic: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Client Name"
                value={newMeeting.client_name}
                onChange={(e) => setNewMeeting({...newMeeting, client_name: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Client Industry"
                value={newMeeting.client_industry}
                onChange={(e) => setNewMeeting({...newMeeting, client_industry: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="datetime-local"
                label="Start Time"
                value={newMeeting.start_time}
                onChange={(e) => setNewMeeting({...newMeeting, start_time: e.target.value})}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                label="Duration (minutes)"
                value={newMeeting.duration}
                onChange={(e) => setNewMeeting({...newMeeting, duration: parseInt(e.target.value)})}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Meeting Type</InputLabel>
                <Select
                  value={newMeeting.meeting_type}
                  onChange={(e) => setNewMeeting({...newMeeting, meeting_type: e.target.value})}
                >
                  <MenuItem value="client_call">Client Call</MenuItem>
                  <MenuItem value="internal">Internal Meeting</MenuItem>
                  <MenuItem value="presentation">Presentation</MenuItem>
                  <MenuItem value="training">Training</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateMeetingOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateMeeting} variant="contained">
            Schedule Meeting
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ManagerDashboard;
