{
  service: 'smartcoverage-backend',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'NLP Service WebSocket error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1118:18)\n' +
    '    at afterConnectMultiple (node:net:1685:7)',
  timestamp: '2025-05-31 10:42:51'
}
{
  service: 'smartcoverage-backend',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'NLP Service WebSocket error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1118:18)\n' +
    '    at afterConnectMultiple (node:net:1685:7)',
  timestamp: '2025-05-31 10:44:26'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeConnectionError',
  parent: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  original: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'SequelizeConnectionError: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:149:20)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Client._handleAuthSASLContinue (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:284:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)',
  timestamp: '2025-05-31 10:44:39'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeConnectionError',
  parent: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  original: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
      at Object.continueSession (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\crypto\sasl.js:36:11)
      at Client._handleAuthSASLContinue (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\client.js:276:18)
      at Connection.emit (node:events:519:28)
      at C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg\lib\connection.js:116:12
      at Parser.parse (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\parser.js:36:17)
      at Socket.<anonymous> (C:\Users\<USER>\Documents\augment-projects\smartcoverage\node_modules\pg-protocol\dist\index.js:11:42)
      at Socket.emit (node:events:519:28)
      at addChunk (node:internal/streams/readable:559:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
      at Readable.push (node:internal/streams/readable:390:5),
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'SequelizeConnectionError: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:149:20)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Client._handleAuthSASLContinue (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:284:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)',
  timestamp: '2025-05-31 10:44:53'
}
{
  service: 'smartcoverage-backend',
  name: 'SequelizeHostNotFoundError',
  parent: Error: getaddrinfo ENOTFOUND postgres
      at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'postgres'
  },
  original: Error: getaddrinfo ENOTFOUND postgres
      at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'postgres'
  },
  level: 'error',
  message: '❌ Unable to connect to PostgreSQL database: getaddrinfo ENOTFOUND postgres',
  stack: 'SequelizeHostNotFoundError: getaddrinfo ENOTFOUND postgres\n' +
    '    at Client._connectionCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:136:24)\n' +
    '    at Client._handleErrorWhileConnecting (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:336:19)\n' +
    '    at Client._handleErrorEvent (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\client.js:346:19)\n' +
    '    at Connection.emit (node:events:519:28)\n' +
    '    at Socket.reportStreamError (C:\\Users\\<USER>\\Documents\\augment-projects\\smartcoverage\\node_modules\\pg\\lib\\connection.js:57:12)\n' +
    '    at Socket.emit (node:events:519:28)\n' +
    '    at emitErrorNT (node:internal/streams/destroy:169:8)\n' +
    '    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)',
  timestamp: '2025-05-31 11:02:09'
}
