const { sequelize } = require('../config/database');

// Import all models
const User = require('./User');
const Meeting = require('./Meeting');
const Tag = require('./Tag');
const Client = require('./Client');

// Import MongoDB models
const MeetingTranscript = require('./MeetingTranscript');

// Define associations
const defineAssociations = () => {
  // User associations
  User.hasMany(Meeting, {
    foreignKey: 'created_by',
    as: 'meetings_created',
  });
  
  User.hasMany(Tag, {
    foreignKey: 'validated_by',
    as: 'tags_validated',
  });
  
  User.hasMany(Client, {
    foreignKey: 'assigned_analyst',
    as: 'assigned_clients',
  });

  // Meeting associations
  Meeting.belongsTo(User, {
    foreignKey: 'created_by',
    as: 'creator',
  });
  
  Meeting.hasMany(Tag, {
    foreignKey: 'meeting_id',
    as: 'tags',
    onDelete: 'CASCADE',
  });

  // Tag associations
  Tag.belongsTo(Meeting, {
    foreignKey: 'meeting_id',
    as: 'meeting',
  });
  
  Tag.belongsTo(User, {
    foreignKey: 'validated_by',
    as: 'validator',
  });

  // Client associations
  Client.belongsTo(User, {
    foreignKey: 'assigned_analyst',
    as: 'analyst',
  });
};

// Initialize associations
defineAssociations();

// Sync database (only in development)
const syncDatabase = async () => {
  if (process.env.NODE_ENV === 'development') {
    try {
      await sequelize.sync({ alter: true });
      console.log('✅ Database synchronized successfully');
    } catch (error) {
      console.error('❌ Database synchronization failed:', error);
      throw error;
    }
  }
};

module.exports = {
  sequelize,
  User,
  Meeting,
  Tag,
  Client,
  MeetingTranscript,
  syncDatabase,
};
