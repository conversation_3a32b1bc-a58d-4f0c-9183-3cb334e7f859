#!/bin/bash

# SmartConverge - Start All Services Script
# This script starts all components of the SmartConverge system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to check if a service is running
check_service() {
    local service_name=$1
    local port=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        print_status "$service_name is running on port $port"
        return 0
    else
        print_warning "$service_name is not running on port $port"
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be ready on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if check_service "$service_name" $port; then
            print_status "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites..."
    
    # Check if databases are running
    if ! check_service "PostgreSQL" 5432; then
        print_error "PostgreSQL is not running. Please start PostgreSQL first."
        exit 1
    fi
    
    if ! check_service "MongoDB" 27017; then
        print_warning "MongoDB is not running. Starting MongoDB..."
        # Try to start MongoDB
        if command -v mongod &> /dev/null; then
            mongod --fork --logpath /tmp/mongodb.log --dbpath ./data/mongodb || print_warning "Failed to start MongoDB automatically"
        fi
    fi
    
    if ! check_service "Redis" 6379; then
        print_warning "Redis is not running. Starting Redis..."
        # Try to start Redis
        if command -v redis-server &> /dev/null; then
            redis-server --daemonize yes || print_warning "Failed to start Redis automatically"
        fi
    fi
}

# Start NLP Service
start_nlp_service() {
    print_header "Starting NLP Service..."
    
    cd nlp-service
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_error "Python virtual environment not found. Please run setup.sh first."
        exit 1
    fi
    
    # Activate virtual environment and start service
    source venv/bin/activate
    
    print_status "Starting NLP Service on port 8000..."
    python start.py &
    NLP_PID=$!
    
    deactivate
    cd ..
    
    # Wait for NLP service to be ready
    wait_for_service "NLP Service" 8000
    
    echo $NLP_PID > .nlp_service.pid
    print_status "NLP Service started with PID: $NLP_PID"
}

# Start Backend API
start_backend() {
    print_header "Starting Backend API..."
    
    cd backend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_error "Backend dependencies not installed. Please run setup.sh first."
        exit 1
    fi
    
    print_status "Starting Backend API on port 3001..."
    npm run dev &
    BACKEND_PID=$!
    
    cd ..
    
    # Wait for backend to be ready
    wait_for_service "Backend API" 3001
    
    echo $BACKEND_PID > .backend.pid
    print_status "Backend API started with PID: $BACKEND_PID"
}

# Start Frontend
start_frontend() {
    print_header "Starting Frontend..."
    
    cd frontend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_error "Frontend dependencies not installed. Please run setup.sh first."
        exit 1
    fi
    
    print_status "Starting Frontend on port 3000..."
    npm start &
    FRONTEND_PID=$!
    
    cd ..
    
    # Wait for frontend to be ready
    wait_for_service "Frontend" 3000
    
    echo $FRONTEND_PID > .frontend.pid
    print_status "Frontend started with PID: $FRONTEND_PID"
}

# Function to handle cleanup on exit
cleanup() {
    print_header "Shutting down services..."
    
    if [ -f ".frontend.pid" ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        print_status "Stopping Frontend (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID 2>/dev/null || true
        rm .frontend.pid
    fi
    
    if [ -f ".backend.pid" ]; then
        BACKEND_PID=$(cat .backend.pid)
        print_status "Stopping Backend API (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null || true
        rm .backend.pid
    fi
    
    if [ -f ".nlp_service.pid" ]; then
        NLP_PID=$(cat .nlp_service.pid)
        print_status "Stopping NLP Service (PID: $NLP_PID)..."
        kill $NLP_PID 2>/dev/null || true
        rm .nlp_service.pid
    fi
    
    print_status "All services stopped."
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Main function
main() {
    print_header "🚀 Starting SmartConverge System"
    echo "=================================="
    
    check_prerequisites
    
    # Start services in order
    start_nlp_service
    sleep 5  # Give NLP service time to fully initialize
    
    start_backend
    sleep 3  # Give backend time to connect to NLP service
    
    start_frontend
    
    print_header "✅ All Services Started Successfully!"
    echo ""
    print_status "SmartConverge is now running:"
    echo ""
    echo "🌐 Frontend:     http://localhost:3000"
    echo "🔧 Backend API:  http://localhost:3001"
    echo "🧠 NLP Service:  http://localhost:8000"
    echo ""
    echo "📚 API Documentation:"
    echo "   Backend:      http://localhost:3001/api-docs"
    echo "   NLP Service:  http://localhost:8000/docs"
    echo ""
    print_status "Demo Credentials:"
    echo "   Admin:    <EMAIL> / admin123"
    echo "   Analyst:  <EMAIL> / password123"
    echo ""
    print_warning "Press Ctrl+C to stop all services"
    
    # Keep script running
    while true; do
        sleep 10
        
        # Check if services are still running
        if ! check_service "NLP Service" 8000; then
            print_error "NLP Service has stopped unexpectedly"
            break
        fi
        
        if ! check_service "Backend API" 3001; then
            print_error "Backend API has stopped unexpectedly"
            break
        fi
        
        if ! check_service "Frontend" 3000; then
            print_error "Frontend has stopped unexpectedly"
            break
        fi
    done
}

# Check if running from correct directory
if [ ! -f "package.json" ] && [ ! -d "backend" ] && [ ! -d "frontend" ] && [ ! -d "nlp-service" ]; then
    print_error "Please run this script from the SmartConverge root directory"
    exit 1
fi

# Run main function
main "$@"
