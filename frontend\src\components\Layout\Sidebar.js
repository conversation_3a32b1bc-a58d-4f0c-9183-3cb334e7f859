import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  Chip,
} from '@mui/material';
import {
  Dashboard,
  Event,
  People,
  Analytics,
  Assessment,
  Settings,
  TrendingUp,
  Business,
} from '@mui/icons-material';

import { useAuth } from '../../contexts/AuthContext';

const menuItems = [
  {
    text: 'Dashboard',
    icon: <Dashboard />,
    path: '/dashboard',
    roles: ['admin', 'manager', 'analyst', 'viewer'],
  },
  {
    text: 'Meetings',
    icon: <Event />,
    path: '/meetings',
    roles: ['admin', 'manager', 'analyst', 'viewer'],
  },
  {
    text: 'Clients',
    icon: <Business />,
    path: '/clients',
    roles: ['admin', 'manager', 'analyst', 'viewer'],
  },
  {
    text: 'Analytics',
    icon: <TrendingUp />,
    path: '/analytics',
    roles: ['admin', 'manager', 'analyst'],
  },
  {
    text: 'Reports',
    icon: <Assessment />,
    path: '/reports',
    roles: ['admin', 'manager', 'analyst'],
  },
  {
    text: 'Settings',
    icon: <Settings />,
    path: '/settings',
    roles: ['admin', 'manager'],
  },
];

const Sidebar = ({ onItemClick }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleNavigation = (path) => {
    navigate(path);
    if (onItemClick) {
      onItemClick();
    }
  };

  const filteredMenuItems = menuItems.filter(item =>
    item.roles.includes(user?.role)
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo/Brand */}
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Analytics color="primary" />
          <Typography variant="h6" noWrap component="div">
            SmartConverge
          </Typography>
        </Box>
      </Toolbar>

      <Divider />

      {/* User Info */}
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Welcome back,
        </Typography>
        <Typography variant="subtitle1" fontWeight="medium">
          {user?.first_name} {user?.last_name}
        </Typography>
        <Chip
          label={user?.role?.charAt(0).toUpperCase() + user?.role?.slice(1)}
          size="small"
          color="primary"
          variant="outlined"
          sx={{ mt: 1 }}
        />
      </Box>

      <Divider />

      {/* Navigation Menu */}
      <List sx={{ flexGrow: 1, pt: 1 }}>
        {filteredMenuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => handleNavigation(item.path)}
              sx={{
                mx: 1,
                borderRadius: 1,
                '&.Mui-selected': {
                  backgroundColor: 'primary.main',
                  color: 'primary.contrastText',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                  },
                  '& .MuiListItemIcon-root': {
                    color: 'primary.contrastText',
                  },
                },
              }}
            >
              <ListItemIcon
                sx={{
                  color: location.pathname === item.path ? 'inherit' : 'text.secondary',
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.text}
                primaryTypographyProps={{
                  fontWeight: location.pathname === item.path ? 'medium' : 'normal',
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      {/* Footer */}
      <Box sx={{ p: 2, mt: 'auto' }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          SmartConverge v1.0.0
        </Typography>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          Intelligent Meeting Analysis
        </Typography>
      </Box>
    </Box>
  );
};

export default Sidebar;
