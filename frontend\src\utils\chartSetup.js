import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

// Default chart options
export const defaultChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
    },
    title: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

// Chart color palette
export const chartColors = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  error: '#d32f2f',
  info: '#0288d1',
  background: {
    primary: 'rgba(25, 118, 210, 0.1)',
    secondary: 'rgba(220, 0, 78, 0.1)',
    success: 'rgba(46, 125, 50, 0.1)',
    warning: 'rgba(237, 108, 2, 0.1)',
    error: 'rgba(211, 47, 47, 0.1)',
    info: 'rgba(2, 136, 209, 0.1)',
  }
};

// Sentiment chart colors
export const sentimentColors = {
  positive: '#4caf50',
  neutral: '#ff9800',
  negative: '#f44336',
  veryPositive: '#2e7d32',
  veryNegative: '#c62828'
};

export default ChartJS;
