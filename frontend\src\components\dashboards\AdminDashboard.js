import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  VideoCall as VideoCallIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { useAuth } from '../../contexts/AuthContext';
import { meetingService } from '../../services/meetingService';
import { userService } from '../../services/userService';
import { zoomService } from '../../services/zoomService';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalMeetings: 0,
    activeMeetings: 0,
    totalUsers: 0,
    totalClients: 0,
    systemHealth: 'good'
  });
  const [meetings, setMeetings] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [createMeetingOpen, setCreateMeetingOpen] = useState(false);
  const [newMeeting, setNewMeeting] = useState({
    topic: '',
    client_name: '',
    client_industry: '',
    start_time: '',
    duration: 60,
    host_email: user?.email || '',
    meeting_type: 'client_call'
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [meetingsData, usersData, statsData] = await Promise.all([
        meetingService.getAllMeetings(),
        userService.getAllUsers(),
        meetingService.getStats()
      ]);

      setMeetings(meetingsData.data || []);
      setUsers(usersData.data || []);
      setStats(statsData.data || stats);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMeeting = async () => {
    try {
      const meetingData = {
        ...newMeeting,
        enable_nlp_analysis: true,
        settings: {
          auto_recording: 'cloud',
          waiting_room: true,
          mute_upon_entry: true
        }
      };

      await zoomService.createMeetingWithNLP(meetingData);
      setCreateMeetingOpen(false);
      setNewMeeting({
        topic: '',
        client_name: '',
        client_industry: '',
        start_time: '',
        duration: 60,
        host_email: user?.email || '',
        meeting_type: 'client_call'
      });
      loadDashboardData();
    } catch (error) {
      console.error('Failed to create meeting:', error);
    }
  };

  const handleJoinMeeting = async (meetingId) => {
    try {
      const joinInfo = await zoomService.joinMeeting(meetingId);
      window.open(joinInfo.join_url, '_blank');
    } catch (error) {
      console.error('Failed to join meeting:', error);
    }
  };

  const handleStartMeeting = async (meetingId) => {
    try {
      const startInfo = await zoomService.startMeeting(meetingId);
      window.open(startInfo.start_url, '_blank');
    } catch (error) {
      console.error('Failed to start meeting:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled': return 'primary';
      case 'in_progress': return 'success';
      case 'completed': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const chartData = {
    meetingTrends: {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      datasets: [{
        label: 'Meetings',
        data: [12, 19, 15, 25, 22, 8, 5],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
      }]
    },
    userRoles: {
      labels: ['Admins', 'Managers', 'Clients'],
      datasets: [{
        data: [
          users.filter(u => u.role === 'admin').length,
          users.filter(u => u.role === 'manager').length,
          users.filter(u => u.role === 'client').length
        ],
        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
      }]
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Admin Dashboard - Complete System Control
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <VideoCallIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Meetings
                  </Typography>
                  <Typography variant="h5">
                    {stats.totalMeetings}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PlayIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Active Meetings
                  </Typography>
                  <Typography variant="h5">
                    {stats.activeMeetings}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PeopleIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Users
                  </Typography>
                  <Typography variant="h5">
                    {stats.totalUsers}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <AnalyticsIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    System Health
                  </Typography>
                  <Chip 
                    label={stats.systemHealth} 
                    color={stats.systemHealth === 'good' ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Meeting Trends (This Week)
              </Typography>
              <Line data={chartData.meetingTrends} />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Distribution
              </Typography>
              <Doughnut data={chartData.userRoles} />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Meeting Management */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Meeting Management
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateMeetingOpen(true)}
            >
              Create Meeting
            </Button>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Topic</TableCell>
                  <TableCell>Client</TableCell>
                  <TableCell>Date/Time</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Platform</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {meetings.slice(0, 10).map((meeting) => (
                  <TableRow key={meeting.id}>
                    <TableCell>{meeting.subject}</TableCell>
                    <TableCell>{meeting.client_name}</TableCell>
                    <TableCell>
                      {new Date(meeting.meeting_date).toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={meeting.status} 
                        color={getStatusColor(meeting.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={meeting.meeting_platform || 'zoom'} 
                        variant="outlined"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {meeting.status === 'scheduled' && (
                        <>
                          <IconButton 
                            size="small" 
                            color="primary"
                            onClick={() => handleStartMeeting(meeting.zoom_meeting_id)}
                            title="Start Meeting"
                          >
                            <PlayIcon />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            color="success"
                            onClick={() => handleJoinMeeting(meeting.zoom_meeting_id)}
                            title="Join Meeting"
                          >
                            <VideoCallIcon />
                          </IconButton>
                        </>
                      )}
                      <IconButton size="small" color="default">
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* User Management */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            User Management
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Department</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.slice(0, 10).map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{user.first_name} {user.last_name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip 
                        label={user.role} 
                        color="primary"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{user.department}</TableCell>
                    <TableCell>
                      <Chip 
                        label={user.is_active ? 'Active' : 'Inactive'} 
                        color={user.is_active ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" color="default">
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Create Meeting Dialog */}
      <Dialog open={createMeetingOpen} onClose={() => setCreateMeetingOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Meeting with AI Analysis</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meeting Topic"
                value={newMeeting.topic}
                onChange={(e) => setNewMeeting({...newMeeting, topic: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Client Name"
                value={newMeeting.client_name}
                onChange={(e) => setNewMeeting({...newMeeting, client_name: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Client Industry"
                value={newMeeting.client_industry}
                onChange={(e) => setNewMeeting({...newMeeting, client_industry: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="datetime-local"
                label="Start Time"
                value={newMeeting.start_time}
                onChange={(e) => setNewMeeting({...newMeeting, start_time: e.target.value})}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                label="Duration (minutes)"
                value={newMeeting.duration}
                onChange={(e) => setNewMeeting({...newMeeting, duration: parseInt(e.target.value)})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Meeting Type</InputLabel>
                <Select
                  value={newMeeting.meeting_type}
                  onChange={(e) => setNewMeeting({...newMeeting, meeting_type: e.target.value})}
                >
                  <MenuItem value="client_call">Client Call</MenuItem>
                  <MenuItem value="internal">Internal Meeting</MenuItem>
                  <MenuItem value="presentation">Presentation</MenuItem>
                  <MenuItem value="training">Training</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Alert severity="info">
                This meeting will be created with AI-powered real-time analysis including sentiment tracking, 
                automatic transcription, and intelligent insights generation.
              </Alert>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateMeetingOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateMeeting} variant="contained">
            Create Meeting
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminDashboard;
