from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Any, Optional
import asyncio
import logging
import json
from datetime import datetime
import os

# Try to import Gemini service, fallback if not available
try:
    from services.gemini_service import gemini_service
    GEMINI_AVAILABLE = True
except ImportError as e:
    print(f"Gemini service not available: {e}")
    GEMINI_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="SmartConverge NLP Service",
    description="Advanced NLP processing service with Gemini AI",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class TextProcessingRequest(BaseModel):
    text: str = Field(..., description="Text to process")
    options: Optional[Dict[str, Any]] = Field(default={}, description="Processing options")

class SearchRequest(BaseModel):
    query: str = Field(..., description="Search query")
    top_k: int = Field(default=10, description="Number of results to return")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="Search filters")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "gemini_available": GEMINI_AVAILABLE,
            "basic_nlp": True
        }
    }

# Basic fallback sentiment analysis
def fallback_sentiment_analysis(text: str):
    """Simple rule-based sentiment analysis"""
    positive_words = ['good', 'great', 'excellent', 'positive', 'successful', 'profitable', 'growth', 'happy', 'satisfied']
    negative_words = ['bad', 'poor', 'negative', 'loss', 'decline', 'risk', 'concern', 'unhappy', 'disappointed']
    
    words = text.lower().split()
    positive_count = sum(1 for word in words if word in positive_words)
    negative_count = sum(1 for word in words if word in negative_words)
    
    total_words = len(words)
    if total_words == 0:
        return {
            "overall": "neutral",
            "confidence": 0.5,
            "sentiment_score": 0,
            "scores": {"positive": 0.33, "negative": 0.33, "neutral": 0.34}
        }
    
    score = (positive_count - negative_count) / total_words
    
    if score > 0.01:
        overall = "positive"
    elif score < -0.01:
        overall = "negative"
    else:
        overall = "neutral"
    
    return {
        "overall": overall,
        "confidence": min(0.8, abs(score) * 10),
        "sentiment_score": score,
        "scores": {
            "positive": positive_count / total_words,
            "negative": negative_count / total_words,
            "neutral": 1 - (positive_count + negative_count) / total_words
        }
    }

# Basic fallback tag extraction
def fallback_tag_extraction(text: str):
    """Simple keyword-based tag extraction"""
    financial_keywords = [
        'investment', 'portfolio', 'roi', 'return', 'profit', 'revenue',
        'funding', 'valuation', 'growth', 'market', 'strategy', 'risk'
    ]
    
    tags = []
    text_lower = text.lower()
    
    for keyword in financial_keywords:
        if keyword in text_lower:
            tags.append({
                "name": keyword,
                "type": "explicit",
                "confidence": 0.7,
                "source": "keyword_match"
            })
    
    return tags

# Basic fallback summarization
def fallback_summarization(text: str):
    """Simple extractive summarization"""
    sentences = text.split('.')
    if len(sentences) <= 3:
        return text
    
    # Return first and last sentences as summary
    summary = f"{sentences[0].strip()}. {sentences[-2].strip()}."
    return summary

# Synchronous processing endpoint
@app.post("/process/sync")
async def process_text_sync(request: TextProcessingRequest):
    """Process text synchronously"""
    try:
        # Basic processing with fallback methods
        sentiment = fallback_sentiment_analysis(request.text)
        tags = fallback_tag_extraction(request.text)
        summary = fallback_summarization(request.text)
        
        result = {
            "explicit_tags": tags,
            "implicit_tags": [],
            "all_tags": tags,
            "summary": summary,
            "sentiment": sentiment,
            "entities": [],
            "processing_time_ms": 100,  # Placeholder
            "word_count": len(request.text.split()),
            "confidence_score": 0.6,
            "ai_provider": "fallback"
        }
        
        return {
            "status": "completed",
            "result": result
        }
    except Exception as e:
        logger.error(f"Sync processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Gemini endpoints (if available)
if GEMINI_AVAILABLE:
    @app.post("/gemini/analyze")
    async def gemini_analyze_text(request: TextProcessingRequest):
        """Comprehensive text analysis using Gemini AI"""
        try:
            context = request.options.get('meeting_context', {})
            
            # Run all Gemini analyses in parallel
            results = await asyncio.gather(
                gemini_service.extract_implicit_tags(request.text, context),
                gemini_service.generate_summary(request.text, context),
                gemini_service.analyze_sentiment(request.text, context),
                gemini_service.extract_insights(request.text, context),
                gemini_service.extract_action_items(request.text, context),
                return_exceptions=True
            )
            
            implicit_tags, summary, sentiment, insights, action_items = results
            
            return {
                "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
                "implicit_tags": implicit_tags if not isinstance(implicit_tags, Exception) else [],
                "summary": summary if not isinstance(summary, Exception) else "Summary generation failed",
                "sentiment": sentiment if not isinstance(sentiment, Exception) else fallback_sentiment_analysis(request.text),
                "insights": insights if not isinstance(insights, Exception) else {"opportunities": [], "risks": [], "recommendations": []},
                "action_items": action_items if not isinstance(action_items, Exception) else [],
                "ai_provider": "gemini",
                "processing_time": "async"
            }
        except Exception as e:
            logger.error(f"Gemini analysis failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.post("/gemini/sentiment")
    async def gemini_sentiment_analysis(request: TextProcessingRequest):
        """Sentiment analysis using Gemini AI"""
        try:
            context = request.options.get('meeting_context', {})
            result = await gemini_service.analyze_sentiment(request.text, context)
            
            return {
                "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
                "sentiment": result,
                "ai_provider": "gemini"
            }
        except Exception as e:
            logger.error(f"Gemini sentiment analysis failed: {e}")
            # Fallback to basic sentiment analysis
            result = fallback_sentiment_analysis(request.text)
            return {
                "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
                "sentiment": result,
                "ai_provider": "fallback"
            }

    @app.post("/gemini/summary")
    async def gemini_generate_summary(request: TextProcessingRequest):
        """Generate summary using Gemini AI"""
        try:
            context = request.options.get('meeting_context', {})
            result = await gemini_service.generate_summary(request.text, context)
            
            return {
                "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
                "summary": result,
                "ai_provider": "gemini"
            }
        except Exception as e:
            logger.error(f"Gemini summary generation failed: {e}")
            # Fallback to basic summarization
            result = fallback_summarization(request.text)
            return {
                "text_preview": request.text[:100] + "..." if len(request.text) > 100 else request.text,
                "summary": result,
                "ai_provider": "fallback"
            }

    @app.get("/gemini/test")
    async def test_gemini_connection():
        """Test Gemini AI connection"""
        try:
            is_connected = await gemini_service.test_connection()
            return {
                "status": "connected" if is_connected else "disconnected",
                "service": "gemini",
                "initialized": gemini_service.initialized
            }
        except Exception as e:
            logger.error(f"Gemini connection test failed: {e}")
            return {
                "status": "error",
                "service": "gemini",
                "error": str(e)
            }

# Basic endpoints that work without complex dependencies
@app.post("/models/sentiment/analyze")
async def analyze_sentiment_basic(request: TextProcessingRequest):
    """Basic sentiment analysis"""
    try:
        result = fallback_sentiment_analysis(request.text)
        return {
            "text": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "sentiment": result
        }
    except Exception as e:
        logger.error(f"Sentiment analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/models/extract-tags")
async def extract_tags_basic(request: TextProcessingRequest):
    """Basic tag extraction"""
    try:
        result = fallback_tag_extraction(request.text)
        return {
            "text": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "tags": result
        }
    except Exception as e:
        logger.error(f"Tag extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/models/summarize")
async def summarize_text_basic(request: TextProcessingRequest):
    """Basic text summarization"""
    try:
        result = fallback_summarization(request.text)
        return {
            "text": request.text[:100] + "..." if len(request.text) > 100 else request.text,
            "summary": result
        }
    except Exception as e:
        logger.error(f"Summarization failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "SmartConverge NLP Service",
        "version": "1.0.0",
        "status": "running",
        "gemini_available": GEMINI_AVAILABLE,
        "endpoints": [
            "/health",
            "/process/sync",
            "/models/sentiment/analyze",
            "/models/extract-tags",
            "/models/summarize"
        ] + (["/gemini/analyze", "/gemini/sentiment", "/gemini/summary", "/gemini/test"] if GEMINI_AVAILABLE else [])
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
