{"ast": null, "code": "import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement, Filler } from 'chart.js';\n\n// Register Chart.js components\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement, Filler);\n\n// Default chart options\nexport const defaultChartOptions = {\n  responsive: true,\n  maintainAspectRatio: false,\n  plugins: {\n    legend: {\n      position: 'top'\n    },\n    title: {\n      display: false\n    }\n  },\n  scales: {\n    y: {\n      beginAtZero: true\n    }\n  }\n};\n\n// Chart color palette\nexport const chartColors = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  error: '#d32f2f',\n  info: '#0288d1',\n  background: {\n    primary: 'rgba(25, 118, 210, 0.1)',\n    secondary: 'rgba(220, 0, 78, 0.1)',\n    success: 'rgba(46, 125, 50, 0.1)',\n    warning: 'rgba(237, 108, 2, 0.1)',\n    error: 'rgba(211, 47, 47, 0.1)',\n    info: 'rgba(2, 136, 209, 0.1)'\n  }\n};\n\n// Sentiment chart colors\nexport const sentimentColors = {\n  positive: '#4caf50',\n  neutral: '#ff9800',\n  negative: '#f44336',\n  veryPositive: '#2e7d32',\n  veryNegative: '#c62828'\n};\nexport default ChartJS;", "map": {"version": 3, "names": ["Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "Filler", "register", "defaultChartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "title", "display", "scales", "y", "beginAtZero", "chartColors", "primary", "secondary", "success", "warning", "error", "info", "background", "sentimentColors", "positive", "neutral", "negative", "veryPositive", "veryNegative"], "sources": ["C:/Users/<USER>/Documents/augment-projects/smartcoverage/frontend/src/utils/chartSetup.js"], "sourcesContent": ["import {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n  Filler\n} from 'chart.js';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n  Filler\n);\n\n// Default chart options\nexport const defaultChartOptions = {\n  responsive: true,\n  maintainAspectRatio: false,\n  plugins: {\n    legend: {\n      position: 'top',\n    },\n    title: {\n      display: false,\n    },\n  },\n  scales: {\n    y: {\n      beginAtZero: true,\n    },\n  },\n};\n\n// Chart color palette\nexport const chartColors = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  error: '#d32f2f',\n  info: '#0288d1',\n  background: {\n    primary: 'rgba(25, 118, 210, 0.1)',\n    secondary: 'rgba(220, 0, 78, 0.1)',\n    success: 'rgba(46, 125, 50, 0.1)',\n    warning: 'rgba(237, 108, 2, 0.1)',\n    error: 'rgba(211, 47, 47, 0.1)',\n    info: 'rgba(2, 136, 209, 0.1)',\n  }\n};\n\n// Sentiment chart colors\nexport const sentimentColors = {\n  positive: '#4caf50',\n  neutral: '#ff9800',\n  negative: '#f44336',\n  veryPositive: '#2e7d32',\n  veryNegative: '#c62828'\n};\n\nexport default ChartJS;\n"], "mappings": "AAAA,SACEA,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,MAAM,QACD,UAAU;;AAEjB;AACAV,OAAO,CAACW,QAAQ,CACdV,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,MACF,CAAC;;AAED;AACA,OAAO,MAAME,mBAAmB,GAAG;EACjCC,UAAU,EAAE,IAAI;EAChBC,mBAAmB,EAAE,KAAK;EAC1BC,OAAO,EAAE;IACPC,MAAM,EAAE;MACNC,QAAQ,EAAE;IACZ,CAAC;IACDC,KAAK,EAAE;MACLC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,MAAM,EAAE;IACNC,CAAC,EAAE;MACDC,WAAW,EAAE;IACf;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE;IACVN,OAAO,EAAE,yBAAyB;IAClCC,SAAS,EAAE,uBAAuB;IAClCC,OAAO,EAAE,wBAAwB;IACjCC,OAAO,EAAE,wBAAwB;IACjCC,KAAK,EAAE,wBAAwB;IAC/BC,IAAI,EAAE;EACR;AACF,CAAC;;AAED;AACA,OAAO,MAAME,eAAe,GAAG;EAC7BC,QAAQ,EAAE,SAAS;EACnBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,SAAS;EACnBC,YAAY,EAAE,SAAS;EACvBC,YAAY,EAAE;AAChB,CAAC;AAED,eAAepC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}