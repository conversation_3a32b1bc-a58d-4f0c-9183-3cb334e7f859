import api from './api';

class MeetingService {
  // Get all meetings
  async getMeetings(filters = {}) {
    try {
      const response = await api.get('/meetings', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get meetings:', error);
      throw error;
    }
  }

  // Get meetings by manager
  async getMeetingsByManager(managerId, filters = {}) {
    try {
      const response = await api.get(`/meetings/manager/${managerId}`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get meetings by manager:', error);
      throw error;
    }
  }

  // Get client meetings
  async getClientMeetings(clientEmail) {
    try {
      const response = await api.get(`/meetings/client/${clientEmail}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get client meetings:', error);
      throw error;
    }
  }

  // Get meeting by ID
  async getMeetingById(id) {
    try {
      const response = await api.get(`/meetings/${id}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting by ID:', error);
      throw error;
    }
  }

  // Create new meeting
  async createMeeting(meetingData) {
    try {
      const response = await api.post('/meetings', meetingData);
      return response.data;
    } catch (error) {
      console.error('Failed to create meeting:', error);
      throw error;
    }
  }

  // Update meeting
  async updateMeeting(id, updateData) {
    try {
      const response = await api.put(`/meetings/${id}`, updateData);
      return response.data;
    } catch (error) {
      console.error('Failed to update meeting:', error);
      throw error;
    }
  }

  // Delete meeting
  async deleteMeeting(id) {
    try {
      const response = await api.delete(`/meetings/${id}`);
      return response.data;
    } catch (error) {
      console.error('Failed to delete meeting:', error);
      throw error;
    }
  }

  // Process meeting transcript
  async processMeetingTranscript(id, transcript) {
    try {
      const response = await api.post(`/meetings/${id}/process`, { transcript });
      return response.data;
    } catch (error) {
      console.error('Failed to process meeting transcript:', error);
      throw error;
    }
  }

  // Get meeting analytics
  async getMeetingAnalytics(id) {
    try {
      const response = await api.get(`/meetings/${id}/analytics`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting analytics:', error);
      throw error;
    }
  }

  // Get meeting statistics
  async getStats() {
    try {
      const response = await api.get('/meetings/stats');
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting stats:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          totalMeetings: 0,
          completedMeetings: 0,
          averageSentiment: 0,
          totalClients: 0
        }
      };
    }
  }

  // Get chart data for analytics
  async getChartData(filters = {}) {
    try {
      const response = await api.get('/meetings/chart-data', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Failed to get chart data:', error);
      // Return fallback data
      return {
        success: true,
        data: {
          meetingTrends: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            data: [8, 12, 15, 10]
          },
          sentimentDistribution: [60, 30, 10],
          clientActivity: {
            labels: ['Client A', 'Client B', 'Client C'],
            data: [5, 8, 3]
          }
        }
      };
    }
  }

  // Upload meeting recording
  async uploadRecording(id, file) {
    try {
      const formData = new FormData();
      formData.append('recording', file);
      
      const response = await api.post(`/meetings/${id}/recording`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to upload recording:', error);
      throw error;
    }
  }

  // Get meeting participants
  async getMeetingParticipants(id) {
    try {
      const response = await api.get(`/meetings/${id}/participants`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting participants:', error);
      throw error;
    }
  }

  // Add meeting participant
  async addParticipant(id, participantData) {
    try {
      const response = await api.post(`/meetings/${id}/participants`, participantData);
      return response.data;
    } catch (error) {
      console.error('Failed to add participant:', error);
      throw error;
    }
  }

  // Remove meeting participant
  async removeParticipant(id, participantId) {
    try {
      const response = await api.delete(`/meetings/${id}/participants/${participantId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to remove participant:', error);
      throw error;
    }
  }

  // Get meeting tags
  async getMeetingTags(id) {
    try {
      const response = await api.get(`/meetings/${id}/tags`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting tags:', error);
      throw error;
    }
  }

  // Add meeting tag
  async addMeetingTag(id, tag) {
    try {
      const response = await api.post(`/meetings/${id}/tags`, { tag });
      return response.data;
    } catch (error) {
      console.error('Failed to add meeting tag:', error);
      throw error;
    }
  }

  // Remove meeting tag
  async removeMeetingTag(id, tagId) {
    try {
      const response = await api.delete(`/meetings/${id}/tags/${tagId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to remove meeting tag:', error);
      throw error;
    }
  }

  // Search meetings
  async searchMeetings(query, filters = {}) {
    try {
      const response = await api.get('/meetings/search', {
        params: { q: query, ...filters }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to search meetings:', error);
      throw error;
    }
  }

  // Export meetings
  async exportMeetings(format = 'csv', filters = {}) {
    try {
      const response = await api.get('/meetings/export', {
        params: { format, ...filters },
        responseType: 'blob'
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `meetings-export.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return { success: true };
    } catch (error) {
      console.error('Failed to export meetings:', error);
      throw error;
    }
  }

  // Get meeting summary
  async getMeetingSummary(id) {
    try {
      const response = await api.get(`/meetings/${id}/summary`);
      return response.data;
    } catch (error) {
      console.error('Failed to get meeting summary:', error);
      throw error;
    }
  }

  // Generate meeting report
  async generateMeetingReport(id, reportType = 'detailed') {
    try {
      const response = await api.post(`/meetings/${id}/report`, { type: reportType });
      return response.data;
    } catch (error) {
      console.error('Failed to generate meeting report:', error);
      throw error;
    }
  }

  // Get recent meetings
  async getRecentMeetings(limit = 10) {
    try {
      const response = await api.get('/meetings/recent', { params: { limit } });
      return response.data;
    } catch (error) {
      console.error('Failed to get recent meetings:', error);
      // Return fallback data
      return {
        success: true,
        data: []
      };
    }
  }

  // Get upcoming meetings
  async getUpcomingMeetings(limit = 10) {
    try {
      const response = await api.get('/meetings/upcoming', { params: { limit } });
      return response.data;
    } catch (error) {
      console.error('Failed to get upcoming meetings:', error);
      // Return fallback data
      return {
        success: true,
        data: []
      };
    }
  }
}

export const meetingService = new MeetingService();
export default meetingService;
