import React from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography, Card, CardContent } from '@mui/material';

const ClientDetailPage = () => {
  const { id } = useParams();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Client Details - {id}
      </Typography>
      <Card>
        <CardContent>
          <Typography variant="body1">
            Detailed client view will be implemented here.
            This will show client information, meeting history, and analytics.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ClientDetailPage;
