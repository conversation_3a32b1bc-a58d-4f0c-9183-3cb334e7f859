const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'SmartConverge API',
      version: '1.0.0',
      description: 'Intelligent Meeting Analysis System API Documentation',
      contact: {
        name: 'SmartConverge Team',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: `http://localhost:${process.env.PORT || 3001}/api`,
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
      schemas: {
        Meeting: {
          type: 'object',
          required: ['subject', 'notes', 'organizer_name', 'client_name', 'meeting_date'],
          properties: {
            id: {
              type: 'integer',
              description: 'Unique meeting identifier',
            },
            subject: {
              type: 'string',
              description: 'Meeting subject/title',
            },
            notes: {
              type: 'string',
              description: 'Meeting notes and discussion points',
            },
            organizer_name: {
              type: 'string',
              description: 'Name of the meeting organizer',
            },
            organizer_department: {
              type: 'string',
              description: 'Department of the organizer',
            },
            client_name: {
              type: 'string',
              description: 'Client company name',
            },
            client_industry: {
              type: 'string',
              description: 'Client industry sector',
            },
            meeting_date: {
              type: 'string',
              format: 'date-time',
              description: 'Meeting date and time',
            },
            manual_tags: {
              type: 'array',
              items: {
                type: 'string',
              },
              description: 'Manually assigned tags',
            },
            attendees: {
              type: 'array',
              items: {
                type: 'string',
              },
              description: 'List of meeting attendees',
            },
            key_decisions: {
              type: 'string',
              description: 'Key decisions made during the meeting',
            },
            investment_amount: {
              type: 'number',
              description: 'Investment amount discussed',
            },
            roi_expectation: {
              type: 'string',
              description: 'Expected ROI percentage range',
            },
            summary: {
              type: 'string',
              description: 'AI-generated meeting summary',
            },
          },
        },
        Tag: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: 'Tag identifier',
            },
            name: {
              type: 'string',
              description: 'Tag name',
            },
            confidence_score: {
              type: 'number',
              minimum: 0,
              maximum: 1,
              description: 'AI confidence score for the tag',
            },
            tag_type: {
              type: 'string',
              enum: ['explicit', 'implicit'],
              description: 'Type of tag extraction',
            },
            meeting_id: {
              type: 'integer',
              description: 'Associated meeting ID',
            },
          },
        },
        Client: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: 'Client identifier',
            },
            name: {
              type: 'string',
              description: 'Client company name',
            },
            industry: {
              type: 'string',
              description: 'Client industry',
            },
            contact_email: {
              type: 'string',
              format: 'email',
              description: 'Primary contact email',
            },
            billing_rate: {
              type: 'number',
              description: 'Hourly billing rate',
            },
          },
        },
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'string',
              description: 'Error type',
            },
            message: {
              type: 'string',
              description: 'Error message',
            },
            details: {
              type: 'object',
              description: 'Additional error details',
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./src/routes/*.js'], // Path to the API files
};

const specs = swaggerJsdoc(options);

module.exports = specs;
